package com.example.demo.controller;

import com.example.demo.dto.ApiResponse;
import com.example.demo.exception.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 异常处理测试Controller
 * 用于测试全局异常处理机制
 */
@Slf4j
@RestController
@RequestMapping("/api/test/exception")
@Tag(name = "异常处理测试", description = "用于测试全局异常处理机制的API")
public class ExceptionTestController extends BaseController {

    @GetMapping("/business")
    @Operation(summary = "测试业务异常", description = "抛出业务异常测试全局异常处理")
    public ApiResponse<String> testBusinessException(
            @Parameter(description = "错误类型") @RequestParam(defaultValue = "user_not_found") String errorType) {
        
        switch (errorType) {
            case "user_not_found":
                throw new UserNotFoundException(123L);
            case "project_not_found":
                throw new ProjectNotFoundException(456L);
            case "permission_denied":
                throw new InsufficientPermissionException("查看用户信息", "用户ID:123");
            case "payment_failed":
                throw new PaymentException("支付网关连接超时");
            case "validation_error":
                throw new ValidationException("用户名", "长度必须在3-20位之间");
            default:
                throw new BusinessException(ErrorCode.BUSINESS_ERROR, "未知的业务错误类型");
        }
    }

    @GetMapping("/system")
    @Operation(summary = "测试系统异常", description = "抛出系统异常测试全局异常处理")
    public ApiResponse<String> testSystemException(
            @Parameter(description = "异常类型") @RequestParam(defaultValue = "runtime") String exceptionType) throws Exception {
        
        switch (exceptionType) {
            case "runtime":
                throw new RuntimeException("这是一个运行时异常");
            case "null_pointer":
                String str = null;
                return success(str.length() + ""); // 故意触发空指针异常
            case "array_index":
                int[] arr = new int[3];
                return success(arr[10] + ""); // 故意触发数组越界异常
            case "arithmetic":
                int result = 10 / 0; // 故意触发算术异常
                return success(result + "");
            default:
                throw new Exception("这是一个检查异常");
        }
    }

    @PostMapping("/validation")
    @Operation(summary = "测试参数验证异常", description = "测试参数验证异常处理")
    public ApiResponse<String> testValidationException(@Valid @RequestBody TestRequest request) {
        return success("参数验证通过：" + request.getName());
    }

    @GetMapping("/missing-param")
    @Operation(summary = "测试缺少参数异常", description = "测试缺少必要参数的异常处理")
    public ApiResponse<String> testMissingParam(
            @Parameter(description = "必要参数") @RequestParam @NotNull String requiredParam) {
        return success("参数值：" + requiredParam);
    }

    @GetMapping("/type-mismatch")
    @Operation(summary = "测试参数类型不匹配异常", description = "测试参数类型不匹配的异常处理")
    public ApiResponse<String> testTypeMismatch(
            @Parameter(description = "数字参数") @RequestParam Integer numberParam) {
        return success("数字参数值：" + numberParam);
    }

    @PostMapping("/json-parse")
    @Operation(summary = "测试JSON解析异常", description = "发送格式错误的JSON测试解析异常")
    public ApiResponse<String> testJsonParseException(@RequestBody Object data) {
        return success("JSON解析成功");
    }

    @PutMapping("/method-not-allowed")
    @Operation(summary = "测试请求方法不支持", description = "使用错误的HTTP方法访问此接口")
    public ApiResponse<String> testMethodNotAllowed() {
        return success("PUT方法调用成功");
    }

    /**
     * 测试请求DTO
     */
    public static class TestRequest {
        @NotBlank(message = "姓名不能为空")
        private String name;

        @NotNull(message = "年龄不能为空")
        private Integer age;

        // Getters and Setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getAge() {
            return age;
        }

        public void setAge(Integer age) {
            this.age = age;
        }
    }
}
