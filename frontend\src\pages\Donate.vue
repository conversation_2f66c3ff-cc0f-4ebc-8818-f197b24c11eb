<template>
  <div class="donate-page">
    <div class="container">
      <div class="donate-container">
        <div class="donate-header">
          <h1>爱心捐赠</h1>
          <p>您的每一份爱心都将温暖需要帮助的人</p>
        </div>

        <el-row :gutter="20">
          <el-col :span="16">
            <el-form ref="donateFormRef" :model="donateForm" :rules="donateRules" label-width="100px">
              <!-- 项目选择 -->
              <el-form-item label="捐赠项目" prop="projectId">
                <el-select v-model="donateForm.projectId" placeholder="请选择要捐赠的项目" style="width: 100%">
                  <el-option
                    v-for="project in projects"
                    :key="project.id"
                    :label="project.title"
                    :value="project.id"
                  />
                </el-select>
              </el-form-item>

              <!-- 捐赠金额 -->
              <el-form-item label="捐赠金额" prop="amount">
                <div class="amount-section">
                  <div class="quick-amounts">
                    <el-button
                      v-for="amount in quickAmounts"
                      :key="amount"
                      :type="donateForm.amount === amount ? 'primary' : 'default'"
                      @click="donateForm.amount = amount"
                    >
                      ¥{{ amount }}
                    </el-button>
                  </div>
                  <el-input
                    v-model.number="donateForm.amount"
                    placeholder="请输入捐赠金额"
                    type="number"
                    :min="1"
                  >
                    <template #prepend>¥</template>
                  </el-input>
                </div>
              </el-form-item>

              <!-- 捐赠留言 -->
              <el-form-item label="捐赠留言">
                <el-input
                  v-model="donateForm.message"
                  type="textarea"
                  :rows="4"
                  placeholder="留下您的爱心寄语（可选）"
                  maxlength="200"
                  show-word-limit
                />
              </el-form-item>

              <!-- 是否匿名 -->
              <el-form-item label="捐赠方式">
                <el-radio-group v-model="donateForm.anonymous">
                  <el-radio :label="false">实名捐赠</el-radio>
                  <el-radio :label="true">匿名捐赠</el-radio>
                </el-radio-group>
              </el-form-item>

              <!-- 支付方式 -->
              <el-form-item label="支付方式" prop="paymentMethod">
                <el-radio-group v-model="donateForm.paymentMethod">
                  <el-radio label="alipay">
                    <el-icon><Money /></el-icon>
                    支付宝
                  </el-radio>
                  <el-radio label="wechat">
                    <el-icon><ChatDotRound /></el-icon>
                    微信支付
                  </el-radio>
                  <el-radio label="bank">
                    <el-icon><CreditCard /></el-icon>
                    银行卡
                  </el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" size="large" @click="handleDonate" :loading="loading">
                  确认捐赠
                </el-button>
                <el-button size="large" @click="$router.go(-1)">
                  返回
                </el-button>
              </el-form-item>
            </el-form>
          </el-col>

          <el-col :span="8">
            <div class="donate-summary">
              <h3>捐赠摘要</h3>
              <div class="summary-item">
                <span>捐赠金额：</span>
                <span class="amount">¥{{ donateForm.amount || 0 }}</span>
              </div>
              <div class="summary-item">
                <span>手续费：</span>
                <span>¥0</span>
              </div>
              <div class="summary-total">
                <span>总计：</span>
                <span class="total-amount">¥{{ donateForm.amount || 0 }}</span>
              </div>
              
              <div class="donate-tips">
                <h4>温馨提示</h4>
                <ul>
                  <li>您的捐赠将直接用于帮助需要的人</li>
                  <li>我们承诺资金使用透明公开</li>
                  <li>捐赠完成后将为您开具电子收据</li>
                  <li>如有疑问请联系客服</li>
                </ul>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getProjects } from '@/api/project'

const route = useRoute()
const router = useRouter()
const donateFormRef = ref()
const loading = ref(false)
const projects = ref([])

const quickAmounts = [50, 100, 200, 500, 1000, 2000]

const donateForm = reactive({
  projectId: route.query.projectId || '',
  amount: '',
  message: '',
  anonymous: false,
  paymentMethod: 'alipay'
})

const donateRules = {
  projectId: [
    { required: true, message: '请选择捐赠项目', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入捐赠金额', trigger: 'blur' },
    { type: 'number', min: 1, message: '捐赠金额不能少于1元', trigger: 'blur' }
  ],
  paymentMethod: [
    { required: true, message: '请选择支付方式', trigger: 'change' }
  ]
}

const fetchProjects = async () => {
  try {
    const data = await getProjects({ status: 'active', size: 100 })
    projects.value = data.records || []
  } catch (error) {
    console.error('获取项目列表失败:', error)
  }
}

const handleDonate = async () => {
  try {
    await donateFormRef.value.validate()
    loading.value = true
    
    // 这里应该调用捐赠API
    // const result = await createDonation(donateForm)
    
    // 模拟支付流程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('捐赠成功，感谢您的爱心！')
    router.push('/profile')
  } catch (error) {
    console.error('捐赠失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchProjects()
})
</script>

<style scoped>
.donate-page {
  min-height: calc(100vh - 120px);
  padding: 40px 0;
  background: #f5f5f5;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.donate-container {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.donate-header {
  text-align: center;
  margin-bottom: 40px;
}

.donate-header h1 {
  font-size: 28px;
  color: #2c3e50;
  margin-bottom: 12px;
}

.donate-header p {
  color: #7f8c8d;
  font-size: 16px;
}

.amount-section {
  width: 100%;
}

.quick-amounts {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.donate-summary {
  background: #f8f9fa;
  padding: 24px;
  border-radius: 8px;
  position: sticky;
  top: 20px;
}

.donate-summary h3 {
  color: #2c3e50;
  margin-bottom: 20px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  color: #7f8c8d;
}

.summary-total {
  display: flex;
  justify-content: space-between;
  padding-top: 12px;
  border-top: 1px solid #eee;
  font-weight: bold;
  color: #2c3e50;
}

.amount, .total-amount {
  color: #e74c3c;
  font-weight: bold;
}

.donate-tips {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #eee;
}

.donate-tips h4 {
  color: #2c3e50;
  margin-bottom: 12px;
}

.donate-tips ul {
  list-style: none;
  padding: 0;
}

.donate-tips li {
  color: #7f8c8d;
  font-size: 14px;
  margin-bottom: 8px;
  position: relative;
  padding-left: 16px;
}

.donate-tips li::before {
  content: '•';
  color: #e74c3c;
  position: absolute;
  left: 0;
}
</style>
