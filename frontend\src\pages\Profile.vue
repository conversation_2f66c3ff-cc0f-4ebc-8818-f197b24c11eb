<template>
  <div class="profile-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <!-- 返回首页按钮 -->
        <div class="header-nav">
          <el-button
            @click="$router.push('/')"
            type="primary"
            plain
            size="large"
            class="back-home-btn"
          >
            <el-icon>
              <ArrowLeft />
            </el-icon>
            返回首页
          </el-button>
        </div>

        <div class="header-content">
          <div class="user-avatar">
            <el-avatar :size="80" :src="userInfo.avatar">
              {{ userInfo.username?.charAt(0)?.toUpperCase() }}
            </el-avatar>
          </div>
          <div class="user-details">
            <h1>{{ userInfo.realName || userInfo.username }}</h1>
            <p class="user-email">{{ userInfo.email }}</p>
            <div class="user-stats">
              <div class="stat-item">
                <span class="stat-value">{{ userStats.donationCount || 0 }}</span>
                <span class="stat-label">次捐赠</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">¥{{ formatAmount(userStats.donationAmount || 0) }}</span>
                <span class="stat-label">捐赠总额</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ userStats.helpRequestCount || 0 }}</span>
                <span class="stat-label">次申请</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="profile-main">
        <!-- 侧边栏 -->
        <div class="profile-sidebar">
          <el-menu :default-active="activeTab" @select="handleTabChange" class="sidebar-menu">
            <!-- 基础功能 - 所有用户都有 -->
            <el-menu-item index="info">
              <el-icon>
                <User />
              </el-icon>
              <span>个人信息</span>
            </el-menu-item>

            <!-- 普通用户功能 -->
            <el-menu-item index="donations">
              <el-icon>
                <Money />
              </el-icon>
              <span>我的捐赠</span>
            </el-menu-item>
            <el-menu-item index="requests">
              <el-icon>
                <Document />
              </el-icon>
              <span>我的申请</span>
            </el-menu-item>

            <!-- 管理员专属功能 -->
            <template v-if="userStore.isAdmin">
              <el-divider>管理功能</el-divider>
 
              <el-menu-item index="quick-actions">
                <el-icon>
                  <Operation />
                </el-icon>
                <span>快捷操作</span>
              </el-menu-item>
              <el-menu-item index="admin-panel" @click="goToAdminPanel">
                <el-icon>
                  <Setting />
                </el-icon>
                <span>管理后台</span>
              </el-menu-item>
            </template>

            <!-- 安全设置 - 所有用户都有 -->
            <el-divider>账户设置</el-divider>
            <el-menu-item index="security">
              <el-icon>
                <Lock />
              </el-icon>
              <span>安全设置</span>
            </el-menu-item>
            <!-- 通知设置暂时隐藏 -->
            <!-- <el-menu-item index="notifications">
              <el-icon>
                <Bell />
              </el-icon>
              <span>通知设置</span>
            </el-menu-item> -->
          </el-menu>
        </div>

        <!-- 主内容 -->
        <div class="profile-content">
          <!-- 个人信息 -->
          <div v-if="activeTab === 'info'" class="tab-content">
            <div class="content-header">
              <h2>个人信息</h2>
              <p>管理您的个人资料和账户信息</p>
            </div>

            <div class="info-form-card">
              <el-form :model="userForm" :rules="formRules" ref="userFormRef" label-width="100px" v-loading="updating">
                <el-form-item label="用户名">
                  <el-input v-model="userForm.username" disabled>
                    <template #suffix>
                      <el-tooltip content="用户名不可修改" placement="top">
                        <el-icon>
                          <InfoFilled />
                        </el-icon>
                      </el-tooltip>
                    </template>
                  </el-input>
                </el-form-item>

                <el-form-item label="真实姓名" prop="realName">
                  <el-input v-model="userForm.realName" placeholder="请输入真实姓名" clearable />
                </el-form-item>

                <el-form-item label="邮箱地址" prop="email">
                  <el-input v-model="userForm.email" type="email" placeholder="请输入邮箱地址" clearable />
                </el-form-item>

                <el-form-item label="手机号码" prop="phone">
                  <el-input v-model="userForm.phone" placeholder="请输入手机号码" clearable />
                </el-form-item>

                <el-form-item label="头像">
                  <div class="avatar-upload" @click="triggerFileUpload">
                    <el-avatar :size="60" :src="userForm.avatar">
                      {{ userForm.username?.charAt(0)?.toUpperCase() }}
                    </el-avatar>
                    <div class="upload-text">
                      <p>点击上传头像</p>
                      <p class="upload-tip">支持 JPG、PNG 格式，文件大小不超过 2MB</p>
                    </div>
                    <!-- 隐藏的文件输入框 -->
                    <input ref="fileInputRef" type="file" accept="image/jpeg,image/jpg,image/png" style="display: none"
                      @change="handleFileSelect" />
                  </div>
                </el-form-item>

                <el-form-item>
                  <el-button type="primary" @click="handleUpdateProfile" :loading="updating">
                    保存修改
                  </el-button>
                  <el-button @click="resetForm">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>

          <!-- 我的捐赠 -->
          <div v-if="activeTab === 'donations'" class="tab-content">
            <div class="content-header">
              <h2>我的捐赠</h2>
              <p>查看您的所有捐赠记录和爱心足迹</p>
            </div>

            <div class="donations-summary">
              <div class="summary-card">
                <div class="summary-icon">
                  <el-icon>
                    <Money />
                  </el-icon>
                </div>
                <div class="summary-info">
                  <div class="summary-value">¥{{ formatAmount(userStats.donationAmount || 0) }}</div>
                  <div class="summary-label">累计捐赠</div>
                </div>
              </div>
              <div class="summary-card">
                <div class="summary-icon">
                  <el-icon>
                    <Trophy />
                  </el-icon>
                </div>
                <div class="summary-info">
                  <div class="summary-value">{{ userStats.donationCount || 0 }}</div>
                  <div class="summary-label">捐赠次数</div>
                </div>
              </div>
            </div>

            <div class="donations-table-card">
              <div class="table-header">
                <div class="table-title">捐赠记录</div>
                <div class="table-actions">
                  <el-button @click="loadMyDonations" :loading="loadingDonations" size="small">
                    <el-icon>
                      <Refresh />
                    </el-icon>
                    刷新
                  </el-button>
          
                </div>
              </div>

              <el-table :data="donations" v-loading="loadingDonations" empty-text="暂无捐赠记录">
                <el-table-column prop="projectTitle" label="项目名称" min-width="200">
                  <template #default="scope">
                    <div class="project-info">
                      <div class="project-title">{{ scope.row.projectTitle }}</div>
                      <div class="project-category">{{ scope.row.projectCategory }}</div>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column prop="amount" label="捐赠金额" width="120">
                  <template #default="scope">
                    <span class="amount-text">¥{{ formatAmount(scope.row.amount) }}</span>
                  </template>
                </el-table-column>

                <el-table-column prop="donationTime" label="捐赠时间" width="180">
                  <template #default="scope">
                    {{ formatDate(scope.row.donationTime) }}
                  </template>
                </el-table-column>

                <el-table-column prop="status" label="状态" width="100">
                  <template #default="scope">
                    <el-tag :type="getStatusType(scope.row.status)" size="small">
                      {{ scope.row.status === 'success' ? '成功' : '处理中' }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

          <!-- 我的申请 -->
          <div v-if="activeTab === 'requests'" class="tab-content">
            <div class="content-header">
              <h2>我的申请</h2>
              <p>查看您提交的救助申请和审核状态</p>
            </div>

            <div class="requests-table-card">
              <div class="table-header">
                <div class="table-title">申请记录</div>
                <div class="table-actions">
                  <el-button @click="loadMyRequests" :loading="loadingRequests" size="small">
                    <el-icon>
                      <Refresh />
                    </el-icon>
                    刷新
                  </el-button>
              
                  <el-button type="primary" @click="$router.push('/help-request/create')">
                    <el-icon>
                      <Plus />
                    </el-icon>
                    新建申请
                  </el-button>
                </div>
              </div>

              <el-table :data="requests" v-loading="loadingRequests" empty-text="暂无申请记录">
                <el-table-column prop="title" label="申请标题" min-width="200">
                  <template #default="scope">
                    <div class="request-title">{{ scope.row.title }}</div>
                  </template>
                </el-table-column>

                <el-table-column prop="amountNeeded" label="申请金额" width="120">
                  <template #default="scope">
                    <span class="amount-text">¥{{ formatAmount(scope.row.amountNeeded) }}</span>
                  </template>
                </el-table-column>

                <el-table-column prop="status" label="审核状态" width="120">
                  <template #default="scope">
                    <el-tag :type="getRequestStatusType(scope.row.status)" size="small">
                      {{ getRequestStatusText(scope.row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="createTime" label="申请时间" width="180">
                  <template #default="scope">
                    {{ formatDate(scope.row.createTime) }}
                  </template>
                </el-table-column>

                <el-table-column label="操作" width="120">
                  <template #default="scope">
                    <el-button type="primary" size="small" text @click="viewRequest(scope.row)">
                      查看详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

          <!-- 安全设置 -->
          <div v-if="activeTab === 'security'" class="tab-content">
            <div class="content-header">
              <h2>安全设置</h2>
              <p>管理您的账户安全和密码设置</p>
            </div>

            <div class="security-form-card">
              <h3>修改密码</h3>
              <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-width="100px"
                v-loading="changingPassword">
                <el-form-item label="当前密码" prop="oldPassword">
                  <el-input v-model="passwordForm.oldPassword" type="password" show-password placeholder="请输入当前密码" />
                </el-form-item>

                <el-form-item label="新密码" prop="newPassword">
                  <el-input v-model="passwordForm.newPassword" type="password" show-password placeholder="请输入新密码" />
                </el-form-item>

                <el-form-item label="确认密码" prop="confirmPassword">
                  <el-input v-model="passwordForm.confirmPassword" type="password" show-password
                    placeholder="请再次输入新密码" />
                </el-form-item>

                <el-form-item>
                  <el-button type="primary" @click="handleChangePassword" :loading="changingPassword">
                    修改密码
                  </el-button>
                  <el-button @click="resetPasswordForm">重置</el-button>
                </el-form-item>
              </el-form>
            </div>

            <div class="security-info-card">
              <h3>账户安全</h3>
              <div class="security-item">
                <div class="security-label">登录状态</div>
                <div class="security-value">已登录</div>
              </div>
              <div class="security-item">
                <div class="security-label">最后登录时间</div>
                <div class="security-value">{{ formatDate(userInfo.lastLoginTime) || '暂无记录' }}</div>
              </div>
              <div class="security-item">
                <div class="security-label">账户创建时间</div>
                <div class="security-value">{{ formatDate(userInfo.createTime) || '暂无记录' }}</div>
              </div>
            </div>
          </div>

          <!-- 管理统计 - 仅管理员可见 -->
          <div v-if="activeTab === 'admin-stats' && userStore.isAdmin" class="tab-content">
            <div class="content-header">
              <h2>管理统计</h2>
              <p>查看平台整体运营数据和统计信息</p>
            </div>

            <div class="admin-stats-grid">
              <div class="stat-card">
                <div class="stat-icon">
                  <el-icon>
                    <User />
                  </el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ adminStats.totalUsers || 0 }}</div>
                  <div class="stat-label">总用户数</div>
                </div>
              </div>

              <div class="stat-card">
                <div class="stat-icon">
                  <el-icon>
                    <Document />
                  </el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ adminStats.totalProjects || 0 }}</div>
                  <div class="stat-label">总项目数</div>
                </div>
              </div>

              <div class="stat-card">
                <div class="stat-icon">
                  <el-icon>
                    <Money />
                  </el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">¥{{ formatAmount(adminStats.totalDonations || 0) }}</div>
                  <div class="stat-label">总捐赠额</div>
                </div>
              </div>

              <div class="stat-card">
                <div class="stat-icon">
                  <el-icon>
                    <Bell />
                  </el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ adminStats.pendingRequests || 0 }}</div>
                  <div class="stat-label">待审核申请</div>
                </div>
              </div>
            </div>

            <div class="admin-charts">
              <div class="chart-card">
                <h3>最近7天捐赠趋势</h3>
                <div class="chart-placeholder">
                  <el-empty description="图表功能开发中..." />
                </div>
              </div>
            </div>
          </div>

          <!-- 快捷操作 - 仅管理员可见 -->
          <div v-if="activeTab === 'quick-actions' && userStore.isAdmin" class="tab-content">
            <div class="content-header">
              <h2>快捷操作</h2>
              <p>常用的管理操作快捷入口</p>
            </div>

            <div class="quick-actions-grid">
              <div class="action-card" @click="$router.push('/admin/projects/create')">
                <div class="action-icon">
                  <el-icon>
                    <Plus />
                  </el-icon>
                </div>
                <div class="action-content">
                  <div class="action-title">创建项目</div>
                  <div class="action-desc">快速创建新的救助项目</div>
                </div>
              </div>

              <div class="action-card" @click="$router.push('/admin/help-requests')">
                <div class="action-icon">
                  <el-icon>
                    <Document />
                  </el-icon>
                </div>
                <div class="action-content">
                  <div class="action-title">审核申请</div>
                  <div class="action-desc">处理待审核的救助申请</div>
                </div>
              </div>

              <div class="action-card" @click="$router.push('/admin/users')">
                <div class="action-icon">
                  <el-icon>
                    <User />
                  </el-icon>
                </div>
                <div class="action-content">
                  <div class="action-title">用户管理</div>
                  <div class="action-desc">管理平台用户账户</div>
                </div>
              </div>

              <div class="action-card" @click="$router.push('/admin/donations')">
                <div class="action-icon">
                  <el-icon>
                    <Money />
                  </el-icon>
                </div>
                <div class="action-content">
                  <div class="action-title">捐赠管理</div>
                  <div class="action-desc">查看和管理捐赠记录</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 通知设置 - 暂时隐藏 -->
          <!-- <div v-if="activeTab === 'notifications'" class="tab-content">
            <div class="content-header">
              <h2>通知设置</h2>
              <p>管理您的通知偏好设置</p>
            </div>

            <div class="notification-settings-card">
              <div class="setting-group">
                <h3>邮件通知</h3>
                <div class="setting-item">
                  <div class="setting-info">
                    <div class="setting-title">捐赠成功通知</div>
                    <div class="setting-desc">当您的捐赠成功时发送邮件通知</div>
                  </div>
                  <el-switch v-model="notificationSettings.emailDonation" />
                </div>
                <div class="setting-item">
                  <div class="setting-info">
                    <div class="setting-title">申请状态更新</div>
                    <div class="setting-desc">当您的申请状态发生变化时发送邮件通知</div>
                  </div>
                  <el-switch v-model="notificationSettings.emailRequest" />
                </div>
              </div>

              <div class="setting-group">
                <h3>系统通知</h3>
                <div class="setting-item">
                  <div class="setting-info">
                    <div class="setting-title">项目推荐</div>
                    <div class="setting-desc">接收个性化的项目推荐通知</div>
                  </div>
                  <el-switch v-model="notificationSettings.systemRecommend" />
                </div>
                <div class="setting-item">
                  <div class="setting-info">
                    <div class="setting-title">活动通知</div>
                    <div class="setting-desc">接收平台活动和公告通知</div>
                  </div>
                  <el-switch v-model="notificationSettings.systemActivity" />
                </div>
              </div>

              <div class="setting-actions">
                <el-button type="primary" @click="saveNotificationSettings">
                  保存设置
                </el-button>
              </div>
            </div>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted, watch } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage } from 'element-plus'
  import { useUserStore } from '@/stores/user'
  import { getUserInfo, updateUserInfo, changePassword } from '@/api/user'
  import { uploadAvatar } from '@/api/file'
  import { getMyDonations, getUserDonationStats } from '@/api/donation'
  import { getMyHelpRequests } from '@/api/helpRequest'
  import {
    User,
    Money,
    Document,
    Lock,
    Bell,
    Plus,
    DataAnalysis,
    Operation,
    Setting,
    Refresh,
    ArrowLeft
  } from '@element-plus/icons-vue'

  const router = useRouter()
  const userStore = useUserStore()

  // 响应式数据
  const loading = ref(false)
  const updating = ref(false)
  const changingPassword = ref(false)
  const loadingDonations = ref(false)
  const loadingRequests = ref(false)

  const activeTab = ref('info')
  const userFormRef = ref()
  const passwordFormRef = ref()
  const fileInputRef = ref()

  // 用户信息
  const userInfo = ref({})
  const userStats = ref({})

  // 表单数据
  const userForm = reactive({
    username: '',
    email: '',
    phone: '',
    realName: '',
    avatar: '',
    avatarPath: '' // 存储相对路径
  })

  const passwordForm = reactive({
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  })

  // 数据列表
  const donations = ref([])
  const requests = ref([])

  // 管理员数据
  const adminStats = ref({
    totalUsers: 0,
    totalProjects: 0,
    totalDonations: 0,
    pendingRequests: 0
  })

  // 通知设置
  const notificationSettings = reactive({
    emailDonation: true,
    emailRequest: true,
    systemRecommend: false,
    systemActivity: true
  })

  // 表单验证规则
  const formRules = {
    email: [
      { required: true, message: '请输入邮箱地址', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    phone: [
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ]
  }

  const passwordRules = {
    oldPassword: [
      { required: true, message: '请输入当前密码', trigger: 'blur' }
    ],
    newPassword: [
      { required: true, message: '请输入新密码', trigger: 'blur' },
      { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
    ],
    confirmPassword: [
      { required: true, message: '请确认新密码', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value !== passwordForm.newPassword) {
            callback(new Error('两次输入的密码不一致'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  }

  // 加载用户统计数据
  const loadUserStats = async () => {
    try {
      console.log('开始加载用户统计数据...')

      // 调用后端API获取用户捐赠统计
      const stats = await getUserDonationStats(userStore.user.id)

      console.log('用户统计数据响应:', stats)

      if (stats) {
        userStats.value = {
          donationCount: stats.donationCount || 0,
          donationAmount: stats.totalAmount || 0,
          helpRequestCount: 0 // 暂时设为0，后续可以添加救助申请统计API
        }
        console.log('用户统计数据已更新:', userStats.value)
      } else {
        console.warn('获取用户统计数据失败')
        // 使用默认值
        userStats.value = {
          donationCount: 0,
          donationAmount: 0,
          helpRequestCount: 0
        }
      }
    } catch (error) {
      console.error('加载用户统计数据失败:', error)
      // 使用默认值
      userStats.value = {
        donationCount: 0,
        donationAmount: 0,
        helpRequestCount: 0
      }
    }
  }

  // 加载用户信息
  const loadUserInfo = async () => {
    try {
      loading.value = true
      console.log('开始加载用户信息...')
      console.log('当前用户角色:', userStore.user?.role)

      const data = await getUserInfo()
      console.log('获取到的用户信息:', data)
      userInfo.value = data

      // 填充表单
      Object.assign(userForm, {
        username: data.username,
        email: data.email,
        phone: data.phone,
        realName: data.realName,
        avatar: data.avatar
      })

      console.log('表单数据已填充:', userForm)

      // 加载真实的用户统计数据
      await loadUserStats()
    } catch (error) {
      console.error('加载用户信息失败:', error)
      console.error('错误详情:', error.response?.data || error.message)
      ElMessage.error('加载用户信息失败: ' + (error.response?.data?.message || error.message))
    } finally {
      loading.value = false
    }
  }

  // 处理标签页切换
  const handleTabChange = (key) => {
    activeTab.value = key
  }

  // 更新用户信息
  const handleUpdateProfile = async () => {
    try {
      await userFormRef.value.validate()
      updating.value = true

      const updateData = {
        email: userForm.email,
        phone: userForm.phone,
        realName: userForm.realName,
        avatar: userForm.avatarPath || userForm.avatar // 优先使用相对路径
      }

      console.log('准备更新用户信息:', updateData)
      console.log('当前用户角色:', userStore.user?.role)

      await updateUserInfo(updateData)
      ElMessage.success('个人信息更新成功')

      // 重新加载用户信息
      await loadUserInfo()
    } catch (error) {
      console.error('更新用户信息失败:', error)
      console.error('错误详情:', error.response?.data || error.message)
      ElMessage.error('更新失败: ' + (error.response?.data?.message || error.message))
    } finally {
      updating.value = false
    }
  }

  // 重置表单
  const resetForm = () => {
    Object.assign(userForm, {
      username: userInfo.value.username,
      email: userInfo.value.email,
      phone: userInfo.value.phone,
      realName: userInfo.value.realName,
      avatar: userInfo.value.avatar,
      avatarPath: ''
    })
  }

  // 触发文件选择
  const triggerFileUpload = () => {
    fileInputRef.value?.click()
  }

  // 处理文件选择
  const handleFileSelect = (event) => {
    const file = event.target.files[0]
    if (!file) return

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png']
    if (!allowedTypes.includes(file.type)) {
      ElMessage.error('只支持 JPG、PNG 格式的图片')
      return
    }

    // 验证文件大小 (2MB)
    const maxSize = 2 * 1024 * 1024
    if (file.size > maxSize) {
      ElMessage.error('图片大小不能超过 2MB')
      return
    }

    // 上传文件
    uploadAvatarFile(file)
  }

  // 上传头像
  const uploadAvatarFile = async (file) => {
    try {
      updating.value = true
      console.log('开始上传头像:', file.name, file.size, file.type)
      console.log('当前用户角色:', userStore.user?.role)

      // 创建FormData
      const formData = new FormData()
      formData.append('file', file)

      // 先创建本地预览URL用于即时显示
      const previewUrl = URL.createObjectURL(file)
      userForm.avatar = previewUrl

      try {
        // 调用上传API
        console.log('调用头像上传API...')
        const response = await uploadAvatar(formData)
        console.log('头像上传API响应:', response)

        // 使用后端返回的完整URL
        if (response && response.fileUrl) {
          // 更新表单中的头像
          userForm.avatar = response.fileUrl

          // 立即更新用户信息到后端
          const updateData = {
            email: userForm.email,
            phone: userForm.phone,
            realName: userForm.realName,
            avatar: response.filePath  // 后端存储相对路径
          }

          console.log('更新用户信息，包含新头像:', updateData)
          await updateUserInfo(updateData)

          // 重新加载用户信息以获取最新数据
          await loadUserInfo()

          ElMessage.success('头像上传成功')

        } else {
          console.error('头像上传响应异常:', response)
          ElMessage.error('头像上传失败：服务器响应异常')
        }
      } catch (apiError) {
        console.error('头像上传API调用失败:', apiError)
        console.error('API错误详情:', apiError.response?.data || apiError.message)
        ElMessage.error('头像上传失败：' + (apiError.response?.data?.message || apiError.message || '网络错误'))

        // 恢复原来的头像
        userForm.avatar = userInfo.value.avatar
      }

      // 清空文件输入框
      if (fileInputRef.value) {
        fileInputRef.value.value = ''
      }
    } catch (error) {
      console.error('头像上传失败:', error)
      ElMessage.error('头像上传失败，请稍后重试')
    } finally {
      updating.value = false
    }
  }

  // 修改密码
  const handleChangePassword = async () => {
    try {
      await passwordFormRef.value.validate()
      changingPassword.value = true

      await changePassword({
        oldPassword: passwordForm.oldPassword,
        newPassword: passwordForm.newPassword,
        confirmPassword: passwordForm.confirmPassword
      })

      ElMessage.success('密码修改成功')
      resetPasswordForm()
    } catch (error) {
      console.error('修改密码失败:', error)
      if (error.message) {
        ElMessage.error(error.message)
      }
    } finally {
      changingPassword.value = false
    }
  }

  // 重置密码表单
  const resetPasswordForm = () => {
    passwordFormRef.value?.resetFields()
    Object.assign(passwordForm, {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    })
  }

  // 查看申请详情
  const viewRequest = (request) => {
    router.push(`/help-request/${request.id}`)
  }

  // 工具函数
  const formatAmount = (amount) => {
    if (!amount) return '0'
    return amount.toLocaleString()
  }

  const formatDate = (dateString) => {
    if (!dateString) return ''
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  const getStatusType = (status) => {
    const typeMap = {
      success: 'success',
      pending: 'warning',
      failed: 'danger'
    }
    return typeMap[status] || 'info'
  }

  const getRequestStatusType = (status) => {
    const types = {
      pending: 'warning',
      approved: 'success',
      rejected: 'danger'
    }
    return types[status] || 'info'
  }

  const getRequestStatusText = (status) => {
    const texts = {
      pending: '审核中',
      approved: '已通过',
      rejected: '已拒绝'
    }
    return texts[status] || '未知'
  }

  // 跳转到管理后台
  const goToAdminPanel = () => {
    router.push('/admin')
  }

  // 加载管理员统计数据
  const loadAdminStats = async () => {
    if (!userStore.isAdmin) return

    try {
      // 这里可以调用真实的API获取统计数据
      // const stats = await getAdminStats()
      // adminStats.value = stats

      // 临时模拟数据
      adminStats.value = {
        totalUsers: 1250,
        totalProjects: 89,
        totalDonations: 2580000,
        pendingRequests: 12
      }
    } catch (error) {
      console.error('加载管理员统计失败:', error)
    }
  }

  // 加载我的捐赠记录
  const loadMyDonations = async () => {
    try {
      loadingDonations.value = true
      console.log('开始加载我的捐赠记录...')
      console.log('当前用户信息:', userStore.user)
      console.log('当前用户ID:', userStore.user?.id)
      console.log('当前用户Token:', userStore.token ? '已设置' : '未设置')

      const response = await getMyDonations({
        current: 1,
        size: 10
      })

      console.log('捐赠记录API完整响应:', response)
      console.log('响应数据类型:', typeof response)
      console.log('响应数据结构:', Object.keys(response || {}))

      // 根据后端API响应结构，应该是分页数据
      // 响应拦截器已经解包了data，所以直接使用response.records
      if (response && response.records) {
        console.log('使用 response.records:', response.records)
        donations.value = response.records || []
      } else if (Array.isArray(response)) {
        console.log('响应是数组:', response)
        donations.value = response
      } else {
        console.log('未知的响应结构，使用空数组')
        console.log('完整响应对象:', JSON.stringify(response, null, 2))
        donations.value = []
      }

      console.log('最终设置的捐赠记录数量:', donations.value.length)
      console.log('最终设置的捐赠记录:', donations.value)

    } catch (error) {
      console.error('加载捐赠记录失败:', error)
      console.error('错误详情:', error.response?.data || error.message)
      ElMessage.error('加载捐赠记录失败: ' + (error.response?.data?.message || error.message))

      // API失败时显示空列表
      donations.value = []
    } finally {
      loadingDonations.value = false
    }
  }

  // 加载我的申请记录
  const loadMyRequests = async () => {
    try {
      loadingRequests.value = true
      console.log('开始加载我的申请记录...')
      console.log('当前用户信息:', userStore.user)
      console.log('当前用户ID:', userStore.user?.id)
      console.log('当前用户Token:', userStore.token ? '已设置' : '未设置')

      const response = await getMyHelpRequests({
        current: 1,
        size: 10
      })

      console.log('申请记录API完整响应:', response)
      console.log('响应数据类型:', typeof response)
      console.log('响应数据结构:', Object.keys(response || {}))

      // 根据后端API响应结构，应该是分页数据
      // 响应拦截器已经解包了data，所以直接使用response.records
      if (response && response.records) {
        console.log('使用 response.records:', response.records)
        requests.value = response.records || []
      } else if (Array.isArray(response)) {
        console.log('响应是数组:', response)
        requests.value = response
      } else {
        console.log('未知的响应结构，使用空数组')
        console.log('完整响应对象:', JSON.stringify(response, null, 2))
        requests.value = []
      }

      console.log('最终设置的申请记录数量:', requests.value.length)
      console.log('最终设置的申请记录:', requests.value)

    } catch (error) {
      console.error('加载申请记录失败:', error)
      console.error('错误详情:', error.response?.data || error.message)
      ElMessage.error('加载申请记录失败: ' + (error.response?.data?.message || error.message))

      // 如果API失败，显示模拟数据作为后备
      requests.value = [
        {
          id: 1,
          title: '医疗费用申请',
          amountNeeded: 50000,
          status: 'approved',
          createTime: '2024-01-12T09:15:00'
        },
        {
          id: 2,
          title: '教育资助申请',
          amountNeeded: 20000,
          status: 'pending',
          createTime: '2024-01-08T14:30:00'
        }
      ]
    } finally {
      loadingRequests.value = false
    }
  }

  // 保存通知设置
  const saveNotificationSettings = async () => {
    try {
      // 这里可以调用API保存通知设置
      // await updateNotificationSettings(notificationSettings)
      ElMessage.success('通知设置已保存')
    } catch (error) {
      console.error('保存通知设置失败:', error)
      ElMessage.error('保存失败，请稍后重试')
    }
  }

  // 测试直接API调用
  const testDirectAPI = async () => {
    try {
      console.log('=== 开始测试直接API调用 ===')
      console.log('当前用户token:', userStore.token)
      console.log('当前用户信息:', userStore.user)

      // 直接使用axios调用API
      const axios = (await import('axios')).default
      const response = await axios.get('http://localhost:8080/api/help-requests', {
        headers: {
          'Authorization': `Bearer ${userStore.token}`
        },
        params: {
          current: 1,
          size: 10
        }
      })

      console.log('直接API调用响应:', response)
      console.log('响应状态:', response.status)
      console.log('响应数据:', response.data)

      ElMessage.success('API测试完成，请查看控制台')
    } catch (error) {
      console.error('直接API调用失败:', error)
      console.error('错误响应:', error.response?.data)
      ElMessage.error('API测试失败: ' + (error.response?.data?.message || error.message))
    }
  }

  // 测试捐赠API调用
  const testDonationAPI = async () => {
    try {
      console.log('=== 开始测试捐赠API调用 ===')
      console.log('当前用户token:', userStore.token)
      console.log('当前用户信息:', userStore.user)

      // 直接使用axios调用API
      const axios = (await import('axios')).default
      const response = await axios.get('http://localhost:8080/api/donations', {
        headers: {
          'Authorization': `Bearer ${userStore.token}`
        },
        params: {
          current: 1,
          size: 10
        }
      })

      console.log('捐赠API直接调用响应:', response)
      console.log('响应状态:', response.status)
      console.log('响应数据:', response.data)

      ElMessage.success('捐赠API测试完成，请查看控制台')
    } catch (error) {
      console.error('捐赠API调用失败:', error)
      console.error('错误响应:', error.response?.data)
      ElMessage.error('捐赠API测试失败: ' + (error.response?.data?.message || error.message))
    }
  }

  // 监听标签页切换，按需加载数据
  watch(activeTab, (newTab) => {
    console.log('切换到标签页:', newTab)

    switch (newTab) {
      case 'donations':
        if (donations.value.length === 0) {
          loadMyDonations()
        }
        break
      case 'requests':
        if (requests.value.length === 0) {
          loadMyRequests()
        }
        break
      case 'admin-stats':
        if (userStore.isAdmin && adminStats.value.totalUsers === 0) {
          loadAdminStats()
        }
        break
    }
  })

  // 页面初始化
  onMounted(() => {
    loadUserInfo()

    // 如果是管理员，加载管理员统计数据
    if (userStore.isAdmin) {
      loadAdminStats()
    }

    // 加载我的捐赠记录
    loadMyDonations()

    // 加载我的申请记录
    loadMyRequests()
  })
</script>

<style scoped>
  .profile-page {
    min-height: 100vh;
    background: #F7FAFC;
    padding: 32px 0;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }

  /* 页面头部 */
  .page-header {
    background: linear-gradient(135deg, #E53E3E 0%, #C53030 100%);
    color: white;
    padding: 40px 0;
    border-radius: 16px;
    margin-bottom: 32px;
    position: relative;
    overflow: hidden;
  }

  .page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hearts" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><text x="10" y="15" text-anchor="middle" fill="rgba(255,255,255,0.1)" font-size="12">♥</text></pattern></defs><rect width="100" height="100" fill="url(%23hearts)"/></svg>');
    opacity: 0.3;
  }

  .header-nav {
    position: relative;
    z-index: 2;
    padding: 0 32px;
    margin-bottom: 16px;
  }

  .back-home-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
  }

  .back-home-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
  }

  .header-content {
    display: flex;
    align-items: center;
    gap: 24px;
    position: relative;
    z-index: 1;
    padding: 0 32px;
  }

  .user-avatar {
    flex-shrink: 0;
  }

  .user-details h1 {
    font-size: 28px;
    margin-bottom: 8px;
    font-weight: bold;
  }

  .user-email {
    font-size: 16px;
    opacity: 0.9;
    margin-bottom: 16px;
  }

  .user-stats {
    display: flex;
    gap: 32px;
  }

  .stat-item {
    text-align: center;
  }

  .stat-value {
    display: block;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 4px;
  }

  .stat-label {
    font-size: 14px;
    opacity: 0.8;
  }

  /* 主要内容区域 */
  .profile-main {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 32px;
  }

  .profile-sidebar {
    background: #FFFFFF;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    height: fit-content;
    border: 2px solid #FED7D7;
  }

  .sidebar-menu {
    border: none;
  }

  .sidebar-menu .el-menu-item {
    border-radius: 8px;
    margin-bottom: 8px;
    color: #2D3748;
  }

  .sidebar-menu .el-menu-item:hover {
    background-color: #FEF5F5;
    color: #E53E3E;
  }

  .sidebar-menu .el-menu-item.is-active {
    background-color: #E53E3E;
    color: white;
  }

  .profile-content {
    background: #FFFFFF;
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  /* 内容区域 */
  .content-header {
    margin-bottom: 32px;
    padding-bottom: 16px;
    border-bottom: 2px solid #FED7D7;
  }

  .content-header h2 {
    font-size: 24px;
    color: #2D3748;
    margin-bottom: 8px;
    font-weight: bold;
  }

  .content-header p {
    color: #718096;
    margin: 0;
  }

  /* 表单卡片 */
  .info-form-card,
  .security-form-card {
    background: #FEF5F5;
    border-radius: 12px;
    padding: 24px;
    border: 1px solid #FED7D7;
  }

  .security-form-card h3 {
    color: #2D3748;
    margin-bottom: 24px;
    font-size: 18px;
    font-weight: bold;
  }

  /* 头像上传 */
  .avatar-upload {
    display: flex;
    align-items: center;
    gap: 16px;
    cursor: pointer;
    padding: 16px;
    border: 2px dashed #FED7D7;
    border-radius: 8px;
    transition: all 0.3s ease;
  }

  .avatar-upload:hover {
    border-color: #E53E3E;
    background-color: #FEF5F5;
  }

  .upload-text p {
    margin: 0;
    color: #2D3748;
  }

  .upload-tip {
    font-size: 12px;
    color: #718096;
  }

  /* 捐赠统计卡片 */
  .donations-summary {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
    margin-bottom: 32px;
  }

  .summary-card {
    background: #FEF5F5;
    border-radius: 12px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    border: 1px solid #FED7D7;
  }

  .summary-icon {
    width: 48px;
    height: 48px;
    background: #E53E3E;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
  }

  .summary-value {
    font-size: 24px;
    font-weight: bold;
    color: #E53E3E;
    margin-bottom: 4px;
  }

  .summary-label {
    font-size: 14px;
    color: #718096;
  }

  /* 表格卡片 */
  .donations-table-card,
  .requests-table-card {
    background: #FFFFFF;
    border-radius: 12px;
    padding: 24px;
    border: 1px solid #E2E8F0;
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .table-title {
    font-size: 16px;
    font-weight: bold;
    color: #2D3748;
  }

  .table-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  /* 表格样式 */
  .project-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .project-title {
    font-weight: 500;
    color: #2D3748;
  }

  .project-category {
    font-size: 12px;
    color: #718096;
  }

  .request-title {
    font-weight: 500;
    color: #2D3748;
  }

  .amount-text {
    font-weight: 600;
    color: #E53E3E;
  }

  /* 安全信息卡片 */
  .security-info-card {
    background: #F7FAFC;
    border-radius: 12px;
    padding: 24px;
    margin-top: 24px;
    border: 1px solid #E2E8F0;
  }

  .security-info-card h3 {
    color: #2D3748;
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: bold;
  }

  .security-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #E2E8F0;
  }

  .security-item:last-child {
    border-bottom: none;
  }

  .security-label {
    color: #718096;
    font-weight: 500;
  }

  .security-value {
    color: #2D3748;
    font-weight: 500;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .profile-main {
      grid-template-columns: 1fr;
      gap: 24px;
    }

    .header-content {
      flex-direction: column;
      text-align: center;
      gap: 16px;
    }

    .user-stats {
      gap: 16px;
    }

    .donations-summary {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .profile-content {
      padding: 24px;
    }

    .admin-stats-grid {
      grid-template-columns: 1fr;
    }

    .quick-actions-grid {
      grid-template-columns: 1fr;
    }

    .stat-card {
      padding: 16px;
    }

    .action-card {
      padding: 16px;
    }
  }

  /* 管理员统计样式 */
  .admin-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
  }

  .stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 24px;
    color: white;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: transform 0.2s ease;
  }

  .stat-card:hover {
    transform: translateY(-4px);
  }

  .stat-card:nth-child(2) {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  .stat-card:nth-child(3) {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .stat-card:nth-child(4) {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }

  .stat-icon {
    font-size: 32px;
    opacity: 0.8;
  }

  .stat-content {
    flex: 1;
  }

  .stat-value {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 4px;
  }

  .stat-label {
    font-size: 14px;
    opacity: 0.9;
  }

  /* 图表卡片 */
  .admin-charts {
    margin-top: 32px;
  }

  .chart-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    border: 1px solid #E2E8F0;
  }

  .chart-card h3 {
    color: #2D3748;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: bold;
  }

  .chart-placeholder {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 快捷操作样式 */
  .quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .action-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    border: 1px solid #E2E8F0;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .action-card:hover {
    border-color: #4299E1;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(66, 153, 225, 0.15);
  }

  .action-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
  }

  .action-content {
    flex: 1;
  }

  .action-title {
    font-size: 16px;
    font-weight: bold;
    color: #2D3748;
    margin-bottom: 4px;
  }

  .action-desc {
    font-size: 14px;
    color: #718096;
  }

  /* 通知设置样式 */
  .notification-settings-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    border: 1px solid #E2E8F0;
  }

  .setting-group {
    margin-bottom: 32px;
  }

  .setting-group:last-child {
    margin-bottom: 0;
  }

  .setting-group h3 {
    color: #2D3748;
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: bold;
  }

  .setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #E2E8F0;
  }

  .setting-item:last-child {
    border-bottom: none;
  }

  .setting-info {
    flex: 1;
  }

  .setting-title {
    font-size: 16px;
    font-weight: 500;
    color: #2D3748;
    margin-bottom: 4px;
  }

  .setting-desc {
    font-size: 14px;
    color: #718096;
  }

  .setting-actions {
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid #E2E8F0;
  }
</style>