# 爱心救助平台数据库设计文档

## 数据库概述
- **数据库名称**: charity_platform
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **表数量**: 5个核心表 + 1个配置表

## 表结构设计

### 1. 用户表 (users)
存储平台用户信息，支持普通用户和管理员两种角色。

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | BIGINT | 用户ID | 主键，自增 |
| username | VARCHAR(50) | 用户名 | 唯一，非空 |
| password | VARCHAR(255) | 密码(加密) | 非空 |
| email | VARCHAR(100) | 邮箱 | 唯一 |
| phone | VARCHAR(20) | 手机号 | - |
| real_name | VARCHAR(50) | 真实姓名 | - |
| avatar | VARCHAR(255) | 头像URL | - |
| role | ENUM | 角色 | user/admin |
| status | TINYINT | 状态 | 0-禁用, 1-正常 |
| create_time | TIMESTAMP | 创建时间 | 默认当前时间 |
| update_time | TIMESTAMP | 更新时间 | 自动更新 |

### 2. 救助项目表 (projects)
存储所有救助项目信息。

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | BIGINT | 项目ID | 主键，自增 |
| title | VARCHAR(200) | 项目标题 | 非空 |
| description | TEXT | 项目描述 | - |
| category | ENUM | 项目分类 | medical/education/disaster/poverty |
| target_amount | DECIMAL(12,2) | 目标金额 | 非空 |
| current_amount | DECIMAL(12,2) | 当前筹集金额 | 默认0.00 |
| images | TEXT | 项目图片 | JSON格式 |
| contact_info | VARCHAR(500) | 联系信息 | - |
| status | ENUM | 状态 | active/completed/closed |
| admin_id | BIGINT | 发布管理员ID | 外键 |
| create_time | TIMESTAMP | 创建时间 | 默认当前时间 |
| update_time | TIMESTAMP | 更新时间 | 自动更新 |

### 3. 捐赠记录表 (donations)
记录所有捐赠交易信息。

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | BIGINT | 捐赠ID | 主键，自增 |
| user_id | BIGINT | 捐赠用户ID | 外键，非空 |
| project_id | BIGINT | 项目ID | 外键，非空 |
| amount | DECIMAL(10,2) | 捐赠金额 | 非空 |
| payment_method | ENUM | 支付方式 | alipay/wechat/bank |
| transaction_id | VARCHAR(100) | 交易流水号 | - |
| message | VARCHAR(500) | 捐赠留言 | - |
| is_anonymous | TINYINT | 是否匿名 | 0-否, 1-是 |
| status | ENUM | 状态 | pending/success/failed |
| donation_time | TIMESTAMP | 捐赠时间 | 默认当前时间 |

### 4. AI对话记录表 (conversations)
存储用户与AI助手的对话记录。

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | BIGINT | 对话ID | 主键，自增 |
| user_id | BIGINT | 用户ID | 外键，可为空 |
| session_id | VARCHAR(100) | 会话ID | 非空 |
| message | TEXT | 用户消息 | 非空 |
| response | TEXT | AI回复 | 非空 |
| conversation_type | ENUM | 对话类型 | general/donation/help_request/project_info |
| create_time | TIMESTAMP | 创建时间 | 默认当前时间 |

### 5. 救助申请表 (help_requests)
存储用户提交的救助申请。

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | BIGINT | 申请ID | 主键，自增 |
| user_id | BIGINT | 申请用户ID | 外键，非空 |
| title | VARCHAR(200) | 申请标题 | 非空 |
| description | TEXT | 详细描述 | 非空 |
| category | ENUM | 申请分类 | medical/education/disaster/poverty |
| amount_needed | DECIMAL(10,2) | 所需金额 | - |
| contact_phone | VARCHAR(20) | 联系电话 | 非空 |
| contact_address | VARCHAR(500) | 联系地址 | - |
| proof_images | TEXT | 证明材料图片 | JSON格式 |
| status | ENUM | 状态 | pending/approved/rejected/processing |
| admin_response | TEXT | 管理员回复 | - |
| admin_id | BIGINT | 处理管理员ID | 外键 |
| create_time | TIMESTAMP | 创建时间 | 默认当前时间 |
| update_time | TIMESTAMP | 更新时间 | 自动更新 |

## 数据库关系

```
users (1) -----> (N) projects [admin_id]
users (1) -----> (N) donations [user_id]
users (1) -----> (N) conversations [user_id]
users (1) -----> (N) help_requests [user_id]
users (1) -----> (N) help_requests [admin_id]
projects (1) ---> (N) donations [project_id]
```

## 触发器

### update_project_amount
- **触发时机**: 在donations表插入新记录后
- **功能**: 当捐赠状态为'success'时，自动更新对应项目的current_amount

## 索引设计

### 主要索引
- 用户表: username, email, role
- 项目表: category, status, create_time
- 捐赠表: user_id, project_id, donation_time, status
- 对话表: user_id, session_id, conversation_type, create_time
- 申请表: user_id, category, status, create_time

## 初始化数据

### 默认用户
- **管理员**: admin / admin123
- **测试用户**: testuser / admin123

### 示例项目
- 小明白血病治疗救助 (医疗类)
- 山区儿童助学计划 (教育类)
- 洪灾紧急救援 (灾难类)
- 孤寡老人关爱行动 (扶贫类)

## 使用说明

1. **创建数据库**
   ```bash
   mysql -u root -p < schema.sql
   ```

2. **初始化数据**
   ```bash
   mysql -u root -p < init_data.sql
   ```

3. **连接信息**
   - 数据库: charity_platform
   - 字符集: utf8mb4
   - 端口: 3306 (默认)

## 注意事项

1. 密码字段使用BCrypt加密存储
2. 图片字段使用JSON格式存储多个URL
3. 金额字段使用DECIMAL类型确保精度
4. 时间字段统一使用TIMESTAMP类型
5. 外键约束确保数据完整性
