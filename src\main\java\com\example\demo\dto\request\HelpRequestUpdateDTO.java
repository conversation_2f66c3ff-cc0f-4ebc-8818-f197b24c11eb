package com.example.demo.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 救助申请更新请求DTO
 */
@Schema(description = "救助申请更新请求")
public class HelpRequestUpdateDTO {

    @Schema(description = "申请ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "申请ID不能为空")
    private Long id;

    @Schema(description = "申请标题", example = "父亲突发心脏病急需手术费")
    @Size(max = 200, message = "申请标题长度不能超过200字符")
    private String title;

    @Schema(description = "详细描述", example = "我父亲突发急性心肌梗塞，需要立即进行心脏搭桥手术...")
    @Size(max = 2000, message = "详细描述长度不能超过2000字符")
    private String description;

    @Schema(description = "所需金额", example = "150000.00")
    @DecimalMin(value = "0", message = "所需金额不能为负数")
    private BigDecimal amountNeeded;

    @Schema(description = "联系电话", example = "13800138001")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String contactPhone;

    @Schema(description = "联系地址", example = "北京市朝阳区某某街道")
    @Size(max = 500, message = "联系地址长度不能超过500字符")
    private String contactAddress;

    @Schema(description = "证明材料图片URL列表", example = "[\"https://example.com/proof1.jpg\"]")
    private List<String> proofImages;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getAmountNeeded() {
        return amountNeeded;
    }

    public void setAmountNeeded(BigDecimal amountNeeded) {
        this.amountNeeded = amountNeeded;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactAddress() {
        return contactAddress;
    }

    public void setContactAddress(String contactAddress) {
        this.contactAddress = contactAddress;
    }

    public List<String> getProofImages() {
        return proofImages;
    }

    public void setProofImages(List<String> proofImages) {
        this.proofImages = proofImages;
    }
}
