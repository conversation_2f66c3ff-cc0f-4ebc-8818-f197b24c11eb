package com.example.demo.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI对话响应DTO
 */
@Schema(description = "AI对话响应")
public class ConversationResponseDTO {

    @Schema(description = "对话ID", example = "1")
    private Long id;

    @Schema(description = "会话ID", example = "session_001")
    private String sessionId;

    @Schema(description = "用户消息", example = "你好，我想了解如何申请救助")
    private String message;

    @Schema(description = "AI回复", example = "您好！很高兴为您服务...")
    private String response;

    @Schema(description = "对话类型", example = "help_request")
    private String conversationType;

    @Schema(description = "对话类型描述", example = "求助咨询")
    private String conversationTypeDescription;

    @Schema(description = "智能回复建议")
    private List<String> smartReplySuggestions;

    @Schema(description = "相关项目推荐")
    private List<Object> relatedProjects;

    @Schema(description = "用户意图分析")
    private Object intentAnalysis;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public String getConversationType() {
        return conversationType;
    }

    public void setConversationType(String conversationType) {
        this.conversationType = conversationType;
    }

    public String getConversationTypeDescription() {
        return conversationTypeDescription;
    }

    public void setConversationTypeDescription(String conversationTypeDescription) {
        this.conversationTypeDescription = conversationTypeDescription;
    }

    public List<String> getSmartReplySuggestions() {
        return smartReplySuggestions;
    }

    public void setSmartReplySuggestions(List<String> smartReplySuggestions) {
        this.smartReplySuggestions = smartReplySuggestions;
    }

    public List<Object> getRelatedProjects() {
        return relatedProjects;
    }

    public void setRelatedProjects(List<Object> relatedProjects) {
        this.relatedProjects = relatedProjects;
    }

    public Object getIntentAnalysis() {
        return intentAnalysis;
    }

    public void setIntentAnalysis(Object intentAnalysis) {
        this.intentAnalysis = intentAnalysis;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
