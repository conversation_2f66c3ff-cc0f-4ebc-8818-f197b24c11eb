<template>
  <div class="home">
    <!-- 顶部导航 -->
    <header class="header">
      <div class="header-content">
        <div class="logo">
          <el-icon class="logo-icon">
            <Heart />
          </el-icon>
          <span>爱心救助平台</span>
        </div>
        <nav class="nav">
          <el-button text @click="$router.push('/')">首页</el-button>
          <el-button text @click="$router.push('/projects')">救助项目</el-button>
          <el-button text @click="$router.push('/help-request/create')" v-if="userStore.isLoggedIn">申请救助</el-button>
          <el-button text @click="$router.push('/chat')">AI助手</el-button>
          <el-button text @click="$router.push('/profile')">个人中心</el-button>

          <!-- 未登录状态显示登录注册按钮 -->
          <template v-if="!userStore.isLoggedIn">
            <el-button @click="$router.push('/login')">登录</el-button>
            <el-button type="primary" @click="$router.push('/register')">注册</el-button>
          </template>

          <!-- 已登录状态显示用户信息和退出按钮 -->
          <template v-else>
            <el-dropdown @command="handleUserCommand">
              <span class="user-info">
                <el-avatar :size="32" :src="userStore.user?.avatar" />
                <span class="username">{{ userStore.user?.username }}</span>
                <el-icon><arrow-down /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                  <el-dropdown-item command="admin" v-if="userStore.user?.role === 'admin'">管理后台</el-dropdown-item>
                  <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </nav>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="main-content">
      <!-- 欢迎区域 -->
      <section class="hero-section">
        <div class="hero-content">
          <h1>传递爱心，温暖世界</h1>
          <p>每一份捐赠都是希望的种子，每一次帮助都是爱的传递</p>
          <div class="hero-actions">
            <el-button type="primary" size="large" @click="$router.push('/chat')">
              开始对话
            </el-button>
            <el-button size="large" @click="handleHelpRequest" v-if="userStore.isLoggedIn">
              申请救助
            </el-button>
            <el-button size="large" @click="$router.push('/login')" v-else>
              立即参与
            </el-button>
          </div>
        </div>
      </section>

      <!-- 统计数据 -->
      <section class="stats-section">
        <div class="container">
          <el-row :gutter="20">
            <el-col :span="6" v-for="stat in stats" :key="stat.label">
              <div class="stat-card">
                <div class="stat-icon">
                  <el-icon>
                    <component :is="stat.icon" />
                  </el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-number">{{ stat.value }}</div>
                  <div class="stat-label">{{ stat.label }}</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </section>

      <!-- AI助手入口 -->
      <section class="ai-section">
        <div class="container">
          <div class="ai-card">
            <div class="ai-content">
              <el-icon class="ai-icon">
                <ChatDotRound />
              </el-icon>
              <div>
                <h3>AI智能助手</h3>
                <p>有任何疑问？我们的AI助手24小时为您服务</p>
              </div>
            </div>
            <el-button type="primary" size="large" @click="$router.push('/chat')">
              开始对话
            </el-button>
          </div>
        </div>
      </section>
    </main>

    <!-- 底部 -->
    <footer class="footer">
      <div class="container">
        <p>&copy; 2024 爱心救助平台 - 传递温暖，传递爱心</p>
      </div>
    </footer>
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { useUserStore } from '@/stores/user'
  import { ElMessage } from 'element-plus'
  import { ArrowDown, ChatDotRound } from '@element-plus/icons-vue'
  import { getPublicStats } from '@/api/admin'

  const router = useRouter()
  const userStore = useUserStore()

  const stats = ref([
    { label: '累计捐赠', value: '0', icon: 'Money' },
    { label: '救助项目', value: '0', icon: 'Document' },
    { label: '受助人数', value: '0', icon: 'User' },
    { label: '爱心人士', value: '0', icon: 'Heart' }
  ])

  const loading = ref(false)

  // 加载统计数据
  const loadStats = async () => {
    try {
      loading.value = true
      const response = await getPublicStats()

      if (response) {
        // 更新统计数据
        const donationStats = response.donationStats || {}
        const projectStats = response.projectStats || {}
        const userStats = response.userStats || {}
        const helpRequestStats = response.helpRequestStats || {}

        stats.value = [
          {
            label: '累计捐赠',
            value: formatAmount(donationStats.totalAmount || 0),
            icon: 'Money'
          },
          {
            label: '救助项目',
            value: formatNumber(projectStats.totalProjects || 0),
            icon: 'Document'
          },
          {
            label: '受助人数',
            value: formatNumber(helpRequestStats.totalBeneficiaries || helpRequestStats.totalRequests || 0),
            icon: 'User'
          },
          {
            label: '爱心人士',
            value: formatNumber(userStats.totalUsers || 0),
            icon: 'Heart'
          }
        ]
      }
    } catch (error) {
      console.error('加载统计数据失败:', error)
      // 保持默认值，不显示错误信息以免影响用户体验
    } finally {
      loading.value = false
    }
  }

  // 格式化金额
  const formatAmount = (amount) => {
    if (!amount) return '0'
    return Number(amount).toLocaleString()
  }

  // 格式化数字
  const formatNumber = (number) => {
    if (!number) return '0'
    return Number(number).toLocaleString()
  }

  // 处理用户下拉菜单命令
  const handleUserCommand = (command) => {
    switch (command) {
      case 'profile':
        router.push('/profile')
        break
      case 'admin':
        router.push('/admin')
        break
      case 'logout':
        handleLogout()
        break
    }
  }

  // 处理申请救助
  const handleHelpRequest = () => {
    router.push('/help-request/create')
  }

  // 处理退出登录
  const handleLogout = async () => {
    try {
      await userStore.logout()
      ElMessage.success('退出登录成功')
      router.push('/')
    } catch (error) {
      ElMessage.error('退出登录失败')
    }
  }

  // 页面初始化
  onMounted(() => {
    loadStats()
  })
</script>

<style scoped>
  .home {
    min-height: 100vh;
    background: #F7FAFC;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  /* 顶部导航样式 */
  .header {
    background: #FFFFFF;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    padding: 0;
    position: sticky;
    top: 0;
    z-index: 100;
  }

  .header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
    padding: 0 24px;
  }

  .logo {
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: bold;
    color: #E53E3E;
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .logo:hover {
    transform: scale(1.05);
  }

  .logo-icon {
    margin-right: 8px;
    font-size: 24px;
    animation: heartbeat 2s ease-in-out infinite;
  }

  @keyframes heartbeat {

    0%,
    100% {
      transform: scale(1);
    }

    50% {
      transform: scale(1.1);
    }
  }

  .nav {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: background-color 0.2s;
  }

  .user-info:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .username {
    color: #333;
    font-size: 14px;
    font-weight: 500;
  }

  /* 主要内容 */
  .main-content {
    flex: 1;
    padding: 0;
  }

  /* 欢迎区域 */
  .hero-section {
    background: linear-gradient(135deg, #E53E3E 0%, #C53030 100%);
    color: white;
    text-align: center;
    padding: 100px 0;
    margin-bottom: 60px;
    position: relative;
    overflow: hidden;
  }

  .hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hearts" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><text x="10" y="15" text-anchor="middle" fill="rgba(255,255,255,0.1)" font-size="12">♥</text></pattern></defs><rect width="100" height="100" fill="url(%23hearts)"/></svg>');
    opacity: 0.3;
  }

  .hero-content {
    position: relative;
    z-index: 1;
  }

  .hero-content h1 {
    font-size: 48px;
    margin-bottom: 20px;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }

  .hero-content p {
    font-size: 20px;
    margin-bottom: 40px;
    opacity: 0.95;
  }

  .hero-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
  }

  .carousel-item {
    height: 400px;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .carousel-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
  }

  .carousel-content {
    text-align: center;
    color: white;
    position: relative;
    z-index: 1;
  }

  .carousel-content h2 {
    font-size: 36px;
    margin-bottom: 16px;
  }

  .carousel-content p {
    font-size: 18px;
    margin-bottom: 24px;
  }

  /* 统计数据样式 */
  .stats-section {
    padding: 60px 0;
    background: #FFFFFF;
    margin-bottom: 60px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .stat-card {
    display: flex;
    align-items: center;
    padding: 24px;
    text-align: center;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    background: #FFFFFF;
    transition: all 0.3s ease;
    border: 2px solid transparent;
  }

  .stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    border-color: #FED7D7;
  }

  .stat-icon {
    font-size: 48px;
    color: #E53E3E;
    margin-right: 16px;
    transition: all 0.3s ease;
  }

  .stat-card:hover .stat-icon {
    transform: scale(1.1);
    color: #C53030;
  }

  .stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #2D3748;
    margin-bottom: 8px;
  }

  .stat-label {
    font-size: 16px;
    color: #718096;
  }

  /* 项目展示样式 */
  .projects-section {
    padding: 60px 0;
    margin-bottom: 60px;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
  }

  .section-header h2 {
    font-size: 28px;
    color: #2c3e50;
  }

  .project-card {
    cursor: pointer;
    transition: transform 0.3s;
    margin-bottom: 20px;
  }

  .project-card:hover {
    transform: translateY(-5px);
  }

  .project-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
  }

  .project-content {
    padding: 16px;
  }

  .project-content h3 {
    font-size: 18px;
    margin-bottom: 8px;
    color: #2c3e50;
  }

  .project-description {
    color: #7f8c8d;
    margin-bottom: 16px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .project-progress {
    margin-top: 16px;
  }

  .progress-info {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 14px;
    color: #7f8c8d;
  }

  /* AI助手样式 */
  .ai-section {
    padding: 60px 0;
    background: linear-gradient(135deg, #E53E3E 0%, #C53030 100%);
    position: relative;
    overflow: hidden;
  }

  .ai-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" x="0" y="0" width="10" height="10" patternUnits="userSpaceOnUse"><circle cx="5" cy="5" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  }

  .ai-card {
    background: #FFFFFF;
    border-radius: 16px;
    padding: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
    border: 2px solid #FED7D7;
    transition: all 0.3s ease;
  }

  .ai-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  .ai-content {
    display: flex;
    align-items: center;
  }

  .ai-icon {
    font-size: 48px;
    color: #E53E3E;
    margin-right: 24px;
    animation: float 3s ease-in-out infinite;
  }

  @keyframes float {

    0%,
    100% {
      transform: translateY(0px);
    }

    50% {
      transform: translateY(-10px);
    }
  }

  .ai-content h3 {
    font-size: 24px;
    color: #2D3748;
    margin-bottom: 8px;
    font-weight: bold;
  }

  .ai-content p {
    color: #718096;
    font-size: 16px;
  }

  /* 底部样式 */
  .footer {
    background: #2D3748;
    color: #FFFFFF;
    text-align: center;
    padding: 24px 0;
    margin-top: 60px;
  }

  .footer p {
    margin: 0;
    font-size: 14px;
    opacity: 0.8;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .hero-content h1 {
      font-size: 32px;
    }

    .hero-content p {
      font-size: 16px;
    }

    .hero-actions {
      flex-direction: column;
      align-items: center;
    }

    .ai-card {
      flex-direction: column;
      text-align: center;
    }

    .ai-content {
      flex-direction: column;
      margin-bottom: 20px;
    }

    .ai-icon {
      margin-right: 0;
      margin-bottom: 16px;
    }

    .stat-card {
      flex-direction: column;
      text-align: center;
    }

    .stat-icon {
      margin-right: 0;
      margin-bottom: 12px;
    }
  }
</style>