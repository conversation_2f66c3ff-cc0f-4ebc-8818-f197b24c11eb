package com.example.demo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.entity.User;

import java.util.List;

/**
 * 用户服务接口
 * 定义用户相关的业务操作
 */
public interface UserService {

    /**
     * 用户注册
     * @param user 用户信息
     * @return 注册成功的用户信息
     */
    User register(User user);

    /**
     * 用户登录
     * @param username 用户名
     * @param password 密码
     * @return 登录成功的用户信息
     */
    User login(String username, String password);

    /**
     * 根据用户名查询用户
     * @param username 用户名
     * @return 用户信息
     */
    User findByUsername(String username);

    /**
     * 根据邮箱查询用户
     * @param email 邮箱
     * @return 用户信息
     */
    User findByEmail(String email);

    /**
     * 根据ID查询用户
     * @param id 用户ID
     * @return 用户信息
     */
    User findById(Long id);

    /**
     * 更新用户信息
     * @param user 用户信息
     * @return 更新后的用户信息
     */
    User updateUser(User user);

    /**
     * 更新用户密码
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否更新成功
     */
    boolean updatePassword(Long userId, String oldPassword, String newPassword);

    /**
     * 更新用户状态
     * @param userId 用户ID
     * @param status 新状态
     * @return 是否更新成功
     */
    boolean updateUserStatus(Long userId, Integer status);

    /**
     * 重置用户密码
     * @param userId 用户ID
     * @return 是否重置成功
     */
    boolean resetUserPassword(Long userId);

    /**
     * 分页查询用户列表
     * @param page 分页参数
     * @param role 角色筛选
     * @param status 状态筛选
     * @param keyword 关键词搜索
     * @return 分页用户列表
     */
    IPage<User> findUsersWithConditions(Page<User> page, String role, Integer status, String keyword);

    /**
     * 根据角色查询用户列表
     * @param role 角色
     * @return 用户列表
     */
    List<User> findByRole(String role);

    /**
     * 检查用户名是否存在
     * @param username 用户名
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean isUsernameExists(String username, Long excludeId);

    /**
     * 检查邮箱是否存在
     * @param email 邮箱
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean isEmailExists(String email, Long excludeId);

    /**
     * 删除用户
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean deleteUser(Long userId);

    /**
     * 批量删除用户
     * @param userIds 用户ID列表
     * @return 是否删除成功
     */
    boolean batchDeleteUsers(List<Long> userIds);

    /**
     * 统计用户数量
     * @param role 角色筛选
     * @param status 状态筛选
     * @return 用户数量
     */
    Long countUsers(String role, Integer status);

    /**
     * 获取所有管理员列表
     * @return 管理员列表
     */
    List<User> getAllAdmins();

    /**
     * 验证用户权限
     * @param userId 用户ID
     * @param requiredRole 需要的角色
     * @return 是否有权限
     */
    boolean hasPermission(Long userId, String requiredRole);

    /**
     * 重置用户密码
     * @param userId 用户ID
     * @param newPassword 新密码
     * @return 是否重置成功
     */
    boolean resetPassword(Long userId, String newPassword);

    /**
     * 获取用户统计信息
     * @return 用户统计信息
     */
    Object getUserStatistics();

    /**
     * 统计今日新增用户数
     * @return 今日新增用户数
     */
    Long countTodayNewUsers();
}
