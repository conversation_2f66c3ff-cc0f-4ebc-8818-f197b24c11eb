<template>
  <div class="chat-page">
    <div class="container">
      <div class="chat-container">
        <!-- 聊天头部 -->
        <div class="chat-header">
          <div class="header-left">
            <el-button @click="goHome" size="small" class="home-btn">
              <el-icon>
                <ArrowLeft />
              </el-icon>
              返回首页
            </el-button>
          </div>
          <div class="chat-title">
            <el-icon>
              <ChatDotRound />
            </el-icon>
            <span>AI智能助手</span>
          </div>
          <div class="header-right">
            <el-button @click="clearChat" size="small">清空对话</el-button>
          </div>
        </div>

        <!-- 聊天内容 -->
        <div class="chat-content" ref="chatContentRef">
          <!-- 欢迎消息 -->
          <div v-if="messages.length === 0" class="welcome-section">
            <div class="welcome-message">
              <el-icon class="welcome-icon">
                <ChatDotRound />
              </el-icon>
              <h3>{{ welcomeMessage }}</h3>
            </div>

            <!-- 热门问题 -->
            <div class="popular-questions">
              <h4>热门问题</h4>
              <div class="question-grid">
                <el-button v-for="question in popularQuestions" :key="question" class="question-btn"
                  @click="sendMessage(question)">
                  {{ question }}
                </el-button>
              </div>
            </div>
          </div>

          <!-- 对话消息 -->
          <div v-for="message in messages" :key="message.id" class="message-item">
            <div v-if="message.type === 'user'" class="user-message">
              <div class="message-content">{{ message.content }}</div>
              <el-avatar class="message-avatar" :size="32">
                <el-icon>
                  <User />
                </el-icon>
              </el-avatar>
            </div>

            <div v-else class="ai-message">
              <el-avatar class="message-avatar" :size="32">
                <el-icon>
                  <ChatDotRound />
                </el-icon>
              </el-avatar>
              <div class="message-wrapper">
                <div class="message-content">{{ message.content }}</div>
                <!-- 智能回复建议 -->
                <div v-if="message.suggestions && message.suggestions.length > 0" class="smart-suggestions">
                  <div class="suggestions-label">您可能想问：</div>
                  <div class="suggestions-buttons">
                    <el-button v-for="suggestion in message.suggestions" :key="suggestion" size="small" type="primary"
                      plain @click="sendMessage(suggestion)">
                      {{ suggestion }}
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="ai-message">
            <el-avatar class="message-avatar" :size="32">
              <el-icon>
                <ChatDotRound />
              </el-icon>
            </el-avatar>
            <div class="message-content loading">
              <span>AI正在思考中</span>
              <el-icon class="loading-icon">
                <Loading />
              </el-icon>
            </div>
          </div>
        </div>

        <!-- 输入框 -->
        <div class="chat-input">
          <el-input v-model="inputMessage" placeholder="请输入您的问题..." @keyup.enter="handleSendMessage"
            :disabled="loading">
            <template #append>
              <el-button type="primary" @click="handleSendMessage" :disabled="loading || !inputMessage.trim()">
                发送
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted, nextTick } from 'vue'
  import { useRouter } from 'vue-router'
  import { sendMessage as sendChatMessage, getWelcomeMessage, getPopularQuestions } from '@/api/chat'
  import { ElMessage } from 'element-plus'
  import { ChatDotRound, User, Loading, ArrowLeft } from '@element-plus/icons-vue'

  const router = useRouter()

  const chatContentRef = ref()
  const loading = ref(false)
  const inputMessage = ref('')
  const welcomeMessage = ref('您好！我是爱心救助平台的AI助手，很高兴为您服务。')
  const popularQuestions = ref([])
  const messages = reactive([])

  let messageId = 0

  // 发送消息
  const sendMessage = async (content) => {
    if (!content.trim()) return

    // 添加用户消息
    messages.push({
      id: ++messageId,
      type: 'user',
      content: content,
      timestamp: new Date()
    })

    // 清空输入框
    inputMessage.value = ''

    // 滚动到底部
    await nextTick()
    scrollToBottom()

    try {
      loading.value = true

      // 调用真实的AI API
      const response = await sendChatMessage({
        message: content,
        sessionId: 'web-chat-' + Date.now(),
        conversationType: 'general'
      })

      // 添加AI回复 - 修复字段名（注意：response.message是用户输入，不是AI回复）
      const aiMessage = {
        id: ++messageId,
        type: 'ai',
        content: response.response || response.aiResponse || '抱歉，我暂时无法回答这个问题。',
        timestamp: new Date()
      }

      // 如果有智能回复建议，也添加进去
      if (response.smartReplySuggestions && response.smartReplySuggestions.length > 0) {
        aiMessage.suggestions = response.smartReplySuggestions
      }

      messages.push(aiMessage)

    } catch (error) {
      console.error('发送消息失败:', error)
      messages.push({
        id: ++messageId,
        type: 'ai',
        content: '抱歉，服务暂时不可用，请稍后再试。',
        timestamp: new Date()
      })
    } finally {
      loading.value = false
      await nextTick()
      scrollToBottom()
    }
  }

  // 处理发送消息
  const handleSendMessage = () => {
    if (inputMessage.value.trim()) {
      sendMessage(inputMessage.value)
    }
  }

  // 返回首页
  const goHome = () => {
    router.push('/')
  }

  // 清空对话
  const clearChat = () => {
    messages.length = 0
    ElMessage.success('对话已清空')
  }

  // 滚动到底部
  const scrollToBottom = () => {
    if (chatContentRef.value) {
      chatContentRef.value.scrollTop = chatContentRef.value.scrollHeight
    }
  }

  // 获取欢迎消息和热门问题
  const fetchInitialData = async () => {
    try {
      const [welcome, questions] = await Promise.all([
        getWelcomeMessage(),
        getPopularQuestions()
      ])

      welcomeMessage.value = welcome || '您好！我是爱心救助平台的AI助手，很高兴为您服务。'
      popularQuestions.value = questions || [
        '如何申请救助？',
        '如何进行捐赠？',
        '救助申请需要什么材料？',
        '捐赠资金如何使用？',
        '如何查看救助进度？',
        '平台如何保证资金安全？'
      ]
    } catch (error) {
      console.error('获取初始数据失败:', error)
      // 使用默认数据
      welcomeMessage.value = '您好！我是爱心救助平台的AI助手，很高兴为您服务。'
      popularQuestions.value = [
        '如何申请救助？',
        '如何进行捐赠？',
        '救助申请需要什么材料？',
        '捐赠资金如何使用？',
        '如何查看救助进度？',
        '平台如何保证资金安全？'
      ]
    }
  }

  onMounted(() => {
    fetchInitialData()
  })
</script>

<style scoped>
  .chat-page {
    min-height: calc(100vh - 120px);
    padding: 20px 0;
    background: #F7FAFC;
  }

  .container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .chat-container {
    background: #FFFFFF;
    border-radius: 16px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    height: 600px;
    display: flex;
    flex-direction: column;
    border: 2px solid #FED7D7;
  }

  .chat-header {
    padding: 20px;
    border-bottom: 2px solid #FED7D7;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #FEF5F5;
    border-radius: 16px 16px 0 0;
  }

  .header-left,
  .header-right {
    flex: 1;
  }

  .header-left {
    display: flex;
    justify-content: flex-start;
  }

  .header-right {
    display: flex;
    justify-content: flex-end;
  }

  .home-btn {
    background: #E53E3E;
    color: white;
    border: none;
    transition: all 0.3s ease;
  }

  .home-btn:hover {
    background: #C53030;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(229, 62, 62, 0.3);
  }

  .chat-title {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
    color: #2D3748;
  }

  .chat-title .el-icon {
    margin-right: 8px;
    font-size: 20px;
    color: #E53E3E;
    animation: pulse 2s ease-in-out infinite;
  }

  @keyframes pulse {

    0%,
    100% {
      opacity: 1;
    }

    50% {
      opacity: 0.7;
    }
  }

  .chat-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }

  .welcome-section {
    text-align: center;
  }

  .welcome-message {
    margin-bottom: 40px;
  }

  .welcome-icon {
    font-size: 48px;
    color: #667eea;
    margin-bottom: 16px;
  }

  .welcome-message h3 {
    color: #2c3e50;
    margin-bottom: 8px;
  }

  .popular-questions h4 {
    color: #2c3e50;
    margin-bottom: 20px;
  }

  .question-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
  }

  .question-btn {
    text-align: left;
    height: auto;
    padding: 12px 16px;
    white-space: normal;
    line-height: 1.4;
  }

  .message-item {
    margin-bottom: 20px;
  }

  .user-message {
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
  }

  .user-message .message-content {
    background: #E53E3E;
    color: white;
    padding: 12px 16px;
    border-radius: 18px 18px 4px 18px;
    max-width: 70%;
    margin-right: 12px;
    box-shadow: 0 2px 4px rgba(229, 62, 62, 0.3);
  }

  .ai-message {
    display: flex;
    align-items: flex-start;
  }

  .message-wrapper {
    margin-left: 12px;
    max-width: 70%;
  }

  .ai-message .message-content {
    background: #F7FAFC;
    color: #2D3748;
    padding: 12px 16px;
    border-radius: 18px 18px 18px 4px;
    margin-bottom: 8px;
    border: 1px solid #E2E8F0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .smart-suggestions {
    margin-top: 8px;
  }

  .suggestions-label {
    font-size: 12px;
    color: #718096;
    margin-bottom: 6px;
    font-weight: 500;
  }

  .suggestions-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
  }

  .suggestions-buttons .el-button {
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
    border-color: #E53E3E;
    color: #ffffff;
    transition: all 0.3s ease;
  }

  .suggestions-buttons .el-button:hover {
    background-color: #E53E3E;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(229, 62, 62, 0.3);
  }

  .message-content.loading {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .loading-icon {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  .chat-input {
    padding: 20px;
    border-top: 1px solid #eee;
  }
</style>