<template>
  <div class="help-request-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" class="back-btn">
          <el-icon>
            <ArrowLeft />
          </el-icon>
          返回申请列表
        </el-button>
        <div class="title-section">
          <h1 class="page-title">申请详情</h1>
          <p class="page-description">查看和审核救助申请详细信息</p>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="handleRefresh">
          <el-icon>
            <Refresh />
          </el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <div v-loading="loading" class="content-wrapper">
      <!-- 申请基本信息 -->
      <div class="info-section">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>申请信息</span>
              <div class="header-actions">
                <el-tag :type="getStatusType(requestInfo.status)" size="large">
                  {{ getStatusText(requestInfo.status) }}
                </el-tag>
              </div>
            </div>
          </template>

          <div class="request-profile">
            <div class="basic-info">
              <h2 class="request-title">{{ requestInfo.title }}</h2>
              <div class="request-meta">
                <el-tag :type="getCategoryType(requestInfo.category)" size="small">
                  {{ getCategoryText(requestInfo.category) }}
                </el-tag>
                <span class="amount">需要金额：¥{{ formatAmount(requestInfo.amountNeeded) }}</span>
              </div>
            </div>

            <div class="info-grid">
              <div class="info-item">
                <label>申请ID</label>
                <span>{{ requestInfo.id }}</span>
              </div>
              <div class="info-item">
                <label>申请人</label>
                <span>{{ requestInfo.username || '未知用户' }}</span>
              </div>
              <div class="info-item">
                <label>联系电话</label>
                <span>{{ requestInfo.contactPhone || '未设置' }}</span>
              </div>
              <div class="info-item">
                <label>联系地址</label>
                <span>{{ requestInfo.contactAddress || '未设置' }}</span>
              </div>
              <div class="info-item">
                <label>申请时间</label>
                <span>{{ formatTime(requestInfo.createTime) }}</span>
              </div>
              <div class="info-item">
                <label>审核时间</label>
                <span>{{ formatTime(requestInfo.reviewTime) }}</span>
              </div>
            </div>

            <!-- 申请描述 -->
            <div class="description-section">
              <h3>申请描述</h3>
              <div class="description-content">
                {{ requestInfo.description || '无描述' }}
              </div>
            </div>

            <!-- 证明材料 -->
            <div v-if="requestInfo.evidenceFiles && requestInfo.evidenceFiles.length > 0" class="evidence-section">
              <h3>证明材料</h3>
              <div class="evidence-grid">
                <div v-for="(file, index) in requestInfo.evidenceFiles" :key="index" class="evidence-item"
                  @click="previewFile(file)">
                  <img v-if="isImage(file)" :src="file" :alt="`证明材料${index + 1}`" />
                  <div v-else class="file-placeholder">
                    <el-icon>
                      <Document />
                    </el-icon>
                    <span>文档</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 审核信息 -->
            <div v-if="requestInfo.adminResponse" class="review-section">
              <h3>审核意见</h3>
              <div class="review-content">
                <div class="review-response">{{ requestInfo.adminResponse }}</div>
                <div class="review-meta">
                  审核人：{{ requestInfo.adminName || '未知' }} •
                  审核时间：{{ formatTime(requestInfo.reviewTime) }}
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 审核操作 -->
      <div v-if="requestInfo.status === 'pending' || requestInfo.status === 'processing'" class="actions-section">
        <el-card>
          <template #header>
            <span>审核操作</span>
          </template>

          <el-form :model="reviewForm" label-width="100px">
            <el-form-item label="审核结果" required>
              <el-radio-group v-model="reviewForm.status">
                <el-radio value="approved">
                  <el-icon>
                    <Check />
                  </el-icon>
                  通过申请
                </el-radio>
                <el-radio value="rejected">
                  <el-icon>
                    <Close />
                  </el-icon>
                  拒绝申请
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="审核意见" required>
              <el-input v-model="reviewForm.adminResponse" type="textarea" :rows="4" placeholder="请输入审核意见..."
                maxlength="500" show-word-limit />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="handleSubmitReview" :loading="submitting"
                :disabled="!reviewForm.status || !reviewForm.adminResponse">
                提交审核
              </el-button>
              <el-button @click="resetReviewForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <!-- 其他操作 -->
      <div class="other-actions-section">
        <el-card>
          <template #header>
            <span>其他操作</span>
          </template>

          <div class="action-buttons">
            <el-button @click="handleMarkUrgent"
              :disabled="requestInfo.status !== 'pending' && requestInfo.status !== 'processing'">
              <el-icon>
                <Warning />
              </el-icon>
              标记紧急
            </el-button>

            <el-button @click="handleViewSimilar">
              <el-icon>
                <Search />
              </el-icon>
              查看相似申请
            </el-button>

            <el-button @click="handleViewUserInfo">
              <el-icon>
                <User />
              </el-icon>
              查看申请人信息
            </el-button>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="previewVisible" title="图片预览" width="60%" center>
      <div class="preview-container">
        <img :src="previewImage" alt="预览图片" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    ArrowLeft,
    Refresh,
    Check,
    Close,
    Document,
    Warning,
    Search,
    User
  } from '@element-plus/icons-vue'
  import {
    getHelpRequestById,
    reviewHelpRequest,
    markAsUrgent
  } from '@/api/admin'

  const route = useRoute()
  const router = useRouter()

  // 响应式数据
  const loading = ref(false)
  const submitting = ref(false)
  const requestInfo = ref({})
  const previewVisible = ref(false)
  const previewImage = ref('')

  // 审核表单
  const reviewForm = reactive({
    status: '',
    adminResponse: ''
  })

  // 获取申请ID
  const requestId = route.params.id

  // 加载申请详情
  const loadRequestDetail = async () => {
    try {
      loading.value = true

      const response = await getHelpRequestById(requestId)
      if (response) {
        requestInfo.value = response
      }

    } catch (error) {
      console.error('加载申请详情失败:', error)
      ElMessage.error('加载申请详情失败')
    } finally {
      loading.value = false
    }
  }

  // 返回申请列表
  const goBack = () => {
    router.push('/admin/help-requests')
  }

  // 刷新
  const handleRefresh = () => {
    loadRequestDetail()
  }

  // 提交审核
  const handleSubmitReview = async () => {
    if (!reviewForm.status || !reviewForm.adminResponse) {
      ElMessage.warning('请填写完整的审核信息')
      return
    }

    try {
      submitting.value = true

      await reviewHelpRequest({
        requestId: requestInfo.value.id,
        status: reviewForm.status,
        adminResponse: reviewForm.adminResponse
      })

      ElMessage.success('审核提交成功')
      loadRequestDetail()
      resetReviewForm()

    } catch (error) {
      console.error('提交审核失败:', error)
      if (error.message) {
        ElMessage.error(error.message)
      }
    } finally {
      submitting.value = false
    }
  }

  // 重置审核表单
  const resetReviewForm = () => {
    reviewForm.status = ''
    reviewForm.adminResponse = ''
  }

  // 标记紧急
  const handleMarkUrgent = async () => {
    try {
      await ElMessageBox.confirm(
        `确定要将申请"${requestInfo.value.title}"标记为紧急吗？`,
        '确认标记',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await markAsUrgent(requestInfo.value.id)
      ElMessage.success('已标记为紧急申请')
      loadRequestDetail()

    } catch (error) {
      if (error !== 'cancel') {
        console.error('标记紧急失败:', error)
        if (error.message) {
          ElMessage.error(error.message)
        }
      }
    }
  }

  // 查看相似申请
  const handleViewSimilar = () => {
    ElMessage.info('查看相似申请功能开发中...')
  }

  // 查看申请人信息
  const handleViewUserInfo = () => {
    if (requestInfo.value.userId) {
      router.push(`/admin/users/${requestInfo.value.userId}`)
    } else {
      ElMessage.warning('无法获取申请人信息')
    }
  }

  // 预览文件
  const previewFile = (fileUrl) => {
    if (isImage(fileUrl)) {
      previewImage.value = fileUrl
      previewVisible.value = true
    } else {
      // 对于非图片文件，可以在新窗口打开
      window.open(fileUrl, '_blank')
    }
  }

  // 判断是否为图片
  const isImage = (fileUrl) => {
    if (!fileUrl) return false
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
    return imageExtensions.some(ext => fileUrl.toLowerCase().includes(ext))
  }

  // 工具函数
  const getCategoryType = (category) => {
    const typeMap = {
      medical: 'danger',
      education: 'primary',
      disaster: 'warning',
      poverty: 'success',
      other: 'info'
    }
    return typeMap[category] || ''
  }

  const getCategoryText = (category) => {
    const textMap = {
      medical: '医疗救助',
      education: '教育助学',
      disaster: '灾难救援',
      poverty: '扶贫助困',
      other: '其他'
    }
    return textMap[category] || category
  }

  const getStatusType = (status) => {
    const typeMap = {
      pending: 'warning',
      processing: 'primary',
      approved: 'success',
      rejected: 'danger'
    }
    return typeMap[status] || ''
  }

  const getStatusText = (status) => {
    const textMap = {
      pending: '待审核',
      processing: '处理中',
      approved: '已通过',
      rejected: '已拒绝'
    }
    return textMap[status] || status
  }

  const formatTime = (timeString) => {
    if (!timeString) return '未知'
    return new Date(timeString).toLocaleString('zh-CN')
  }

  const formatAmount = (amount) => {
    if (!amount) return '0'
    return amount.toLocaleString()
  }

  // 页面初始化
  onMounted(() => {
    loadRequestDetail()
  })
</script>

<style scoped>
  .help-request-detail {
    padding: 24px;
    background: #f5f5f5;
    min-height: 100vh;
  }

  /* 页面头部 */
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .back-btn {
    color: #718096;
  }

  .title-section {
    flex: 1;
  }

  .page-title {
    font-size: 24px;
    font-weight: bold;
    color: #2D3748;
    margin: 0 0 8px 0;
  }

  .page-description {
    color: #718096;
    margin: 0;
    font-size: 14px;
  }

  /* 内容区域 */
  .content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  /* 信息区域 */
  .info-section .el-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .request-profile {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .basic-info {
    padding-bottom: 24px;
    border-bottom: 1px solid #E2E8F0;
  }

  .request-title {
    font-size: 20px;
    font-weight: bold;
    color: #2D3748;
    margin: 0 0 12px 0;
  }

  .request-meta {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .amount {
    color: #E53E3E;
    font-weight: 500;
    font-size: 16px;
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .info-item label {
    font-size: 12px;
    color: #718096;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .info-item span {
    font-size: 14px;
    color: #2D3748;
    font-weight: 500;
  }

  /* 描述区域 */
  .description-section h3,
  .evidence-section h3,
  .review-section h3 {
    font-size: 16px;
    font-weight: bold;
    color: #2D3748;
    margin: 0 0 12px 0;
  }

  .description-content {
    background: #F7FAFC;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #4299E1;
    line-height: 1.6;
    color: #2D3748;
  }

  /* 证明材料 */
  .evidence-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 16px;
  }

  .evidence-item {
    cursor: pointer;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid #E2E8F0;
    transition: border-color 0.2s;
  }

  .evidence-item:hover {
    border-color: #4299E1;
  }

  .evidence-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
  }

  .file-placeholder {
    width: 100%;
    height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #F7FAFC;
    color: #718096;
    gap: 8px;
  }

  .file-placeholder .el-icon {
    font-size: 32px;
  }

  /* 审核信息 */
  .review-content {
    background: #F7FAFC;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #48BB78;
  }

  .review-response {
    color: #2D3748;
    line-height: 1.6;
    margin-bottom: 8px;
  }

  .review-meta {
    font-size: 12px;
    color: #718096;
  }

  /* 操作区域 */
  .actions-section .el-card,
  .other-actions-section .el-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .action-buttons {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
  }

  .action-buttons .el-button {
    flex: 1;
    min-width: 140px;
  }

  /* 图片预览 */
  .preview-container {
    text-align: center;
  }

  .preview-container img {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .help-request-detail {
      padding: 16px;
    }

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .header-left {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    .request-meta {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .info-grid {
      grid-template-columns: 1fr;
    }

    .evidence-grid {
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }

    .action-buttons {
      flex-direction: column;
    }

    .action-buttons .el-button {
      flex: none;
      width: 100%;
    }
  }
</style>