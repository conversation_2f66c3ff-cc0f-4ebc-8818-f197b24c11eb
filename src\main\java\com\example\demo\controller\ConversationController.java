package com.example.demo.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.dto.ApiResponse;
import com.example.demo.dto.PageDTO;
import com.example.demo.dto.converter.DTOConverter;
import com.example.demo.dto.request.ConversationRequestDTO;
import com.example.demo.dto.response.ConversationResponseDTO;
import com.example.demo.entity.Conversation;
import com.example.demo.service.ConversationService;
import com.example.demo.service.AiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * AI对话Controller
 * 提供AI对话、会话管理等API
 */
@Slf4j
@RestController
@RequestMapping("/api/conversations")
@Tag(name = "AI对话管理", description = "AI对话、会话管理等API")
@Validated
public class ConversationController extends BaseController {

    @Autowired
    private ConversationService conversationService;

    @Autowired
    private AiService aiService;

    @PostMapping("/chat")
    @Operation(summary = "发送AI对话消息", description = "发送消息给AI并获取回复")
    public ApiResponse<ConversationResponseDTO> chat(@Valid @RequestBody ConversationRequestDTO requestDTO) {
        try {
            Long userId = getCurrentUserId();
            
            // 如果没有提供sessionId，生成新的
            String sessionId = requestDTO.getSessionId();
            if (!StringUtils.hasText(sessionId)) {
                sessionId = conversationService.generateSessionId(userId);
            }
            
            logOperation("发送AI对话消息", userId, sessionId, requestDTO.getConversationType());
            
            // 使用真正的AI服务获取回复
            Long conversationId = sessionId != null ? Long.valueOf(sessionId.hashCode()) : null;
            String aiResponse = aiService.chat(requestDTO.getMessage(), conversationId);

            // 保存对话记录到数据库（使用真正的AI回复）
            Conversation conversation = new Conversation();
            conversation.setUserId(userId);
            conversation.setSessionId(sessionId);
            conversation.setMessage(requestDTO.getMessage());
            conversation.setResponse(aiResponse); // 使用真正的AI回复
            conversation.setConversationType(requestDTO.getConversationType());
            conversationService.createConversation(conversation);

            // 获取最新的对话记录
            List<Conversation> conversations = conversationService.findBySessionId(sessionId);
            Conversation latestConversation = conversations.get(conversations.size() - 1);

            // 构建响应DTO
            ConversationResponseDTO responseDTO = convertToConversationResponseDTO(latestConversation);
            
            // 获取智能回复建议
            List<String> suggestions = conversationService.getSmartReplySuggestions(
                    requestDTO.getMessage(), requestDTO.getConversationType());
            responseDTO.setSmartReplySuggestions(suggestions);
            
            // 获取相关项目推荐
            List<Object> relatedProjects = conversationService.getRelatedProjectRecommendations(
                    requestDTO.getMessage(), 3);
            responseDTO.setRelatedProjects(relatedProjects);
            
            // 分析用户意图
            Object intentAnalysis = conversationService.analyzeUserIntent(requestDTO.getMessage());
            responseDTO.setIntentAnalysis(intentAnalysis);
            
            return success("对话成功", responseDTO);
        } catch (Exception e) {
            log.error("AI对话失败", e);
            return error("对话失败：" + e.getMessage());
        }
    }

    @GetMapping("/sessions")
    @Operation(summary = "获取用户会话列表", description = "获取当前用户的最近会话列表")
    public ApiResponse<List<String>> getUserSessions(
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        try {
            Long userId = getCurrentUserId();
            logOperation("获取用户会话列表", userId, limit);
            
            List<String> sessions = conversationService.findRecentSessionsByUser(userId, limit);
            return success(sessions);
        } catch (Exception e) {
            log.error("获取用户会话列表失败", e);
            return error("获取会话列表失败：" + e.getMessage());
        }
    }

    @GetMapping("/sessions/{sessionId}")
    @Operation(summary = "获取会话历史", description = "获取指定会话的对话历史")
    public ApiResponse<List<ConversationResponseDTO>> getSessionHistory(
            @Parameter(description = "会话ID") @PathVariable @NotNull String sessionId) {
        try {
            Long userId = getCurrentUserId();
            logOperation("获取会话历史", userId, sessionId);
            
            List<Conversation> conversations = conversationService.findBySessionId(sessionId);
            
            // 权限检查：确保会话属于当前用户
            if (!conversations.isEmpty() && conversations.get(0).getUserId() != null 
                && !conversations.get(0).getUserId().equals(userId)) {
                return forbidden("无权限查看此会话");
            }
            
            List<ConversationResponseDTO> responseList = convertList(conversations, 
                    this::convertToConversationResponseDTO);
            
            return success(responseList);
        } catch (Exception e) {
            log.error("获取会话历史失败", e);
            return error("获取会话历史失败：" + e.getMessage());
        }
    }

    @GetMapping
    @Operation(summary = "分页查询对话记录", description = "管理员分页查询对话记录")
    public ApiResponse<PageDTO<ConversationResponseDTO>> getConversationList(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId,
            @Parameter(description = "对话类型") @RequestParam(required = false) String conversationType,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword) {
        try {
            logOperation("分页查询对话记录", current, size, userId, conversationType, keyword);
            
            // 权限检查：只有管理员可以查看所有对话记录
            if (!isAdmin()) {
                return forbidden("无权限查看对话记录");
            }
            
            Page<Conversation> page = new Page<>(current, size);
            var result = conversationService.findConversationsWithConditions(
                    page, userId, conversationType, keyword);
            
            return convertPageResult(result, this::convertToConversationResponseDTO);
        } catch (Exception e) {
            log.error("查询对话记录失败", e);
            return error("查询对话记录失败：" + e.getMessage());
        }
    }

    @GetMapping("/welcome")
    @Operation(summary = "获取欢迎消息", description = "获取指定类型的AI欢迎消息")
    public ApiResponse<String> getWelcomeMessage(
            @Parameter(description = "对话类型") @RequestParam(required = false) String conversationType) {
        try {
            logOperation("获取欢迎消息", conversationType);
            
            String welcomeMessage = aiService.getWelcomeMessage();
            return success(welcomeMessage);
        } catch (Exception e) {
            log.error("获取欢迎消息失败", e);
            return error("获取欢迎消息失败：" + e.getMessage());
        }
    }

    @GetMapping("/popular-questions")
    @Operation(summary = "获取热门问题", description = "获取用户常问的热门问题")
    public ApiResponse<List<String>> getPopularQuestions(
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        try {
            logOperation("获取热门问题", limit);

            List<String> questions = aiService.getPopularQuestions();
            // 如果需要限制数量，截取前limit个
            if (limit != null && limit > 0 && questions.size() > limit) {
                questions = questions.subList(0, limit);
            }
            return success(questions);
        } catch (Exception e) {
            log.error("获取热门问题失败", e);
            return error("获取热门问题失败：" + e.getMessage());
        }
    }

    @GetMapping("/stats")
    @Operation(summary = "获取对话统计信息", description = "获取对话相关的统计数据")
    public ApiResponse<Object> getConversationStats() {
        try {
            logOperation("获取对话统计信息");
            
            // 权限检查：只有管理员可以查看统计信息
            if (!isAdmin()) {
                return forbidden("无权限查看统计信息");
            }
            
            Object stats = conversationService.getConversationOverviewStats();
            return success(stats);
        } catch (Exception e) {
            log.error("获取对话统计信息失败", e);
            return error("获取对话统计信息失败：" + e.getMessage());
        }
    }

    @GetMapping("/user/{userId}/summary")
    @Operation(summary = "获取用户对话摘要", description = "获取指定用户的对话摘要信息")
    public ApiResponse<Object> getUserConversationSummary(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long userId) {
        try {
            Long currentUserId = getCurrentUserId();
            logOperation("获取用户对话摘要", currentUserId, userId);
            
            // 权限检查：普通用户只能查看自己的摘要
            if (!isAdmin() && !currentUserId.equals(userId)) {
                return forbidden("无权限查看其他用户的对话摘要");
            }
            
            Object summary = conversationService.getUserConversationSummary(userId);
            return success(summary);
        } catch (Exception e) {
            log.error("获取用户对话摘要失败", e);
            return error("获取用户对话摘要失败：" + e.getMessage());
        }
    }

    @PostMapping("/{id}/rate")
    @Operation(summary = "对话评分", description = "用户对AI对话进行评分")
    public ApiResponse<Void> rateConversation(
            @Parameter(description = "对话ID") @PathVariable @NotNull Long id,
            @Parameter(description = "评分(1-5)") @RequestParam @NotNull Integer rating,
            @Parameter(description = "反馈内容") @RequestParam(required = false) String feedback) {
        try {
            Long userId = getCurrentUserId();
            logOperation("对话评分", userId, id, rating);
            
            boolean success = conversationService.rateConversation(id, rating, feedback);
            if (success) {
                return success("评分成功", null);
            } else {
                return error("评分失败");
            }
        } catch (Exception e) {
            log.error("对话评分失败", e);
            return error("评分失败：" + e.getMessage());
        }
    }

    @PostMapping("/{id}/resolve")
    @Operation(summary = "标记对话已解决", description = "管理员标记对话为已解决")
    public ApiResponse<Void> markConversationResolved(
            @Parameter(description = "对话ID") @PathVariable @NotNull Long id) {
        try {
            Long adminId = getCurrentUserId();
            logOperation("标记对话已解决", adminId, id);
            
            // 权限检查：只有管理员可以标记对话
            if (!isAdmin()) {
                return forbidden("无权限标记对话");
            }
            
            boolean success = conversationService.markConversationResolved(id, adminId);
            if (success) {
                return success("标记成功", null);
            } else {
                return error("标记失败");
            }
        } catch (Exception e) {
            log.error("标记对话已解决失败", e);
            return error("标记失败：" + e.getMessage());
        }
    }

    @GetMapping("/{id}/intervention-check")
    @Operation(summary = "检查是否需要人工介入", description = "检查对话是否需要人工客服介入")
    public ApiResponse<Boolean> checkNeedsHumanIntervention(
            @Parameter(description = "对话ID") @PathVariable @NotNull Long id) {
        try {
            logOperation("检查是否需要人工介入", id);
            
            boolean needsIntervention = conversationService.needsHumanIntervention(id);
            return success(needsIntervention);
        } catch (Exception e) {
            log.error("检查人工介入失败", e);
            return error("检查失败：" + e.getMessage());
        }
    }

    @PostMapping("/sessions/new")
    @Operation(summary = "创建新会话", description = "为用户创建新的对话会话")
    public ApiResponse<String> createNewSession() {
        try {
            Long userId = getCurrentUserId();
            logOperation("创建新会话", userId);
            
            String sessionId = conversationService.generateSessionId(userId);
            return success("会话创建成功", sessionId);
        } catch (Exception e) {
            log.error("创建新会话失败", e);
            return error("创建会话失败：" + e.getMessage());
        }
    }

    /**
     * 转换Conversation实体为ConversationResponseDTO
     */
    private ConversationResponseDTO convertToConversationResponseDTO(Conversation conversation) {
        ConversationResponseDTO dto = new ConversationResponseDTO();
        dto.setId(conversation.getId());
        dto.setSessionId(conversation.getSessionId());
        dto.setMessage(conversation.getMessage());
        dto.setResponse(conversation.getResponse());
        dto.setConversationType(conversation.getConversationType());
        dto.setConversationTypeDescription(
                DTOConverter.getConversationTypeDescription(conversation.getConversationType()));
        dto.setCreateTime(conversation.getCreateTime());
        
        return dto;
    }
}
