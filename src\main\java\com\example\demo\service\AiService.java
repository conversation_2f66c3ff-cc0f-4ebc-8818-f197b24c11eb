package com.example.demo.service;

import java.util.List;

/**
 * AI服务接口
 * 提供AI对话和智能助手功能
 */
public interface AiService {

    /**
     * 发送消息给AI并获取回复
     * @param message 用户消息
     * @param conversationId 对话ID（可选）
     * @return AI回复
     */
    String chat(String message, Long conversationId);

    /**
     * 获取欢迎消息
     * @return 欢迎消息
     */
    String getWelcomeMessage();

    /**
     * 获取热门问题列表
     * @return 热门问题列表
     */
    List<String> getPopularQuestions();

    /**
     * 清除对话历史
     * @param conversationId 对话ID
     */
    void clearConversationHistory(Long conversationId);

    /**
     * 获取对话历史
     * @param conversationId 对话ID
     * @return 对话历史
     */
    List<String> getConversationHistory(Long conversationId);

    /**
     * 检查AI服务是否可用
     * @return 是否可用
     */
    boolean isServiceAvailable();
}
