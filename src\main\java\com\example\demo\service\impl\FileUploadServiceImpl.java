package com.example.demo.service.impl;

import com.example.demo.dto.response.FileUploadResponseDTO;
import com.example.demo.exception.BusinessException;
import com.example.demo.exception.ErrorCode;
import com.example.demo.service.FileUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;


import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.UUID;

/**
 * 文件上传服务实现类
 */
@Slf4j
@Service
public class FileUploadServiceImpl implements FileUploadService {

    // 使用项目根目录下的uploads文件夹
    private final String uploadBasePath = System.getProperty("user.dir") + "/uploads";

    @Value("${server.servlet.context-path:}")
    private String contextPath;

    @Value("${server.port:8080}")
    private String serverPort;

    // 文件类型常量
    private static final String[] IMAGE_TYPES = {"image/jpeg", "image/jpg", "image/png", "image/gif"};
    private static final String[] DOCUMENT_TYPES = {"application/pdf", "application/msword", 
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document"};
    
    // 文件大小常量（字节）
    private static final long MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB
    private static final long MAX_DOCUMENT_SIZE = 10 * 1024 * 1024; // 10MB

    @Override
    public FileUploadResponseDTO uploadAvatar(MultipartFile file, Long userId) {
        log.info("上传头像文件: userId={}, fileName={}", userId, file.getOriginalFilename());
        
        // 验证文件
        validateFile(file, IMAGE_TYPES, MAX_IMAGE_SIZE);
        
        return uploadFile(file, "avatars", userId);
    }

    @Override
    public FileUploadResponseDTO uploadProjectImage(MultipartFile file, Long projectId) {
        log.info("上传项目图片: projectId={}, fileName={}", projectId, file.getOriginalFilename());
        
        // 验证文件
        validateFile(file, IMAGE_TYPES, MAX_IMAGE_SIZE);
        
        return uploadFile(file, "projects", projectId);
    }

    @Override
    public FileUploadResponseDTO uploadRequestDocument(MultipartFile file, Long requestId) {
        log.info("上传申请证明材料: requestId={}, fileName={}", requestId, file.getOriginalFilename());
        
        // 验证文件（支持图片和文档）
        String[] allowedTypes = mergeArrays(IMAGE_TYPES, DOCUMENT_TYPES);
        validateFile(file, allowedTypes, MAX_DOCUMENT_SIZE);
        
        return uploadFile(file, "documents", requestId);
    }

    @Override
    public FileUploadResponseDTO uploadFile(MultipartFile file, String category, Long relatedId) {
        try {
            // 生成文件名
            String originalFileName = file.getOriginalFilename();
            String extension = getFileExtension(originalFileName);
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String uuid = UUID.randomUUID().toString().substring(0, 8);
            String fileName = String.format("%s_%s_%s_%s.%s",
                    category.substring(0, category.length() - 1), // 去掉复数s
                    relatedId != null ? relatedId : "temp",
                    timestamp,
                    uuid,
                    extension);

            // 创建目录路径
            String yearMonth = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM"));
            String fullDirPath = uploadBasePath + "/" + category + "/" + yearMonth;

            log.info("准备创建目录: {}", fullDirPath);

            // 确保目录存在
            Path dirPath = Paths.get(fullDirPath);
            if (!Files.exists(dirPath)) {
                Files.createDirectories(dirPath);
                log.info("成功创建上传目录: {}", fullDirPath);
            }

            // 保存文件
            String fullFilePath = fullDirPath + "/" + fileName;
            Path filePath = Paths.get(fullFilePath);
            file.transferTo(filePath.toFile());

            log.info("文件保存成功: {}", fullFilePath);

            // 生成相对路径和访问URL
            String relativePath = "/" + category + "/" + yearMonth + "/" + fileName;
            String fileUrl = generateFileUrl(relativePath);

            log.info("文件上传成功: {}", relativePath);

            return new FileUploadResponseDTO(
                    fileName,
                    relativePath,
                    fileUrl,
                    file.getSize(),
                    file.getContentType(),
                    System.currentTimeMillis()
            );

        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new BusinessException(ErrorCode.FILE_UPLOAD_ERROR, "文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public boolean deleteFile(String filePath) {
        try {
            if (!StringUtils.hasText(filePath)) {
                return false;
            }

            // 构建完整文件路径
            String fullPath = uploadBasePath + filePath;
            Path path = Paths.get(fullPath);
            
            if (Files.exists(path)) {
                Files.delete(path);
                log.info("文件删除成功: {}", filePath);
                return true;
            } else {
                log.warn("文件不存在: {}", filePath);
                return false;
            }
        } catch (IOException e) {
            log.error("文件删除失败: {}", filePath, e);
            return false;
        }
    }

    @Override
    public boolean validateFileType(MultipartFile file, String[] allowedTypes) {
        if (file == null || file.isEmpty()) {
            return false;
        }

        String contentType = file.getContentType();
        if (!StringUtils.hasText(contentType)) {
            return false;
        }

        return Arrays.asList(allowedTypes).contains(contentType.toLowerCase());
    }

    @Override
    public boolean validateFileSize(MultipartFile file, long maxSize) {
        if (file == null || file.isEmpty()) {
            return false;
        }

        return file.getSize() <= maxSize;
    }

    /**
     * 验证文件
     */
    private void validateFile(MultipartFile file, String[] allowedTypes, long maxSize) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException(ErrorCode.INVALID_PARAMETER, "文件不能为空");
        }

        if (!validateFileType(file, allowedTypes)) {
            throw new BusinessException(ErrorCode.INVALID_PARAMETER, 
                    "不支持的文件类型，支持的类型: " + Arrays.toString(allowedTypes));
        }

        if (!validateFileSize(file, maxSize)) {
            throw new BusinessException(ErrorCode.INVALID_PARAMETER, 
                    "文件大小超过限制，最大允许: " + (maxSize / 1024 / 1024) + "MB");
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (!StringUtils.hasText(fileName)) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        
        return "";
    }

    /**
     * 生成文件访问URL
     */
    private String generateFileUrl(String relativePath) {
        return String.format("http://localhost:%s%s/uploads%s", 
                serverPort, 
                StringUtils.hasText(contextPath) ? contextPath : "",
                relativePath);
    }

    /**
     * 合并数组
     */
    private String[] mergeArrays(String[] array1, String[] array2) {
        String[] result = new String[array1.length + array2.length];
        System.arraycopy(array1, 0, result, 0, array1.length);
        System.arraycopy(array2, 0, result, array1.length, array2.length);
        return result;
    }
}
