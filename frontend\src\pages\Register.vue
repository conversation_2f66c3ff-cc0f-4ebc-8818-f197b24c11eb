<template>
  <div class="register-page">
    <div class="register-container">
      <div class="register-header">
        <el-icon class="logo-icon">
          <Heart />
        </el-icon>
        <h1>注册账号</h1>
        <p>加入我们，传递爱心</p>
      </div>

      <el-form ref="registerFormRef" :model="registerForm" :rules="registerRules" class="register-form">
        <el-form-item prop="username">
          <el-input v-model="registerForm.username" placeholder="请输入用户名" size="large" prefix-icon="User" />
        </el-form-item>

        <el-form-item prop="email">
          <el-input v-model="registerForm.email" placeholder="请输入邮箱" size="large" prefix-icon="Message" />
        </el-form-item>

        <el-form-item prop="password">
          <el-input v-model="registerForm.password" type="password" placeholder="请输入密码" size="large" prefix-icon="Lock"
            show-password />
        </el-form-item>

        <el-form-item prop="confirmPassword">
          <el-input v-model="registerForm.confirmPassword" type="password" placeholder="请确认密码" size="large"
            prefix-icon="Lock" show-password />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" size="large" class="register-button" :loading="loading" @click="handleRegister">
            注册
          </el-button>
        </el-form-item>

        <div class="register-footer">
          <span>已有账号？</span>
          <el-link type="primary" @click="$router.push('/login')">
            立即登录
          </el-link>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage } from 'element-plus'
  import { register } from '@/api/user'

  const router = useRouter()
  const registerFormRef = ref()
  const loading = ref(false)

  const registerForm = reactive({
    username: '',
    email: '',
    password: '',
    confirmPassword: ''
  })

  const validateConfirmPassword = (rule, value, callback) => {
    if (value !== registerForm.password) {
      callback(new Error('两次输入的密码不一致'))
    } else {
      callback()
    }
  }

  const registerRules = {
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 3, max: 20, message: '用户名长度在3到20个字符', trigger: 'blur' }
    ],
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
    ],
    confirmPassword: [
      { required: true, message: '请确认密码', trigger: 'blur' },
      { validator: validateConfirmPassword, trigger: 'blur' }
    ]
  }

  const handleRegister = async () => {
    try {
      await registerFormRef.value.validate()
      loading.value = true

      // 调用真实的注册API
      await register({
        username: registerForm.username,
        email: registerForm.email,
        password: registerForm.password,
        confirmPassword: registerForm.confirmPassword
      })

      ElMessage.success('注册成功，请登录')
      router.push('/login')
    } catch (error) {
      console.error('注册失败:', error)
    } finally {
      loading.value = false
    }
  }
</script>

<style scoped>
  .register-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
  }

  .register-container {
    background: white;
    border-radius: 12px;
    padding: 40px;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .register-header {
    text-align: center;
    margin-bottom: 40px;
  }

  .logo-icon {
    font-size: 48px;
    color: #e74c3c;
    margin-bottom: 16px;
  }

  .register-header h1 {
    font-size: 24px;
    color: #2c3e50;
    margin-bottom: 8px;
  }

  .register-header p {
    color: #7f8c8d;
    font-size: 14px;
  }

  .register-form {
    width: 100%;
  }

  .register-button {
    width: 100%;
  }

  .register-footer {
    text-align: center;
    margin-top: 20px;
    color: #7f8c8d;
    font-size: 14px;
  }

  .register-footer .el-link {
    margin-left: 8px;
  }
</style>