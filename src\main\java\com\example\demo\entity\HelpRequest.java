package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 救助申请实体类
 * 对应数据库表: help_requests
 */
@Data
@TableName("help_requests")
@Schema(description = "救助申请实体")
public class HelpRequest {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "申请ID", example = "1")
    private Long id;

    @TableField("user_id")
    @Schema(description = "申请用户ID", example = "2")
    private Long userId;

    @TableField("title")
    @Schema(description = "申请标题", example = "父亲突发心脏病急需手术费")
    private String title;

    @TableField("description")
    @Schema(description = "详细描述", example = "我父亲突发急性心肌梗塞，需要立即进行心脏搭桥手术...")
    private String description;

    @TableField("category")
    @Schema(description = "申请分类: medical-医疗, education-教育, disaster-灾难, poverty-扶贫", example = "medical")
    private String category;

    @TableField("amount_needed")
    @Schema(description = "所需金额", example = "150000.00")
    private BigDecimal amountNeeded;

    @TableField("contact_phone")
    @Schema(description = "联系电话", example = "13800138001")
    private String contactPhone;

    @TableField("contact_address")
    @Schema(description = "联系地址", example = "北京市朝阳区某某街道")
    private String contactAddress;

    @TableField("proof_images")
    @Schema(description = "证明材料图片(JSON格式)", example = "[\"https://example.com/proof1.jpg\"]")
    private String proofImages;

    @TableField("status")
    @Schema(description = "状态: pending-待审核, approved-已通过, rejected-已拒绝, processing-处理中", example = "pending")
    private String status;

    @TableField("admin_response")
    @Schema(description = "管理员回复", example = "经审核，情况属实，已安排志愿者跟进。")
    private String adminResponse;

    @TableField("admin_id")
    @Schema(description = "处理管理员ID", example = "1")
    private Long adminId;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 申请分类枚举
     */
    public enum Category {
        MEDICAL("medical", "医疗救助"),
        EDUCATION("education", "教育助学"),
        DISASTER("disaster", "灾难救援"),
        POVERTY("poverty", "扶贫助困");

        private final String code;
        private final String description;

        Category(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 申请状态枚举
     */
    public enum Status {
        PENDING("pending", "待审核"),
        APPROVED("approved", "已通过"),
        REJECTED("rejected", "已拒绝"),
        PROCESSING("processing", "处理中");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
