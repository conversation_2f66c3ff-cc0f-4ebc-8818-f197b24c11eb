// 爱心救助平台 - 红白主题配色方案

// 主色调
$primary-red: #E53E3E;
$primary-red-dark: #C53030;
$primary-red-light: #FED7D7;
$primary-red-lighter: #FEF5F5;

// 辅助色
$white: #FFFFFF;
$gray-50: #F7FAFC;
$gray-100: #EDF2F7;
$gray-200: #E2E8F0;
$gray-300: #CBD5E0;
$gray-400: #A0AEC0;
$gray-500: #718096;
$gray-600: #4A5568;
$gray-700: #2D3748;
$gray-800: #1A202C;
$gray-900: #171923;

// 功能色
$success: #38A169;
$warning: #D69E2E;
$error: #E53E3E;
$info: #3182CE;

// 文字颜色
$text-primary: #2D3748;
$text-secondary: #718096;
$text-light: #A0AEC0;
$text-white: #FFFFFF;

// 背景色
$bg-primary: #FFFFFF;
$bg-secondary: #F7FAFC;
$bg-accent: #FEF5F5;

// 边框色
$border-light: #E2E8F0;
$border-medium: #CBD5E0;
$border-dark: #A0AEC0;

// 阴影
$shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

// 圆角
$radius-sm: 4px;
$radius-md: 8px;
$radius-lg: 12px;
$radius-xl: 16px;
$radius-full: 50%;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-2xl: 48px;

// 字体大小
$text-xs: 12px;
$text-sm: 14px;
$text-base: 16px;
$text-lg: 18px;
$text-xl: 20px;
$text-2xl: 24px;
$text-3xl: 30px;
$text-4xl: 36px;

// 断点
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
