package com.example.demo.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 捐赠列表响应DTO（用于分页查询）
 */
@Schema(description = "捐赠列表响应")
public class DonationListDTO {

    @Schema(description = "捐赠ID", example = "1")
    private Long id;

    @Schema(description = "用户ID", example = "2")
    private Long userId;

    @Schema(description = "项目ID", example = "1")
    private Long projectId;

    @Schema(description = "捐赠者姓名", example = "张三")
    private String donorName;

    @Schema(description = "项目标题", example = "小明白血病治疗救助")
    private String projectTitle;

    @Schema(description = "捐赠金额", example = "1000.00")
    private BigDecimal amount;

    @Schema(description = "支付方式", example = "alipay")
    private String paymentMethod;

    @Schema(description = "支付方式描述", example = "支付宝")
    private String paymentMethodDescription;

    @Schema(description = "交易流水号", example = "ALI20241201001")
    private String transactionId;

    @Schema(description = "捐赠留言", example = "希望小明早日康复！")
    private String message;

    @Schema(description = "是否匿名", example = "false")
    private Boolean isAnonymous;

    @Schema(description = "捐赠状态", example = "success")
    private String status;

    @Schema(description = "状态描述", example = "成功")
    private String statusDescription;

    @Schema(description = "捐赠时间")
    private LocalDateTime donationTime;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getDonorName() {
        return donorName;
    }

    public void setDonorName(String donorName) {
        this.donorName = donorName;
    }

    public String getProjectTitle() {
        return projectTitle;
    }

    public void setProjectTitle(String projectTitle) {
        this.projectTitle = projectTitle;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getPaymentMethodDescription() {
        return paymentMethodDescription;
    }

    public void setPaymentMethodDescription(String paymentMethodDescription) {
        this.paymentMethodDescription = paymentMethodDescription;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Boolean getIsAnonymous() {
        return isAnonymous;
    }

    public void setIsAnonymous(Boolean isAnonymous) {
        this.isAnonymous = isAnonymous;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusDescription() {
        return statusDescription;
    }

    public void setStatusDescription(String statusDescription) {
        this.statusDescription = statusDescription;
    }

    public LocalDateTime getDonationTime() {
        return donationTime;
    }

    public void setDonationTime(LocalDateTime donationTime) {
        this.donationTime = donationTime;
    }
}
