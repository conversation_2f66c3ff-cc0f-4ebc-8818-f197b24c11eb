package com.example.demo.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.dto.ApiResponse;
import com.example.demo.dto.PageDTO;
import com.example.demo.dto.converter.DTOConverter;
import com.example.demo.dto.request.*;
import com.example.demo.dto.response.*;
import com.example.demo.entity.User;
import com.example.demo.security.JwtTokenProvider;
import com.example.demo.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * 用户管理Controller
 * 提供用户注册、登录、信息管理等API
 */
@Slf4j
@RestController
@RequestMapping("/api/users")
@Tag(name = "用户管理", description = "用户注册、登录、信息管理相关API")
@Validated
public class UserController extends BaseController {

    @Autowired
    private UserService userService;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "新用户注册账号")
    public ApiResponse<UserResponseDTO> register(@Valid @RequestBody UserRegisterDTO registerDTO) {
        try {
            logOperation("用户注册", registerDTO.getUsername());
            
            // 验证密码确认
            if (!registerDTO.getPassword().equals(registerDTO.getConfirmPassword())) {
                return badRequest("两次输入的密码不一致");
            }
            
            // 创建用户实体
            User user = new User();
            user.setUsername(registerDTO.getUsername());
            user.setPassword(registerDTO.getPassword());
            user.setEmail(registerDTO.getEmail());
            user.setPhone(registerDTO.getPhone());
            user.setRealName(registerDTO.getRealName());
            
            // 注册用户
            User registeredUser = userService.register(user);
            UserResponseDTO responseDTO = DTOConverter.convertToUserResponseDTO(registeredUser);
            
            return success("注册成功", responseDTO);
        } catch (Exception e) {
            log.error("用户注册失败", e);
            return error("注册失败：" + e.getMessage());
        }
    }

    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户登录获取访问令牌")
    public ApiResponse<UserLoginResponseDTO> login(@Valid @RequestBody UserLoginDTO loginDTO) {
        try {
            logOperation("用户登录", loginDTO.getUsername());
            
            // 用户登录验证
            User user = userService.login(loginDTO.getUsername(), loginDTO.getPassword());

            // 生成JWT令牌
            String accessToken = jwtTokenProvider.generateAccessToken(user.getId(), user.getUsername(), user.getRole());
            String refreshToken = jwtTokenProvider.generateRefreshToken(user.getId(), user.getUsername());

            // 创建登录响应
            UserLoginResponseDTO responseDTO = new UserLoginResponseDTO();
            responseDTO.setAccessToken(accessToken);
            responseDTO.setRefreshToken(refreshToken);
            responseDTO.setExpiresIn(7200L); // 2小时
            responseDTO.setUserInfo(DTOConverter.convertToUserResponseDTO(user));
            
            return success("登录成功", responseDTO);
        } catch (Exception e) {
            log.error("用户登录失败", e);
            return error("登录失败：" + e.getMessage());
        }
    }

    @GetMapping("/profile")
    @Operation(summary = "获取用户信息", description = "获取当前登录用户的详细信息")
    public ApiResponse<UserResponseDTO> getProfile() {
        try {
            Long userId = getCurrentUserId();
            logOperation("获取用户信息", userId);
            
            User user = userService.findById(userId);
            if (user == null) {
                return notFound("用户不存在");
            }
            
            UserResponseDTO responseDTO = DTOConverter.convertToUserResponseDTO(user);
            return success(responseDTO);
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return error("获取用户信息失败：" + e.getMessage());
        }
    }

    @PutMapping("/profile")
    @Operation(summary = "更新用户信息", description = "更新当前登录用户的基本信息")
    public ApiResponse<UserResponseDTO> updateProfile(@Valid @RequestBody UserUpdateDTO updateDTO) {
        try {
            Long userId = getCurrentUserId();
            logOperation("更新用户信息", userId, updateDTO);
            
            // 创建用户实体
            User user = new User();
            user.setId(userId);
            user.setEmail(updateDTO.getEmail());
            user.setPhone(updateDTO.getPhone());
            user.setRealName(updateDTO.getRealName());
            user.setAvatar(updateDTO.getAvatar());
            
            // 更新用户信息
            User updatedUser = userService.updateUser(user);
            UserResponseDTO responseDTO = DTOConverter.convertToUserResponseDTO(updatedUser);
            
            return success("更新成功", responseDTO);
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return error("更新失败：" + e.getMessage());
        }
    }

    @PutMapping("/password")
    @Operation(summary = "修改密码", description = "修改当前登录用户的密码")
    public ApiResponse<Void> updatePassword(@Valid @RequestBody UserPasswordUpdateDTO passwordDTO) {
        try {
            Long userId = getCurrentUserId();
            logOperation("修改密码", userId);
            
            // 验证新密码确认
            if (!passwordDTO.getNewPassword().equals(passwordDTO.getConfirmPassword())) {
                return badRequest("两次输入的新密码不一致");
            }
            
            // 修改密码
            boolean success = userService.updatePassword(userId, passwordDTO.getOldPassword(), passwordDTO.getNewPassword());
            if (success) {
                return success("密码修改成功", null);
            } else {
                return error("密码修改失败");
            }
        } catch (Exception e) {
            log.error("修改密码失败", e);
            return error("修改密码失败：" + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取用户信息", description = "管理员可查看任意用户信息，普通用户只能查看自己的信息")
    public ApiResponse<UserResponseDTO> getUserById(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long id) {
        try {
            Long currentUserId = getCurrentUserId();
            logOperation("根据ID获取用户信息", currentUserId, id);
            
            // 权限检查：普通用户只能查看自己的信息
            if (!isAdmin() && !currentUserId.equals(id)) {
                return forbidden("无权限查看其他用户信息");
            }
            
            User user = userService.findById(id);
            if (user == null) {
                return notFound("用户不存在");
            }
            
            UserResponseDTO responseDTO = DTOConverter.convertToUserResponseDTO(user);
            return success(responseDTO);
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return error("获取用户信息失败：" + e.getMessage());
        }
    }

    @GetMapping
    @Operation(summary = "分页查询用户列表", description = "管理员查看用户列表，支持分页和搜索")
    public ApiResponse<PageDTO<UserListDTO>> getUserList(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "用户角色") @RequestParam(required = false) String role,
            @Parameter(description = "用户状态") @RequestParam(required = false) Integer status) {
        try {
            logOperation("分页查询用户列表", current, size, keyword, role, status);
            
            // 权限检查：只有管理员可以查看用户列表
            if (!isAdmin()) {
                return forbidden("无权限查看用户列表");
            }
            
            Page<User> page = new Page<>(current, size);
            // 参数顺序：page, role, status, keyword
            var result = userService.findUsersWithConditions(page, role, status, keyword);

            return convertPageResult(result, DTOConverter::convertToUserListDTO);
        } catch (Exception e) {
            log.error("查询用户列表失败", e);
            return error("查询用户列表失败：" + e.getMessage());
        }
    }

    @PutMapping("/{id}/status")
    @Operation(summary = "更新用户状态", description = "管理员更新用户状态（启用/禁用）")
    public ApiResponse<Void> updateUserStatus(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long id,
            @Parameter(description = "新状态") @RequestParam @NotNull Integer status) {
        try {
            logOperation("更新用户状态", id, status);
            
            // 权限检查：只有管理员可以更新用户状态
            if (!isAdmin()) {
                return forbidden("无权限更新用户状态");
            }
            
            boolean success = userService.updateUserStatus(id, status);
            if (success) {
                return success("用户状态更新成功", null);
            } else {
                return error("用户状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新用户状态失败", e);
            return error("更新用户状态失败：" + e.getMessage());
        }
    }

    @GetMapping("/stats")
    @Operation(summary = "获取用户统计信息", description = "获取用户相关的统计数据")
    public ApiResponse<Object> getUserStats() {
        try {
            logOperation("获取用户统计信息");
            
            Object stats = userService.getUserStatistics();
            return success(stats);
        } catch (Exception e) {
            log.error("获取用户统计信息失败", e);
            return error("获取用户统计信息失败：" + e.getMessage());
        }
    }

    @PutMapping("/{id}/reset-password")
    @Operation(summary = "重置用户密码", description = "管理员重置用户密码为默认密码")
    public ApiResponse<Void> resetUserPassword(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long id) {
        try {
            logOperation("重置用户密码", id);

            // 权限检查：只有管理员可以重置用户密码
            if (!isAdmin()) {
                return forbidden("无权限重置用户密码");
            }

            boolean success = userService.resetUserPassword(id);
            if (success) {
                return success("用户密码重置成功", null);
            } else {
                return error("用户密码重置失败");
            }
        } catch (Exception e) {
            log.error("重置用户密码失败", e);
            return error("重置用户密码失败：" + e.getMessage());
        }
    }

    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户登出，清除会话信息")
    public ApiResponse<Void> logout() {
        try {
            Long userId = getCurrentUserId();
            logOperation("用户登出", userId);
            
            // JWT token失效逻辑已在AuthController中实现，这里可以直接返回成功
            
            return success("登出成功", null);
        } catch (Exception e) {
            log.error("用户登出失败", e);
            return error("登出失败：" + e.getMessage());
        }
    }
}
