<template>
  <div class="projects-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="container">
        <div class="header-content">
          <div class="header-text">
            <h1>救助项目</h1>
            <p>每一个项目都承载着希望，每一份捐赠都传递着温暖</p>
          </div>
          <div class="header-stats">
            <div class="stat-item">
              <span class="stat-number">{{ totalProjects }}</span>
              <span class="stat-label">个项目</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ totalAmount }}</span>
              <span class="stat-label">总金额</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="container">
        <div class="filter-bar">
          <div class="filter-group">
            <label>项目类型：</label>
            <el-select v-model="filters.category" placeholder="全部类型" clearable @change="handleFilterChange">
              <el-option label="全部类型" value="" />
              <el-option label="医疗救助" value="medical" />
              <el-option label="教育助学" value="education" />
              <el-option label="灾难救援" value="disaster" />
              <el-option label="扶贫助困" value="poverty" />
              <el-option label="环境保护" value="environment" />
            </el-select>
          </div>

          <div class="filter-group">
            <label>项目状态：</label>
            <el-select v-model="filters.status" placeholder="全部状态" clearable @change="handleFilterChange">
              <el-option label="全部状态" value="" />
              <el-option label="进行中" value="active" />
              <el-option label="已完成" value="completed" />
              <el-option label="暂停" value="paused" />
            </el-select>
          </div>

          <div class="filter-group">
            <label>排序方式：</label>
            <el-select v-model="filters.sortBy" @change="handleFilterChange">
              <el-option label="最新发布" value="createTime" />
              <el-option label="筹款进度" value="progress" />
              <el-option label="目标金额" value="targetAmount" />
              <el-option label="已筹金额" value="currentAmount" />
            </el-select>
          </div>

          <div class="filter-actions">
            <el-button @click="resetFilters">重置</el-button>
            <el-button type="primary" @click="loadProjects">搜索</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 项目列表 -->
    <div class="projects-section">
      <div class="container">
        <div class="projects-grid" v-loading="loading">
          <div v-for="project in projects" :key="project.id" class="project-card"
            @click="goToProjectDetail(project.id)">
            <div class="project-image">
              <img v-if="getProjectImage(project)" :src="getProjectImage(project)" :alt="project.title" />
              <div v-else class="no-image-placeholder">
                <el-icon class="empty-icon">
                  <Picture />
                </el-icon>
                <span>暂无图片</span>
              </div>
              <div class="project-status" :class="project.status">
                {{ getStatusText(project.status) }}
              </div>
            </div>

            <div class="project-content">
              <div class="project-category">
                <el-tag :type="getCategoryType(project.category)" size="small">
                  {{ getCategoryText(project.category) }}
                </el-tag>
              </div>

              <h3 class="project-title">{{ project.title }}</h3>
              <p class="project-description">{{ project.description }}</p>

              <div class="project-progress">
                <div class="progress-bar">
                  <el-progress :percentage="getProgressPercentage(project)" :stroke-width="8" :show-text="false" />
                </div>
                <div class="progress-info">
                  <span class="current-amount">已筹：¥{{ formatAmount(project.currentAmount) }}</span>
                  <span class="target-amount">目标：¥{{ formatAmount(project.targetAmount) }}</span>
                </div>
              </div>

              <div class="project-meta">
                <div class="project-location">
                  <el-icon>
                    <Location />
                  </el-icon>
                  <span>{{ project.location || '全国' }}</span>
                </div>
                <div class="project-time">
                  <el-icon>
                    <Clock />
                  </el-icon>
                  <span>{{ formatDate(project.createTime) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && projects.length === 0" class="empty-state">
          <el-icon class="empty-icon">
            <DocumentRemove />
          </el-icon>
          <h3>暂无项目</h3>
          <p>当前筛选条件下没有找到相关项目</p>
          <el-button type="primary" @click="resetFilters">重置筛选</el-button>
        </div>

        <!-- 分页 -->
        <div v-if="total > 0" class="pagination-wrapper">
          <el-pagination v-model:current-page="pagination.page" v-model:page-size="pagination.size"
            :page-sizes="[12, 24, 48]" :total="total" layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange" @current-change="handlePageChange" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { getProjects, getProjectStats } from '@/api/project'
  import { ElMessage } from 'element-plus'
  import { Picture } from '@element-plus/icons-vue'

  const router = useRouter()

  // 响应式数据
  const loading = ref(false)
  const projects = ref([])
  const total = ref(0)
  const totalProjects = ref(0)
  const totalAmount = ref('0')

  // 筛选条件
  const filters = reactive({
    category: '',
    status: '',
    sortBy: 'createTime'
  })

  // 分页
  const pagination = reactive({
    page: 1,
    size: 12
  })

  // 加载项目列表
  const loadProjects = async () => {
    try {
      loading.value = true
      const params = {
        current: pagination.page,  // 修正参数名：page -> current
        size: pagination.size,
        category: filters.category || undefined,
        status: filters.status || undefined,
        keyword: filters.keyword || undefined
      }

      // 过滤掉空值
      Object.keys(params).forEach(key => {
        if (params[key] === undefined || params[key] === '') {
          delete params[key]
        }
      })

      console.log('请求参数:', params)
      const response = await getProjects(params)
      console.log('响应数据:', response)

      projects.value = response.records || []
      total.value = response.total || 0
    } catch (error) {
      console.error('加载项目失败:', error)
      ElMessage.error('加载项目失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 计算总金额（当前筹集金额）
  const calculateTotalAmount = (projectList) => {
    const total = projectList.reduce((sum, project) => sum + (project.currentAmount || 0), 0)
    return formatAmount(total)
  }

  // 筛选变化处理
  const handleFilterChange = () => {
    pagination.page = 1
    loadProjects()
  }

  // 重置筛选
  const resetFilters = () => {
    filters.category = ''
    filters.status = ''
    filters.sortBy = 'createTime'
    pagination.page = 1
    loadProjects()
  }

  // 分页处理
  const handlePageChange = (page) => {
    pagination.page = page
    loadProjects()
  }

  const handleSizeChange = (size) => {
    pagination.size = size
    pagination.page = 1
    loadProjects()
  }

  // 跳转到项目详情
  const goToProjectDetail = (projectId) => {
    router.push(`/projects/${projectId}`)
  }

  // 工具函数
  const getProgressPercentage = (project) => {
    if (!project.targetAmount || project.targetAmount === 0) return 0
    return Math.min(Math.round((project.currentAmount / project.targetAmount) * 100), 100)
  }

  const formatAmount = (amount) => {
    if (!amount) return '0'
    return amount.toLocaleString()
  }

  const formatDate = (dateString) => {
    if (!dateString) return ''
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  const getStatusText = (status) => {
    const statusMap = {
      active: '进行中',
      completed: '已完成',
      paused: '暂停'
    }
    return statusMap[status] || '未知'
  }

  const getCategoryText = (category) => {
    const categoryMap = {
      medical: '医疗救助',
      education: '教育助学',
      disaster: '灾难救援',
      poverty: '扶贫助困',
      environment: '环境保护'
    }
    return categoryMap[category] || '其他'
  }

  const getCategoryType = (category) => {
    const typeMap = {
      medical: 'danger',
      education: 'primary',
      disaster: 'warning',
      poverty: 'success',
      environment: 'info'
    }
    return typeMap[category] || ''
  }

  // 获取项目图片
  const getProjectImage = (project) => {
    // 优先使用coverImage字段（项目列表API返回的字段）
    if (project.coverImage) {
      return project.coverImage
    }

    // 如果有images数组，使用第一张图片
    if (project.images && project.images.length > 0) {
      return project.images[0]
    }

    // 如果有imageUrl字段
    if (project.imageUrl) {
      return project.imageUrl
    }

    // 默认占位图 - 返回null，让组件处理无图片情况
    return null
  }

  // 加载统计数据
  const loadStats = async () => {
    try {
      const statsResponse = await getProjectStats()
      console.log('统计数据:', statsResponse)

      if (statsResponse) {
        totalProjects.value = statsResponse.totalProjects || 0
        totalAmount.value = formatAmount(statsResponse.totalAmount || 0)
      }
    } catch (error) {
      console.error('加载统计数据失败:', error)
      // 不显示错误消息，使用默认值
    }
  }

  // 页面加载时获取数据
  onMounted(() => {
    loadStats()
    loadProjects()
  })
</script>

<style scoped>
  .projects-page {
    min-height: 100vh;
    background: #F7FAFC;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }

  /* 页面头部 */
  .page-header {
    background: linear-gradient(135deg, #E53E3E 0%, #C53030 100%);
    color: white;
    padding: 60px 0;
    position: relative;
    overflow: hidden;
  }

  .page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hearts" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><text x="10" y="15" text-anchor="middle" fill="rgba(255,255,255,0.1)" font-size="12">♥</text></pattern></defs><rect width="100" height="100" fill="url(%23hearts)"/></svg>');
    opacity: 0.3;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
  }

  .header-text h1 {
    font-size: 36px;
    margin-bottom: 12px;
    font-weight: bold;
  }

  .header-text p {
    font-size: 18px;
    opacity: 0.9;
  }

  .header-stats {
    display: flex;
    gap: 32px;
  }

  .stat-item {
    text-align: center;
  }

  .stat-number {
    display: block;
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 4px;
  }

  .stat-label {
    font-size: 14px;
    opacity: 0.8;
  }

  /* 筛选区域 */
  .filter-section {
    background: #FFFFFF;
    padding: 24px 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 32px;
  }

  .filter-bar {
    display: flex;
    align-items: center;
    gap: 24px;
    flex-wrap: wrap;
  }

  .filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .filter-group label {
    font-weight: 500;
    color: #2D3748;
    white-space: nowrap;
  }

  .filter-actions {
    margin-left: auto;
    display: flex;
    gap: 12px;
  }

  /* 项目列表 */
  .projects-section {
    padding: 32px 0;
  }

  .projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
  }

  .project-card {
    background: #FFFFFF;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
  }

  .project-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    border-color: #FED7D7;
  }

  .project-image {
    position: relative;
    height: 200px;
    overflow: hidden;
  }

  .project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .project-card:hover .project-image img {
    transform: scale(1.05);
  }

  /* 无图片占位符 */
  .project-image .no-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f9f9f9;
    color: #ccc;
    gap: 8px;
  }

  .project-image .empty-icon {
    font-size: 32px;
  }

  .project-image .no-image-placeholder span {
    font-size: 12px;
    color: #999;
  }

  .project-status {
    position: absolute;
    top: 12px;
    right: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    color: white;
    backdrop-filter: blur(4px);
  }

  .project-status.active {
    background: rgba(56, 161, 105, 0.9);
  }

  .project-status.completed {
    background: rgba(113, 128, 150, 0.9);
  }

  .project-status.paused {
    background: rgba(214, 158, 46, 0.9);
  }

  .project-content {
    padding: 20px;
  }

  .project-category {
    margin-bottom: 12px;
  }

  .project-title {
    font-size: 18px;
    font-weight: bold;
    color: #2D3748;
    margin-bottom: 8px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.4;
  }

  .project-description {
    color: #718096;
    margin-bottom: 16px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.5;
  }

  .project-progress {
    margin-bottom: 16px;
  }

  .progress-bar {
    margin-bottom: 8px;
  }

  .progress-info {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
  }

  .current-amount {
    color: #E53E3E;
    font-weight: 600;
  }

  .target-amount {
    color: #718096;
  }

  .project-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #718096;
  }

  .project-location,
  .project-time {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .project-location .el-icon,
  .project-time .el-icon {
    font-size: 16px;
  }

  /* 空状态 */
  .empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
  }

  .empty-icon {
    font-size: 64px;
    color: #CBD5E0;
    margin-bottom: 16px;
  }

  .empty-state h3 {
    font-size: 20px;
    color: #2D3748;
    margin-bottom: 8px;
  }

  .empty-state p {
    margin-bottom: 24px;
  }

  /* 分页 */
  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 40px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .header-content {
      flex-direction: column;
      text-align: center;
      gap: 24px;
    }

    .header-stats {
      gap: 24px;
    }

    .filter-bar {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;
    }

    .filter-actions {
      margin-left: 0;
      justify-content: center;
    }

    .projects-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .project-meta {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }
  }
</style>