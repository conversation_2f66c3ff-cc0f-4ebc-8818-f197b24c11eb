package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 救助项目实体类
 * 对应数据库表: projects
 */
@Data
@TableName("projects")
@Schema(description = "救助项目实体")
public class Project {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "项目ID", example = "1")
    private Long id;

    @TableField("title")
    @Schema(description = "项目标题", example = "小明白血病治疗救助")
    private String title;

    @TableField("description")
    @Schema(description = "项目描述", example = "小明今年8岁，患有急性白血病...")
    private String description;

    @TableField("category")
    @Schema(description = "项目分类: medical-医疗, education-教育, disaster-灾难, poverty-扶贫", example = "medical")
    private String category;

    @TableField("target_amount")
    @Schema(description = "目标金额", example = "300000.00")
    private BigDecimal targetAmount;

    @TableField("current_amount")
    @Schema(description = "当前筹集金额", example = "85000.00")
    private BigDecimal currentAmount;

    @TableField("images")
    @Schema(description = "项目图片(JSON格式存储多张图片URL)", example = "[\"https://example.com/image1.jpg\"]")
    private String images;

    @TableField("contact_info")
    @Schema(description = "联系信息", example = "联系人：小明妈妈，电话：138****1234")
    private String contactInfo;

    @TableField("status")
    @Schema(description = "状态: active-进行中, completed-已完成, closed-已关闭", example = "active")
    private String status;

    @TableField("admin_id")
    @Schema(description = "发布管理员ID", example = "1")
    private Long adminId;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 项目分类枚举
     */
    public enum Category {
        MEDICAL("medical", "医疗救助"),
        EDUCATION("education", "教育助学"),
        DISASTER("disaster", "灾难救援"),
        POVERTY("poverty", "扶贫助困");

        private final String code;
        private final String description;

        Category(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 项目状态枚举
     */
    public enum Status {
        ACTIVE("active", "进行中"),
        COMPLETED("completed", "已完成"),
        CLOSED("closed", "已关闭");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
