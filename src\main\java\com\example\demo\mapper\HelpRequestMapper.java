package com.example.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.entity.HelpRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 救助申请Mapper接口
 * 继承MyBatis Plus的BaseMapper，提供基础的CRUD操作
 */
@Mapper
public interface HelpRequestMapper extends BaseMapper<HelpRequest> {

    /**
     * 根据用户ID查询救助申请
     * @param userId 用户ID
     * @return 救助申请列表
     */
    @Select("SELECT * FROM help_requests WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<HelpRequest> findByUserId(@Param("userId") Long userId);

    /**
     * 根据状态查询救助申请
     * @param status 申请状态
     * @return 救助申请列表
     */
    @Select("SELECT * FROM help_requests WHERE status = #{status} ORDER BY create_time DESC")
    List<HelpRequest> findByStatus(@Param("status") String status);

    /**
     * 根据分类查询救助申请
     * @param category 申请分类
     * @param status 申请状态（可选）
     * @return 救助申请列表
     */
    @Select("<script>" +
            "SELECT * FROM help_requests WHERE category = #{category} " +
            "<if test='status != null and status != \"\"'> AND status = #{status} </if>" +
            "ORDER BY create_time DESC" +
            "</script>")
    List<HelpRequest> findByCategoryAndStatus(@Param("category") String category, @Param("status") String status);

    /**
     * 分页查询救助申请
     * @param page 分页参数
     * @param userId 用户ID（可选）
     * @param category 申请分类（可选）
     * @param status 申请状态（可选）
     * @param keyword 关键词搜索（标题或描述）
     * @return 分页救助申请列表
     */
    @Select("<script>" +
            "SELECT hr.*, u.username, u.real_name, u.phone as user_phone, " +
            "admin.username as admin_username, admin.real_name as admin_real_name " +
            "FROM help_requests hr " +
            "LEFT JOIN users u ON hr.user_id = u.id " +
            "LEFT JOIN users admin ON hr.admin_id = admin.id " +
            "WHERE 1=1 " +
            "<if test='userId != null'> AND hr.user_id = #{userId} </if>" +
            "<if test='category != null and category != \"\"'> AND hr.category = #{category} </if>" +
            "<if test='status != null and status != \"\"'> AND hr.status = #{status} </if>" +
            "<if test='keyword != null and keyword != \"\"'> " +
            "AND (hr.title LIKE CONCAT('%', #{keyword}, '%') OR hr.description LIKE CONCAT('%', #{keyword}, '%')) " +
            "</if>" +
            "ORDER BY hr.create_time DESC" +
            "</script>")
    IPage<HelpRequest> findHelpRequestsWithConditions(Page<HelpRequest> page,
                                                     @Param("userId") Long userId,
                                                     @Param("category") String category,
                                                     @Param("status") String status,
                                                     @Param("keyword") String keyword);

    /**
     * 查询待审核的申请（按紧急程度排序）
     * @param limit 限制数量
     * @return 待审核申请列表
     */
    @Select("SELECT hr.*, u.username, u.real_name " +
            "FROM help_requests hr " +
            "LEFT JOIN users u ON hr.user_id = u.id " +
            "WHERE hr.status = 'pending' " +
            "ORDER BY hr.amount_needed DESC, hr.create_time ASC " +
            "LIMIT #{limit}")
    List<HelpRequest> findPendingRequests(@Param("limit") Integer limit);

    /**
     * 查询最近通过的申请
     * @param limit 限制数量
     * @return 最近通过的申请列表
     */
    @Select("SELECT hr.*, u.username, u.real_name " +
            "FROM help_requests hr " +
            "LEFT JOIN users u ON hr.user_id = u.id " +
            "WHERE hr.status = 'approved' " +
            "ORDER BY hr.update_time DESC " +
            "LIMIT #{limit}")
    List<HelpRequest> findRecentApprovedRequests(@Param("limit") Integer limit);

    /**
     * 统计救助申请数量
     * @param category 申请分类（可选）
     * @param status 申请状态（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 申请数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM help_requests WHERE 1=1 " +
            "<if test='category != null and category != \"\"'> AND category = #{category} </if>" +
            "<if test='status != null and status != \"\"'> AND status = #{status} </if>" +
            "<if test='startTime != null'> AND create_time >= #{startTime} </if>" +
            "<if test='endTime != null'> AND create_time &lt;= #{endTime} </if>" +
            "</script>")
    Long countHelpRequests(@Param("category") String category,
                          @Param("status") String status,
                          @Param("startTime") LocalDateTime startTime,
                          @Param("endTime") LocalDateTime endTime);

    /**
     * 统计救助申请总金额
     * @param status 申请状态（可选）
     * @return 总金额
     */
    @Select("<script>" +
            "SELECT COALESCE(SUM(amount_needed), 0) FROM help_requests WHERE 1=1 " +
            "<if test='status != null and status != \"\"'> AND status = #{status} </if>" +
            "</script>")
    BigDecimal sumAmountNeeded(@Param("status") String status);

    /**
     * 查询用户申请次数
     * @param userId 用户ID
     * @return 申请次数
     */
    @Select("SELECT COUNT(*) FROM help_requests WHERE user_id = #{userId}")
    Long countRequestsByUser(@Param("userId") Long userId);

    /**
     * 查询管理员处理的申请数量
     * @param adminId 管理员ID
     * @return 处理数量
     */
    @Select("SELECT COUNT(*) FROM help_requests WHERE admin_id = #{adminId}")
    Long countRequestsByAdmin(@Param("adminId") Long adminId);

    /**
     * 更新申请状态和管理员回复
     * @param id 申请ID
     * @param status 新状态
     * @param adminResponse 管理员回复
     * @param adminId 管理员ID
     * @return 影响行数
     */
    @Update("UPDATE help_requests SET status = #{status}, admin_response = #{adminResponse}, " +
            "admin_id = #{adminId}, update_time = NOW() WHERE id = #{id}")
    int updateRequestStatus(@Param("id") Long id,
                           @Param("status") String status,
                           @Param("adminResponse") String adminResponse,
                           @Param("adminId") Long adminId);

    /**
     * 查询申请统计信息（按分类）
     * @return 分类统计列表
     */
    @Select("SELECT category, " +
            "COUNT(*) as total_count, " +
            "SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count, " +
            "SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_count, " +
            "SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_count, " +
            "COALESCE(SUM(amount_needed), 0) as total_amount " +
            "FROM help_requests GROUP BY category")
    List<Object> getRequestStatsByCategory();

    /**
     * 查询申请统计信息（按状态）
     * @return 状态统计列表
     */
    @Select("SELECT status, COUNT(*) as count, COALESCE(SUM(amount_needed), 0) as total_amount " +
            "FROM help_requests GROUP BY status")
    List<Object> getRequestStatsByStatus();

    /**
     * 查询每日申请统计（最近30天）
     * @return 每日申请统计列表
     */
    @Select("SELECT DATE(create_time) as request_date, " +
            "COUNT(*) as count, " +
            "COALESCE(SUM(amount_needed), 0) as total_amount " +
            "FROM help_requests " +
            "WHERE create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY) " +
            "GROUP BY DATE(create_time) " +
            "ORDER BY request_date DESC")
    List<Object> getDailyRequestStats();

    /**
     * 查询处理效率统计（平均处理时间）
     * @param adminId 管理员ID（可选）
     * @return 处理效率统计
     */
    @Select("<script>" +
            "SELECT " +
            "COUNT(*) as processed_count, " +
            "AVG(TIMESTAMPDIFF(HOUR, create_time, update_time)) as avg_process_hours " +
            "FROM help_requests " +
            "WHERE status IN ('approved', 'rejected') " +
            "<if test='adminId != null'> AND admin_id = #{adminId} </if>" +
            "</script>")
    Object getProcessEfficiencyStats(@Param("adminId") Long adminId);
}
