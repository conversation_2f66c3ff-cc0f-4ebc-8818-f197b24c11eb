package com.example.demo.exception;

/**
 * 数据验证异常
 */
public class ValidationException extends BusinessException {

    public ValidationException() {
        super(ErrorCode.PARAM_ERROR);
    }

    public ValidationException(String message) {
        super(ErrorCode.PARAM_ERROR, message);
    }

    public ValidationException(String field, String message) {
        super(ErrorCode.PARAM_ERROR, String.format("字段 %s 验证失败：%s", field, message));
    }

    public ValidationException(ErrorCode errorCode, String message) {
        super(errorCode, message);
    }

    public ValidationException(Throwable cause) {
        super(ErrorCode.PARAM_ERROR, cause);
    }

    /**
     * 参数缺失异常
     */
    public static ValidationException missingParam(String paramName) {
        return new ValidationException(ErrorCode.PARAM_MISSING, "缺少必要参数：" + paramName);
    }

    /**
     * 参数格式错误异常
     */
    public static ValidationException formatError(String paramName, String expectedFormat) {
        return new ValidationException(ErrorCode.PARAM_FORMAT_ERROR, 
                String.format("参数 %s 格式错误，期望格式：%s", paramName, expectedFormat));
    }

    /**
     * 参数长度错误异常
     */
    public static ValidationException lengthError(String paramName, int minLength, int maxLength) {
        return new ValidationException(ErrorCode.PARAM_LENGTH_ERROR, 
                String.format("参数 %s 长度错误，应在 %d-%d 之间", paramName, minLength, maxLength));
    }

    /**
     * 参数范围错误异常
     */
    public static ValidationException rangeError(String paramName, Object minValue, Object maxValue) {
        return new ValidationException(ErrorCode.PARAM_RANGE_ERROR, 
                String.format("参数 %s 超出范围，应在 %s-%s 之间", paramName, minValue, maxValue));
    }
}
