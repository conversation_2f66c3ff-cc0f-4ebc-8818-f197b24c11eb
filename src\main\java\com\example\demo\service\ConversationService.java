package com.example.demo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.entity.Conversation;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI对话服务接口
 * 定义AI对话相关的业务操作
 */
public interface ConversationService {

    /**
     * 创建对话记录
     * @param conversation 对话信息
     * @return 创建成功的对话记录
     */
    Conversation createConversation(Conversation conversation);

    /**
     * 处理AI对话
     * @param userId 用户ID（可为空，支持游客）
     * @param sessionId 会话ID
     * @param message 用户消息
     * @param conversationType 对话类型
     * @return AI回复内容
     */
    String processAIConversation(Long userId, String sessionId, String message, String conversationType);

    /**
     * 根据ID查询对话记录
     * @param id 对话ID
     * @return 对话记录
     */
    Conversation findById(Long id);

    /**
     * 根据用户ID查询对话记录
     * @param userId 用户ID
     * @return 对话记录列表
     */
    List<Conversation> findByUserId(Long userId);

    /**
     * 根据会话ID查询对话记录
     * @param sessionId 会话ID
     * @return 对话记录列表
     */
    List<Conversation> findBySessionId(String sessionId);

    /**
     * 根据对话类型查询对话记录
     * @param conversationType 对话类型
     * @param limit 限制数量
     * @return 对话记录列表
     */
    List<Conversation> findByConversationType(String conversationType, Integer limit);

    /**
     * 分页查询对话记录
     * @param page 分页参数
     * @param userId 用户ID
     * @param conversationType 对话类型
     * @param keyword 关键词搜索
     * @return 分页对话记录列表
     */
    IPage<Conversation> findConversationsWithConditions(Page<Conversation> page, Long userId, 
                                                       String conversationType, String keyword);

    /**
     * 查询用户最近的会话ID列表
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 会话ID列表
     */
    List<String> findRecentSessionsByUser(Long userId, Integer limit);

    /**
     * 获取对话类型统计
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 对话类型统计列表
     */
    List<Object> getConversationTypeStats(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计对话数量
     * @param conversationType 对话类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 对话数量
     */
    Long countConversations(String conversationType, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计活跃用户数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 活跃用户数量
     */
    Long countActiveUsers(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询热门问题
     * @param limit 限制数量
     * @return 热门问题列表
     */
    List<Object> getPopularQuestions(Integer limit);

    /**
     * 获取每日对话统计
     * @return 每日对话统计列表
     */
    List<Object> getDailyConversationStats();

    /**
     * 获取对话概览统计
     * @return 对话概览统计
     */
    Object getConversationOverviewStats();

    /**
     * 获取用户对话摘要信息
     * @param userId 用户ID
     * @return 对话摘要
     */
    Object getUserConversationSummary(Long userId);

    /**
     * 生成新的会话ID
     * @param userId 用户ID（可为空）
     * @return 会话ID
     */
    String generateSessionId(Long userId);

    /**
     * 删除指定时间之前的对话记录
     * @param beforeTime 指定时间
     * @return 删除的记录数
     */
    int deleteConversationsBefore(LocalDateTime beforeTime);

    /**
     * 获取AI欢迎消息
     * @param conversationType 对话类型
     * @return 欢迎消息
     */
    String getWelcomeMessage(String conversationType);

    /**
     * 获取智能回复建议
     * @param message 用户消息
     * @param conversationType 对话类型
     * @return 回复建议列表
     */
    List<String> getSmartReplySuggestions(String message, String conversationType);

    /**
     * 分析用户意图
     * @param message 用户消息
     * @return 意图分析结果
     */
    Object analyzeUserIntent(String message);

    /**
     * 获取相关项目推荐
     * @param message 用户消息
     * @param limit 限制数量
     * @return 推荐项目列表
     */
    List<Object> getRelatedProjectRecommendations(String message, Integer limit);

    /**
     * 检查对话是否需要人工介入
     * @param conversationId 对话ID
     * @return 是否需要人工介入
     */
    boolean needsHumanIntervention(Long conversationId);

    /**
     * 标记对话为已解决
     * @param conversationId 对话ID
     * @param adminId 管理员ID
     * @return 是否标记成功
     */
    boolean markConversationResolved(Long conversationId, Long adminId);

    /**
     * 获取对话满意度评分
     * @param conversationId 对话ID
     * @param rating 评分（1-5）
     * @param feedback 反馈内容
     * @return 是否评分成功
     */
    boolean rateConversation(Long conversationId, Integer rating, String feedback);
}
