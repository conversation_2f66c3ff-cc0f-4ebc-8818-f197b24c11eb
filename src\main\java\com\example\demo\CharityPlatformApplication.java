package com.example.demo;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
@MapperScan("com.example.demo.mapper")
public class CharityPlatformApplication {

    public static void main(String[] args) {
        SpringApplication.run(CharityPlatformApplication.class, args);
        System.out.println("=================================");
        System.out.println("🎉 爱心救助平台启动成功！");
        System.out.println("📍 访问地址: http://localhost:8080/api");
        System.out.println("📚 API文档: http://localhost:8080/swagger-ui/index.html ");
        System.out.println("🧪 测试接口: http://localhost:8080/api/test/hello");
        System.out.println("💖 健康检查: http://localhost:8080/api/test/health");
        System.out.println("💾 数据库功能已启用，连接MySQL数据库");
        System.out.println("⚙️  Spring Boot 3.1.5 + MyBatis Plus ******* + JDK 21");
        System.out.println("=================================");
    }

}
