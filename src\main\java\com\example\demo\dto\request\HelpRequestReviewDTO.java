package com.example.demo.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 救助申请审核请求DTO
 */
@Schema(description = "救助申请审核请求")
public class HelpRequestReviewDTO {

    @Schema(description = "申请ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "申请ID不能为空")
    private Long requestId;

    @Schema(description = "审核状态", example = "approved", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "审核状态不能为空")
    private String status;

    @Schema(description = "管理员回复", example = "经审核，情况属实，已安排志愿者跟进。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "管理员回复不能为空")
    @Size(max = 1000, message = "管理员回复长度不能超过1000字符")
    private String adminResponse;

    // Getters and Setters
    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAdminResponse() {
        return adminResponse;
    }

    public void setAdminResponse(String adminResponse) {
        this.adminResponse = adminResponse;
    }
}
