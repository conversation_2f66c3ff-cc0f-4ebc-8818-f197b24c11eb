package com.example.demo.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.entity.Donation;
import com.example.demo.entity.Project;
import com.example.demo.entity.User;
import com.example.demo.mapper.DonationMapper;
import com.example.demo.service.DonationService;
import com.example.demo.service.ProjectService;
import com.example.demo.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 捐赠服务实现类
 * 实现捐赠相关的业务逻辑
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class DonationServiceImpl implements DonationService {

    @Autowired
    private DonationMapper donationMapper;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private UserService userService;

    @Override
    public Donation createDonation(Donation donation) {
        log.info("创建捐赠记录: userId={}, projectId={}, amount={}", 
                donation.getUserId(), donation.getProjectId(), donation.getAmount());
        
        // 业务校验
        validateDonationForCreate(donation);
        
        // 检查用户是否可以捐赠
        if (!canUserDonate(donation.getUserId(), donation.getProjectId(), donation.getAmount())) {
            throw new RuntimeException("当前无法进行捐赠");
        }
        
        // 设置默认值
        if (donation.getStatus() == null) {
            donation.setStatus(Donation.Status.PENDING.getCode());
        }
        if (donation.getIsAnonymous() == null) {
            donation.setIsAnonymous(Donation.Anonymous.NO.getCode());
        }
        if (donation.getPaymentMethod() == null) {
            donation.setPaymentMethod(Donation.PaymentMethod.ALIPAY.getCode());
        }
        
        // 保存捐赠记录
        int result = donationMapper.insert(donation);
        if (result > 0) {
            log.info("捐赠记录创建成功: {}", donation.getId());
            return donation;
        } else {
            throw new RuntimeException("捐赠记录创建失败");
        }
    }

    @Override
    public boolean processDonationPayment(Long donationId, String transactionId) {
        log.info("处理捐赠支付: donationId={}, transactionId={}", donationId, transactionId);
        
        if (donationId == null || donationId <= 0) {
            throw new RuntimeException("捐赠ID不能为空");
        }
        
        if (!StringUtils.hasText(transactionId)) {
            throw new RuntimeException("交易流水号不能为空");
        }
        
        Donation donation = donationMapper.selectById(donationId);
        if (donation == null) {
            throw new RuntimeException("捐赠记录不存在");
        }
        
        if (!Donation.Status.PENDING.getCode().equals(donation.getStatus())) {
            throw new RuntimeException("捐赠状态不正确，无法处理支付");
        }
        
        // 更新捐赠状态和交易流水号
        donation.setStatus(Donation.Status.SUCCESS.getCode());
        donation.setTransactionId(transactionId);
        
        int result = donationMapper.updateById(donation);
        if (result > 0) {
            // 更新项目筹集金额
            projectService.updateCurrentAmount(donation.getProjectId(), donation.getAmount());
            
            log.info("捐赠支付处理成功: donationId={}", donationId);
            return true;
        } else {
            throw new RuntimeException("捐赠支付处理失败");
        }
    }

    @Override
    public boolean cancelDonation(Long donationId, Long userId) {
        log.info("取消捐赠: donationId={}, userId={}", donationId, userId);
        
        if (donationId == null || donationId <= 0) {
            throw new RuntimeException("捐赠ID不能为空");
        }
        
        if (userId == null || userId <= 0) {
            throw new RuntimeException("用户ID不能为空");
        }
        
        Donation donation = donationMapper.selectById(donationId);
        if (donation == null) {
            throw new RuntimeException("捐赠记录不存在");
        }
        
        // 检查权限
        if (!hasDonationPermission(donationId, userId)) {
            throw new RuntimeException("没有权限取消此捐赠");
        }
        
        // 只有待支付状态的捐赠可以取消
        if (!Donation.Status.PENDING.getCode().equals(donation.getStatus())) {
            throw new RuntimeException("只有待支付状态的捐赠可以取消");
        }
        
        donation.setStatus(Donation.Status.FAILED.getCode());
        int result = donationMapper.updateById(donation);
        
        if (result > 0) {
            log.info("捐赠取消成功: donationId={}", donationId);
            return true;
        } else {
            throw new RuntimeException("捐赠取消失败");
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Donation findById(Long id) {
        if (id == null || id <= 0) {
            return null;
        }
        return donationMapper.selectById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Donation> findByUserId(Long userId) {
        if (userId == null || userId <= 0) {
            throw new RuntimeException("用户ID不能为空");
        }
        return donationMapper.findByUserId(userId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Donation> findByProjectId(Long projectId) {
        if (projectId == null || projectId <= 0) {
            throw new RuntimeException("项目ID不能为空");
        }
        return donationMapper.findByProjectId(projectId);
    }

    @Override
    @Transactional(readOnly = true)
    public IPage<Donation> findDonationsWithConditions(Page<Donation> page, Long userId, Long projectId,
                                                      String status, String paymentMethod,
                                                      LocalDateTime startTime, LocalDateTime endTime) {
        return donationMapper.findDonationsWithConditions(page, userId, projectId, status, paymentMethod, startTime, endTime);
    }

    @Override
    @Transactional(readOnly = true)
    public BigDecimal getUserTotalDonationAmount(Long userId) {
        if (userId == null || userId <= 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal amount = donationMapper.sumDonationAmountByUser(userId);
        return amount != null ? amount : BigDecimal.ZERO;
    }

    @Override
    @Transactional(readOnly = true)
    public BigDecimal getProjectTotalDonationAmount(Long projectId) {
        if (projectId == null || projectId <= 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal amount = donationMapper.sumDonationAmountByProject(projectId);
        return amount != null ? amount : BigDecimal.ZERO;
    }

    @Override
    @Transactional(readOnly = true)
    public Long getProjectDonorCount(Long projectId) {
        if (projectId == null || projectId <= 0) {
            return 0L;
        }
        Long count = donationMapper.countDonorsByProject(projectId);
        return count != null ? count : 0L;
    }

    @Override
    @Transactional(readOnly = true)
    public List<Donation> findRecentDonations(Integer limit, Boolean isAnonymous) {
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        return donationMapper.findRecentDonations(limit, isAnonymous);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Donation> findLargeDonations(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        return donationMapper.findLargeDonations(limit);
    }

    @Override
    @Transactional(readOnly = true)
    public Long countDonations(String status, LocalDateTime startTime, LocalDateTime endTime) {
        Long count = donationMapper.countDonations(status, startTime, endTime);
        return count != null ? count : 0L;
    }

    @Override
    @Transactional(readOnly = true)
    public BigDecimal sumDonationAmount(String status, LocalDateTime startTime, LocalDateTime endTime) {
        BigDecimal amount = donationMapper.sumDonationAmount(status, startTime, endTime);
        return amount != null ? amount : BigDecimal.ZERO;
    }

    @Override
    @Transactional
    public boolean updateDonationStatus(Long donationId, String status) {
        log.info("更新捐赠状态: donationId={}, status={}", donationId, status);

        if (donationId == null || donationId <= 0) {
            throw new RuntimeException("捐赠ID不能为空");
        }

        if (!StringUtils.hasText(status)) {
            throw new RuntimeException("状态不能为空");
        }

        if (!isValidDonationStatus(status)) {
            throw new RuntimeException("无效的捐赠状态");
        }

        // 获取原始捐赠记录
        Donation originalDonation = donationMapper.selectById(donationId);
        if (originalDonation == null) {
            throw new RuntimeException("捐赠记录不存在");
        }

        String oldStatus = originalDonation.getStatus();

        // 更新捐赠状态
        int result = donationMapper.updateDonationStatus(donationId, status);
        if (result > 0) {
            log.info("捐赠状态更新成功: donationId={}, oldStatus={}, newStatus={}", donationId, oldStatus, status);

            // 更新项目金额
            updateProjectAmountOnStatusChange(originalDonation, oldStatus, status);

            return true;
        } else {
            throw new RuntimeException("捐赠状态更新失败");
        }
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasDonationPermission(Long donationId, Long userId) {
        if (donationId == null || donationId <= 0 || userId == null || userId <= 0) {
            return false;
        }
        
        Donation donation = donationMapper.selectById(donationId);
        if (donation == null) {
            return false;
        }
        
        // 用户只能操作自己的捐赠记录
        return donation.getUserId().equals(userId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Object> getDonationStatsByPaymentMethod() {
        return donationMapper.getDonationStatsByPaymentMethod();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Object> getDailyDonationStats() {
        return donationMapper.getDailyDonationStats();
    }

    @Override
    @Transactional(readOnly = true)
    public Object getDonationOverviewStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 总捐赠数量
        Long totalDonations = countDonations(null, null, null);
        stats.put("totalDonations", totalDonations);
        
        // 成功捐赠数量
        Long successDonations = countDonations(Donation.Status.SUCCESS.getCode(), null, null);
        stats.put("successDonations", successDonations);
        
        // 待支付捐赠数量
        Long pendingDonations = countDonations(Donation.Status.PENDING.getCode(), null, null);
        stats.put("pendingDonations", pendingDonations);
        
        // 失败捐赠数量
        Long failedDonations = countDonations(Donation.Status.FAILED.getCode(), null, null);
        stats.put("failedDonations", failedDonations);
        
        // 总捐赠金额
        BigDecimal totalAmount = sumDonationAmount(null, null, null);
        stats.put("totalAmount", totalAmount);
        
        // 成功捐赠金额
        BigDecimal successAmount = sumDonationAmount(Donation.Status.SUCCESS.getCode(), null, null);
        stats.put("successAmount", successAmount);
        
        // 今日捐赠统计
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        LocalDateTime todayEnd = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
        
        Long todayDonations = countDonations(Donation.Status.SUCCESS.getCode(), todayStart, todayEnd);
        BigDecimal todayAmount = sumDonationAmount(Donation.Status.SUCCESS.getCode(), todayStart, todayEnd);
        
        stats.put("todayDonations", todayDonations);
        stats.put("todayAmount", todayAmount);
        
        return stats;
    }

    @Override
    @Transactional(readOnly = true)
    public Object generateDonationCertificate(Long donationId) {
        if (donationId == null || donationId <= 0) {
            throw new RuntimeException("捐赠ID不能为空");
        }
        
        Donation donation = donationMapper.selectById(donationId);
        if (donation == null) {
            throw new RuntimeException("捐赠记录不存在");
        }
        
        if (!Donation.Status.SUCCESS.getCode().equals(donation.getStatus())) {
            throw new RuntimeException("只有成功的捐赠才能生成证书");
        }
        
        Map<String, Object> certificate = new HashMap<>();
        certificate.put("donationId", donation.getId());
        certificate.put("amount", donation.getAmount());
        certificate.put("donationTime", donation.getDonationTime());
        certificate.put("transactionId", donation.getTransactionId());
        
        // 获取项目信息
        Project project = projectService.findById(donation.getProjectId());
        if (project != null) {
            certificate.put("projectTitle", project.getTitle());
        }
        
        // 获取用户信息（如果不是匿名捐赠）
        if (donation.getIsAnonymous() == Donation.Anonymous.NO.getCode()) {
            User user = userService.findById(donation.getUserId());
            if (user != null) {
                certificate.put("donorName", user.getRealName() != null ? user.getRealName() : user.getUsername());
            }
        } else {
            certificate.put("donorName", "匿名捐赠者");
        }
        
        certificate.put("certificateNo", "CERT" + donation.getId() + System.currentTimeMillis());
        
        return certificate;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canUserDonate(Long userId, Long projectId, BigDecimal amount) {
        if (userId == null || userId <= 0 || projectId == null || projectId <= 0 || 
            amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        
        // 检查用户是否存在且状态正常
        User user = userService.findById(userId);
        if (user == null || user.getStatus() != User.Status.NORMAL.getCode()) {
            return false;
        }
        
        // 检查项目是否可以接受捐赠
        if (!projectService.canAcceptDonation(projectId)) {
            return false;
        }
        
        // 检查捐赠金额是否在合理范围内
        if (amount.compareTo(new BigDecimal("0.01")) < 0 || amount.compareTo(new BigDecimal("1000000")) > 0) {
            return false;
        }
        
        return true;
    }

    @Override
    @Transactional(readOnly = true)
    public Object getUserDonationStats(Long userId) {
        if (userId == null || userId <= 0) {
            throw new RuntimeException("用户ID不能为空");
        }
        
        Map<String, Object> stats = new HashMap<>();
        
        // 总捐赠金额
        BigDecimal totalAmount = getUserTotalDonationAmount(userId);
        stats.put("totalAmount", totalAmount);
        
        // 捐赠次数
        List<Donation> donations = findByUserId(userId);
        long successCount = donations.stream()
                .filter(d -> Donation.Status.SUCCESS.getCode().equals(d.getStatus()))
                .count();
        stats.put("donationCount", successCount);
        
        // 参与项目数量
        long projectCount = donations.stream()
                .filter(d -> Donation.Status.SUCCESS.getCode().equals(d.getStatus()))
                .map(Donation::getProjectId)
                .distinct()
                .count();
        stats.put("projectCount", projectCount);
        
        // 最近一次捐赠时间
        donations.stream()
                .filter(d -> Donation.Status.SUCCESS.getCode().equals(d.getStatus()))
                .map(Donation::getDonationTime)
                .max(LocalDateTime::compareTo)
                .ifPresent(lastTime -> stats.put("lastDonationTime", lastTime));
        
        return stats;
    }

    @Override
    @Transactional(readOnly = true)
    public Object getProjectDonationStats(Long projectId) {
        if (projectId == null || projectId <= 0) {
            throw new RuntimeException("项目ID不能为空");
        }
        
        Map<String, Object> stats = new HashMap<>();
        
        // 总捐赠金额
        BigDecimal totalAmount = getProjectTotalDonationAmount(projectId);
        stats.put("totalAmount", totalAmount);
        
        // 捐赠人数
        Long donorCount = getProjectDonorCount(projectId);
        stats.put("donorCount", donorCount);
        
        // 捐赠次数
        List<Donation> donations = findByProjectId(projectId);
        stats.put("donationCount", donations.size());
        
        // 平均捐赠金额
        if (donorCount > 0) {
            BigDecimal avgAmount = totalAmount.divide(BigDecimal.valueOf(donorCount), 2, BigDecimal.ROUND_HALF_UP);
            stats.put("avgAmount", avgAmount);
        } else {
            stats.put("avgAmount", BigDecimal.ZERO);
        }
        
        return stats;
    }

    @Override
    public boolean refundDonation(Long donationId, String reason, Long adminId) {
        log.info("处理捐赠退款: donationId={}, reason={}, adminId={}", donationId, reason, adminId);
        
        if (donationId == null || donationId <= 0) {
            throw new RuntimeException("捐赠ID不能为空");
        }
        
        if (!StringUtils.hasText(reason)) {
            throw new RuntimeException("退款原因不能为空");
        }
        
        if (adminId == null || adminId <= 0) {
            throw new RuntimeException("管理员ID不能为空");
        }
        
        // 检查管理员权限
        User admin = userService.findById(adminId);
        if (admin == null || !User.Role.ADMIN.getCode().equals(admin.getRole())) {
            throw new RuntimeException("只有管理员可以处理退款");
        }
        
        Donation donation = donationMapper.selectById(donationId);
        if (donation == null) {
            throw new RuntimeException("捐赠记录不存在");
        }
        
        if (!Donation.Status.SUCCESS.getCode().equals(donation.getStatus())) {
            throw new RuntimeException("只有成功的捐赠才能退款");
        }
        
        // 更新捐赠状态为失败
        donation.setStatus(Donation.Status.FAILED.getCode());
        int result = donationMapper.updateById(donation);
        
        if (result > 0) {
            // 减少项目筹集金额
            projectService.reduceCurrentAmount(donation.getProjectId(), donation.getAmount());

            log.info("捐赠退款处理成功: donationId={}", donationId);
            return true;
        } else {
            throw new RuntimeException("捐赠退款处理失败");
        }
    }

    @Override
    public boolean batchUpdateDonationStatus(List<Long> donationIds, String status) {
        log.info("批量更新捐赠状态: donationIds={}, status={}", donationIds, status);
        
        if (donationIds == null || donationIds.isEmpty()) {
            throw new RuntimeException("捐赠ID列表不能为空");
        }
        
        if (!StringUtils.hasText(status)) {
            throw new RuntimeException("状态不能为空");
        }
        
        if (!isValidDonationStatus(status)) {
            throw new RuntimeException("无效的捐赠状态");
        }
        
        int successCount = 0;
        for (Long donationId : donationIds) {
            try {
                if (updateDonationStatus(donationId, status)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("更新捐赠状态失败: donationId={}, error={}", donationId, e.getMessage());
            }
        }
        
        log.info("批量更新捐赠状态完成: 成功{}个，总共{}个", successCount, donationIds.size());
        return successCount > 0;
    }

    /**
     * 验证捐赠创建信息
     */
    private void validateDonationForCreate(Donation donation) {
        if (donation == null) {
            throw new RuntimeException("捐赠信息不能为空");
        }
        
        if (donation.getUserId() == null || donation.getUserId() <= 0) {
            throw new RuntimeException("用户ID不能为空");
        }
        
        if (donation.getProjectId() == null || donation.getProjectId() <= 0) {
            throw new RuntimeException("项目ID不能为空");
        }
        
        if (donation.getAmount() == null || donation.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("捐赠金额必须大于0");
        }
        
        if (donation.getAmount().compareTo(new BigDecimal("1000000")) > 0) {
            throw new RuntimeException("单次捐赠金额不能超过100万");
        }
        
        if (StringUtils.hasText(donation.getPaymentMethod()) && 
            !isValidPaymentMethod(donation.getPaymentMethod())) {
            throw new RuntimeException("无效的支付方式");
        }
    }

    /**
     * 验证支付方式是否有效
     */
    private boolean isValidPaymentMethod(String paymentMethod) {
        for (Donation.PaymentMethod method : Donation.PaymentMethod.values()) {
            if (method.getCode().equals(paymentMethod)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 验证捐赠状态是否有效
     */
    private boolean isValidDonationStatus(String status) {
        for (Donation.Status stat : Donation.Status.values()) {
            if (stat.getCode().equals(status)) {
                return true;
            }
        }
        return false;
    }

    @Override
    @Transactional(readOnly = true)
    public Object getTodayDonationStats() {
        Map<String, Object> stats = new HashMap<>();

        // 简化实现，实际应该查询今天的捐赠统计
        BigDecimal totalAmount = donationMapper.sumDonationAmount(null, null, null);
        Long totalCount = donationMapper.countDonations(null, null, null);

        // 模拟今日数据（实际应该根据日期筛选）
        stats.put("todayAmount", totalAmount != null ? totalAmount.divide(new BigDecimal("30")) : BigDecimal.ZERO);
        stats.put("todayCount", totalCount != null ? totalCount / 30 : 0L);

        return stats;
    }

    /**
     * 根据捐赠状态变化更新项目金额
     */
    private void updateProjectAmountOnStatusChange(Donation donation, String oldStatus, String newStatus) {
        log.info("处理项目金额更新: donationId={}, projectId={}, amount={}, oldStatus={}, newStatus={}",
                donation.getId(), donation.getProjectId(), donation.getAmount(), oldStatus, newStatus);

        try {
            // 当状态从非success变为success时，增加项目金额
            if (!"success".equals(oldStatus) && "success".equals(newStatus)) {
                log.info("捐赠状态变为成功，增加项目金额: projectId={}, amount={}",
                        donation.getProjectId(), donation.getAmount());

                // 直接增加金额（updateCurrentAmount方法本身就是增加）
                projectService.updateCurrentAmount(donation.getProjectId(), donation.getAmount());
                log.info("项目金额增加成功: projectId={}, addedAmount={}",
                        donation.getProjectId(), donation.getAmount());
            }

            // 当状态从success变为非success时，减少项目金额
            if ("success".equals(oldStatus) && !"success".equals(newStatus)) {
                log.info("捐赠状态从成功变为其他，减少项目金额: projectId={}, amount={}",
                        donation.getProjectId(), donation.getAmount());

                // 使用专门的减少金额方法
                projectService.reduceCurrentAmount(donation.getProjectId(), donation.getAmount());
                log.info("项目金额减少成功: projectId={}, reducedAmount={}",
                        donation.getProjectId(), donation.getAmount());
            }
        } catch (Exception e) {
            log.error("更新项目金额失败: donationId={}, projectId={}, error={}",
                    donation.getId(), donation.getProjectId(), e.getMessage(), e);
            // 不抛出异常，避免影响捐赠状态更新
        }
    }
}
