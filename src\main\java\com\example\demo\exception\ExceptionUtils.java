package com.example.demo.exception;

import org.springframework.util.StringUtils;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.UUID;

/**
 * 异常处理工具类
 */
public class ExceptionUtils {

    /**
     * 生成请求追踪ID
     */
    public static String generateTraceId() {
        return "trace_" + UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }

    /**
     * 获取异常的完整堆栈信息
     */
    public static String getStackTrace(Throwable throwable) {
        if (throwable == null) {
            return "";
        }
        
        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);
        throwable.printStackTrace(printWriter);
        return stringWriter.toString();
    }

    /**
     * 获取异常的根本原因
     */
    public static Throwable getRootCause(Throwable throwable) {
        if (throwable == null) {
            return null;
        }
        
        Throwable rootCause = throwable;
        while (rootCause.getCause() != null && rootCause.getCause() != rootCause) {
            rootCause = rootCause.getCause();
        }
        return rootCause;
    }

    /**
     * 获取异常的根本原因消息
     */
    public static String getRootCauseMessage(Throwable throwable) {
        Throwable rootCause = getRootCause(throwable);
        return rootCause != null ? rootCause.getMessage() : "";
    }

    /**
     * 判断是否为业务异常
     */
    public static boolean isBusinessException(Throwable throwable) {
        return throwable instanceof BusinessException;
    }

    /**
     * 判断是否为系统异常
     */
    public static boolean isSystemException(Throwable throwable) {
        return !isBusinessException(throwable);
    }

    /**
     * 获取友好的错误消息
     */
    public static String getFriendlyMessage(Throwable throwable) {
        if (throwable == null) {
            return "未知错误";
        }
        
        if (throwable instanceof BusinessException) {
            BusinessException businessException = (BusinessException) throwable;
            return StringUtils.hasText(businessException.getMessage()) ? 
                    businessException.getMessage() : businessException.getErrorCode().getMessage();
        }
        
        // 对于系统异常，返回通用错误消息
        return "系统繁忙，请稍后重试";
    }

    /**
     * 获取错误码
     */
    public static int getErrorCode(Throwable throwable) {
        if (throwable instanceof BusinessException) {
            return ((BusinessException) throwable).getCode();
        }
        return ErrorCode.SYSTEM_ERROR.getCode();
    }

    /**
     * 判断异常是否需要记录详细日志
     */
    public static boolean shouldLogDetail(Throwable throwable) {
        // 业务异常通常不需要记录详细堆栈
        if (throwable instanceof BusinessException) {
            BusinessException businessException = (BusinessException) throwable;
            // 只有系统级错误才记录详细日志
            return businessException.getErrorCode().isServerError();
        }
        
        // 系统异常需要记录详细日志
        return true;
    }

    /**
     * 判断异常是否需要告警
     */
    public static boolean shouldAlert(Throwable throwable) {
        // 系统异常需要告警
        if (isSystemException(throwable)) {
            return true;
        }
        
        // 某些业务异常也需要告警
        if (throwable instanceof BusinessException) {
            BusinessException businessException = (BusinessException) throwable;
            ErrorCode errorCode = businessException.getErrorCode();
            
            // 数据库错误、支付失败等需要告警
            return errorCode == ErrorCode.DATABASE_ERROR ||
                   errorCode == ErrorCode.PAYMENT_FAILED ||
                   errorCode == ErrorCode.AI_SERVICE_ERROR;
        }
        
        return false;
    }

    /**
     * 脱敏敏感信息
     */
    public static String maskSensitiveInfo(String message) {
        if (!StringUtils.hasText(message)) {
            return message;
        }
        
        // 脱敏手机号
        message = message.replaceAll("(1[3-9]\\d)\\d{4}(\\d{4})", "$1****$2");
        
        // 脱敏邮箱
        message = message.replaceAll("([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})", 
                "***@$2");
        
        // 脱敏身份证号
        message = message.replaceAll("(\\d{6})\\d{8}(\\d{4})", "$1********$2");
        
        // 脱敏银行卡号
        message = message.replaceAll("(\\d{4})\\d{8,12}(\\d{4})", "$1********$2");
        
        return message;
    }

    /**
     * 构建错误详情（用于开发环境）
     */
    public static String buildErrorDetail(Throwable throwable, String traceId) {
        StringBuilder detail = new StringBuilder();
        detail.append("TraceId: ").append(traceId).append("\n");
        detail.append("Exception: ").append(throwable.getClass().getSimpleName()).append("\n");
        detail.append("Message: ").append(throwable.getMessage()).append("\n");
        
        if (throwable instanceof BusinessException) {
            BusinessException businessException = (BusinessException) throwable;
            detail.append("ErrorCode: ").append(businessException.getErrorCode()).append("\n");
        }
        
        detail.append("RootCause: ").append(getRootCauseMessage(throwable)).append("\n");
        detail.append("StackTrace: \n").append(getStackTrace(throwable));
        
        return detail.toString();
    }
}
