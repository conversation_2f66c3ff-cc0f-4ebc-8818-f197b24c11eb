package com.example.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.entity.Conversation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI对话记录Mapper接口
 * 继承MyBatis Plus的BaseMapper，提供基础的CRUD操作
 */
@Mapper
public interface ConversationMapper extends BaseMapper<Conversation> {

    /**
     * 根据用户ID查询对话记录
     * @param userId 用户ID
     * @return 对话记录列表
     */
    @Select("SELECT * FROM conversations WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<Conversation> findByUserId(@Param("userId") Long userId);

    /**
     * 根据会话ID查询对话记录
     * @param sessionId 会话ID
     * @return 对话记录列表
     */
    @Select("SELECT * FROM conversations WHERE session_id = #{sessionId} ORDER BY create_time ASC")
    List<Conversation> findBySessionId(@Param("sessionId") String sessionId);

    /**
     * 根据对话类型查询对话记录
     * @param conversationType 对话类型
     * @param limit 限制数量
     * @return 对话记录列表
     */
    @Select("SELECT * FROM conversations WHERE conversation_type = #{conversationType} " +
            "ORDER BY create_time DESC LIMIT #{limit}")
    List<Conversation> findByConversationType(@Param("conversationType") String conversationType, 
                                            @Param("limit") Integer limit);

    /**
     * 分页查询对话记录
     * @param page 分页参数
     * @param userId 用户ID（可选）
     * @param conversationType 对话类型（可选）
     * @param keyword 关键词搜索（消息内容）
     * @return 分页对话记录列表
     */
    @Select("<script>" +
            "SELECT c.*, u.username, u.real_name " +
            "FROM conversations c " +
            "LEFT JOIN users u ON c.user_id = u.id " +
            "WHERE 1=1 " +
            "<if test='userId != null'> AND c.user_id = #{userId} </if>" +
            "<if test='conversationType != null and conversationType != \"\"'> AND c.conversation_type = #{conversationType} </if>" +
            "<if test='keyword != null and keyword != \"\"'> " +
            "AND (c.message LIKE CONCAT('%', #{keyword}, '%') OR c.response LIKE CONCAT('%', #{keyword}, '%')) " +
            "</if>" +
            "ORDER BY c.create_time DESC" +
            "</script>")
    IPage<Conversation> findConversationsWithConditions(Page<Conversation> page,
                                                       @Param("userId") Long userId,
                                                       @Param("conversationType") String conversationType,
                                                       @Param("keyword") String keyword);

    /**
     * 查询用户最近的会话ID列表
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 会话ID列表
     */
    @Select("SELECT DISTINCT session_id FROM conversations WHERE user_id = #{userId} " +
            "ORDER BY MAX(create_time) DESC LIMIT #{limit}")
    List<String> findRecentSessionsByUser(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 查询最活跃的对话类型统计
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 对话类型统计列表
     */
    @Select("<script>" +
            "SELECT conversation_type, COUNT(*) as count " +
            "FROM conversations WHERE 1=1 " +
            "<if test='startTime != null'> AND create_time >= #{startTime} </if>" +
            "<if test='endTime != null'> AND create_time &lt;= #{endTime} </if>" +
            "GROUP BY conversation_type ORDER BY count DESC" +
            "</script>")
    List<Object> getConversationTypeStats(@Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 统计对话数量
     * @param conversationType 对话类型（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 对话数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM conversations WHERE 1=1 " +
            "<if test='conversationType != null and conversationType != \"\"'> AND conversation_type = #{conversationType} </if>" +
            "<if test='startTime != null'> AND create_time >= #{startTime} </if>" +
            "<if test='endTime != null'> AND create_time &lt;= #{endTime} </if>" +
            "</script>")
    Long countConversations(@Param("conversationType") String conversationType,
                           @Param("startTime") LocalDateTime startTime,
                           @Param("endTime") LocalDateTime endTime);

    /**
     * 统计活跃用户数量（有对话记录的用户）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 活跃用户数量
     */
    @Select("<script>" +
            "SELECT COUNT(DISTINCT user_id) FROM conversations WHERE user_id IS NOT NULL " +
            "<if test='startTime != null'> AND create_time >= #{startTime} </if>" +
            "<if test='endTime != null'> AND create_time &lt;= #{endTime} </if>" +
            "</script>")
    Long countActiveUsers(@Param("startTime") LocalDateTime startTime,
                         @Param("endTime") LocalDateTime endTime);

    /**
     * 查询热门问题（出现频率高的用户消息）
     * @param limit 限制数量
     * @return 热门问题列表
     */
    @Select("SELECT message, COUNT(*) as count " +
            "FROM conversations " +
            "WHERE LENGTH(message) > 5 AND LENGTH(message) < 100 " +
            "GROUP BY message " +
            "HAVING count > 1 " +
            "ORDER BY count DESC, LENGTH(message) ASC " +
            "LIMIT #{limit}")
    List<Object> getPopularQuestions(@Param("limit") Integer limit);

    /**
     * 查询每日对话统计（最近30天）
     * @return 每日对话统计列表
     */
    @Select("SELECT DATE(create_time) as conversation_date, " +
            "COUNT(*) as total_count, " +
            "COUNT(DISTINCT user_id) as user_count, " +
            "COUNT(DISTINCT session_id) as session_count " +
            "FROM conversations " +
            "WHERE create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY) " +
            "GROUP BY DATE(create_time) " +
            "ORDER BY conversation_date DESC")
    List<Object> getDailyConversationStats();

    /**
     * 删除指定时间之前的对话记录（数据清理）
     * @param beforeTime 指定时间
     * @return 删除的记录数
     */
    @Select("DELETE FROM conversations WHERE create_time < #{beforeTime}")
    int deleteConversationsBefore(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 查询用户对话摘要信息
     * @param userId 用户ID
     * @return 对话摘要
     */
    @Select("SELECT " +
            "COUNT(*) as total_conversations, " +
            "COUNT(DISTINCT session_id) as total_sessions, " +
            "MIN(create_time) as first_conversation_time, " +
            "MAX(create_time) as last_conversation_time " +
            "FROM conversations WHERE user_id = #{userId}")
    Object getUserConversationSummary(@Param("userId") Long userId);
}
