<template>
  <div class="help-request-detail-page">
    <div class="container" v-loading="loading">
      <!-- 返回按钮 -->
      <div class="page-header">
        <el-button @click="goBack" class="back-btn">
          <el-icon>
            <ArrowLeft />
          </el-icon>
          返回
        </el-button>
      </div>

      <!-- 申请详情 -->
      <div v-if="!loading && requestInfo.id" class="request-detail">
        <!-- 基本信息卡片 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <h2>{{ requestInfo.title }}</h2>
              <el-tag :type="getStatusType(requestInfo.status)" size="large">
                {{ getStatusText(requestInfo.status) }}
              </el-tag>
            </div>
          </template>

          <div class="request-content">
            <div class="info-section">
              <h3>申请信息</h3>
              <div class="info-grid">
                <div class="info-item">
                  <label>申请分类：</label>
                  <span>{{ getCategoryText(requestInfo.category) }}</span>
                </div>
                <div class="info-item">
                  <label>所需金额：</label>
                  <span class="amount">¥{{ formatAmount(requestInfo.amountNeeded) }}</span>
                </div>
                <div class="info-item">
                  <label>联系电话：</label>
                  <span>{{ requestInfo.contactPhone }}</span>
                </div>
                <div class="info-item">
                  <label>联系地址：</label>
                  <span>{{ requestInfo.contactAddress }}</span>
                </div>
                <div class="info-item">
                  <label>申请时间：</label>
                  <span>{{ formatDate(requestInfo.createTime) }}</span>
                </div>
              </div>
            </div>

            <div class="description-section">
              <h3>详细描述</h3>
              <div class="description-content">
                {{ requestInfo.description }}
              </div>
            </div>

            <!-- 证明材料 -->
            <div v-if="getProofImages().length > 0" class="proof-section">
              <h3>证明材料</h3>
              <div class="proof-images">
                <el-image
                  v-for="(image, index) in getProofImages()"
                  :key="index"
                  :src="image"
                  :preview-src-list="getProofImages()"
                  :initial-index="index"
                  fit="cover"
                  class="proof-image"
                />
              </div>
            </div>

            <!-- 管理员回复 -->
            <div v-if="requestInfo.adminResponse" class="response-section">
              <h3>管理员回复</h3>
              <div class="response-content">
                {{ requestInfo.adminResponse }}
              </div>
              <div class="response-time">
                回复时间：{{ formatDate(requestInfo.updateTime) }}
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!loading" class="empty-state">
        <el-empty description="申请不存在或已被删除" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getHelpRequestById } from '@/api/helpRequest'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const requestInfo = ref({})

// 加载申请详情
const loadRequestDetail = async () => {
  try {
    loading.value = true
    const requestId = route.params.id
    const response = await getHelpRequestById(requestId)
    requestInfo.value = response || {}
  } catch (error) {
    console.error('加载申请详情失败:', error)
    ElMessage.error('加载申请详情失败')
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 工具函数
const formatAmount = (amount) => {
  if (!amount) return '0'
  return amount.toLocaleString()
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

const getStatusText = (status) => {
  const statusMap = {
    pending: '审核中',
    approved: '已通过',
    rejected: '已拒绝',
    processing: '处理中'
  }
  return statusMap[status] || '未知'
}

const getStatusType = (status) => {
  const typeMap = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    processing: 'info'
  }
  return typeMap[status] || 'info'
}

const getCategoryText = (category) => {
  const categoryMap = {
    medical: '医疗救助',
    education: '教育助学',
    disaster: '灾难救援',
    poverty: '扶贫助困'
  }
  return categoryMap[category] || '其他'
}

const getProofImages = () => {
  if (!requestInfo.value.proofImages) return []
  try {
    return JSON.parse(requestInfo.value.proofImages)
  } catch {
    return []
  }
}

// 页面初始化
onMounted(() => {
  loadRequestDetail()
})
</script>

<style scoped>
.help-request-detail-page {
  min-height: 100vh;
  background: #F7FAFC;
  padding: 32px 0;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 24px;
}

.page-header {
  margin-bottom: 24px;
}

.back-btn {
  background: white;
  border: 1px solid #E2E8F0;
}

.info-card {
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #2D3748;
}

.request-content {
  padding: 16px 0;
}

.info-section h3,
.description-section h3,
.proof-section h3,
.response-section h3 {
  color: #2D3748;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item label {
  font-weight: 500;
  color: #718096;
  min-width: 100px;
}

.info-item .amount {
  color: #E53E3E;
  font-weight: 600;
  font-size: 18px;
}

.description-content {
  background: #F7FAFC;
  padding: 16px;
  border-radius: 8px;
  line-height: 1.6;
  color: #2D3748;
  margin-bottom: 32px;
}

.proof-images {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.proof-image {
  width: 100%;
  height: 200px;
  border-radius: 8px;
}

.response-section {
  background: #EDF2F7;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #4299E1;
}

.response-content {
  color: #2D3748;
  line-height: 1.6;
  margin-bottom: 8px;
}

.response-time {
  color: #718096;
  font-size: 14px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .proof-images {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}
</style>
