package com.example.demo.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 捐赠创建请求DTO
 */
@Schema(description = "捐赠创建请求")
public class DonationCreateDTO {

    @Schema(description = "项目ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    @Schema(description = "捐赠金额", example = "1000.00", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "捐赠金额不能为空")
    @DecimalMin(value = "0.01", message = "捐赠金额必须大于0")
    private BigDecimal amount;

    @Schema(description = "支付方式", example = "alipay", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "支付方式不能为空")
    private String paymentMethod;

    @Schema(description = "捐赠留言", example = "希望小明早日康复！")
    @Size(max = 500, message = "捐赠留言长度不能超过500字符")
    private String message;

    @Schema(description = "是否匿名", example = "false")
    private Boolean isAnonymous = false;

    // Getters and Setters
    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Boolean getIsAnonymous() {
        return isAnonymous;
    }

    public void setIsAnonymous(Boolean isAnonymous) {
        this.isAnonymous = isAnonymous;
    }
}
