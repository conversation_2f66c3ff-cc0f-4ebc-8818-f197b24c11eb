package com.example.demo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.entity.HelpRequest;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 救助申请服务接口
 * 定义救助申请相关的业务操作
 */
public interface HelpRequestService {

    /**
     * 创建救助申请
     * @param helpRequest 申请信息
     * @return 创建成功的申请记录
     */
    HelpRequest createHelpRequest(HelpRequest helpRequest);

    /**
     * 更新救助申请
     * @param helpRequest 申请信息
     * @param userId 用户ID
     * @return 更新后的申请记录
     */
    HelpRequest updateHelpRequest(HelpRequest helpRequest, Long userId);

    /**
     * 审核救助申请
     * @param requestId 申请ID
     * @param status 审核状态
     * @param adminResponse 管理员回复
     * @param adminId 管理员ID
     * @return 是否审核成功
     */
    boolean reviewHelpRequest(Long requestId, String status, String adminResponse, Long adminId);

    /**
     * 根据ID查询救助申请
     * @param id 申请ID
     * @return 申请记录
     */
    HelpRequest findById(Long id);

    /**
     * 根据用户ID查询救助申请
     * @param userId 用户ID
     * @return 申请记录列表
     */
    List<HelpRequest> findByUserId(Long userId);

    /**
     * 根据状态查询救助申请
     * @param status 申请状态
     * @return 申请记录列表
     */
    List<HelpRequest> findByStatus(String status);

    /**
     * 根据分类查询救助申请
     * @param category 申请分类
     * @param status 申请状态
     * @return 申请记录列表
     */
    List<HelpRequest> findByCategoryAndStatus(String category, String status);

    /**
     * 分页查询救助申请
     * @param page 分页参数
     * @param userId 用户ID
     * @param category 申请分类
     * @param status 申请状态
     * @param keyword 关键词搜索
     * @return 分页申请记录列表
     */
    IPage<HelpRequest> findHelpRequestsWithConditions(Page<HelpRequest> page, Long userId, 
                                                     String category, String status, String keyword);

    /**
     * 查询待审核的申请
     * @param limit 限制数量
     * @return 待审核申请列表
     */
    List<HelpRequest> findPendingRequests(Integer limit);

    /**
     * 查询最近通过的申请
     * @param limit 限制数量
     * @return 最近通过的申请列表
     */
    List<HelpRequest> findRecentApprovedRequests(Integer limit);

    /**
     * 统计救助申请数量
     * @param category 申请分类
     * @param status 申请状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 申请数量
     */
    Long countHelpRequests(String category, String status, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计救助申请总金额
     * @param status 申请状态
     * @return 总金额
     */
    BigDecimal sumAmountNeeded(String status);

    /**
     * 查询用户申请次数
     * @param userId 用户ID
     * @return 申请次数
     */
    Long countRequestsByUser(Long userId);

    /**
     * 查询管理员处理的申请数量
     * @param adminId 管理员ID
     * @return 处理数量
     */
    Long countRequestsByAdmin(Long adminId);

    /**
     * 删除救助申请
     * @param requestId 申请ID
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean deleteHelpRequest(Long requestId, Long userId);

    /**
     * 批量审核救助申请
     * @param requestIds 申请ID列表
     * @param status 审核状态
     * @param adminResponse 管理员回复
     * @param adminId 管理员ID
     * @return 是否审核成功
     */
    boolean batchReviewHelpRequests(List<Long> requestIds, String status, String adminResponse, Long adminId);

    /**
     * 获取申请统计信息（按分类）
     * @return 分类统计列表
     */
    List<Object> getRequestStatsByCategory();

    /**
     * 获取申请统计信息（按状态）
     * @return 状态统计列表
     */
    List<Object> getRequestStatsByStatus();

    /**
     * 获取每日申请统计
     * @return 每日申请统计列表
     */
    List<Object> getDailyRequestStats();

    /**
     * 获取申请概览统计
     * @return 申请概览统计
     */
    Object getRequestOverviewStats();

    /**
     * 获取处理效率统计
     * @param adminId 管理员ID
     * @return 处理效率统计
     */
    Object getProcessEfficiencyStats(Long adminId);

    /**
     * 检查用户是否可以申请救助
     * @param userId 用户ID
     * @param category 申请分类
     * @return 是否可以申请
     */
    boolean canUserApplyForHelp(Long userId, String category);

    /**
     * 验证申请权限
     * @param requestId 申请ID
     * @param userId 用户ID
     * @return 是否有权限
     */
    boolean hasRequestPermission(Long requestId, Long userId);

    /**
     * 获取用户申请统计
     * @param userId 用户ID
     * @return 用户申请统计
     */
    Object getUserRequestStats(Long userId);

    /**
     * 自动分配审核员
     * @param requestId 申请ID
     * @return 分配的管理员ID
     */
    Long autoAssignReviewer(Long requestId);

    /**
     * 发送申请状态通知
     * @param requestId 申请ID
     * @param status 新状态
     * @return 是否发送成功
     */
    boolean sendStatusNotification(Long requestId, String status);

    /**
     * 生成申请报告
     * @param requestId 申请ID
     * @return 申请报告
     */
    Object generateRequestReport(Long requestId);

    /**
     * 检查申请是否需要补充材料
     * @param requestId 申请ID
     * @return 是否需要补充材料
     */
    boolean needsAdditionalDocuments(Long requestId);

    /**
     * 标记申请为紧急
     * @param requestId 申请ID
     * @param adminId 管理员ID
     * @return 是否标记成功
     */
    boolean markAsUrgent(Long requestId, Long adminId);

    /**
     * 获取相似申请
     * @param requestId 申请ID
     * @param limit 限制数量
     * @return 相似申请列表
     */
    List<HelpRequest> findSimilarRequests(Long requestId, Integer limit);

    /**
     * 获取救助申请概览统计
     * @return 救助申请概览统计
     */
    Object getHelpRequestOverviewStats();

    /**
     * 统计待审核申请数量
     * @return 待审核申请数量
     */
    Long countPendingRequests();
}
