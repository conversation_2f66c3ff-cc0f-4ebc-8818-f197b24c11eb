package com.example.demo.dto.converter;

import com.example.demo.dto.response.*;
import com.example.demo.entity.*;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * DTO转换工具类
 * 负责实体类与DTO之间的转换
 */
public class DTOConverter {

    /**
     * 脱敏邮箱
     */
    public static String maskEmail(String email) {
        if (!StringUtils.hasText(email) || !email.contains("@")) {
            return email;
        }
        String[] parts = email.split("@");
        String username = parts[0];
        String domain = parts[1];
        
        if (username.length() <= 2) {
            return username.charAt(0) + "***@" + domain;
        } else {
            return username.charAt(0) + "***" + username.charAt(username.length() - 1) + "@" + domain;
        }
    }

    /**
     * 脱敏手机号
     */
    public static String maskPhone(String phone) {
        if (!StringUtils.hasText(phone) || phone.length() != 11) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }

    /**
     * 解析图片JSON字符串为列表
     */
    public static List<String> parseImageList(String imagesJson) {
        if (!StringUtils.hasText(imagesJson) || "[]".equals(imagesJson.trim())) {
            return Collections.emptyList();
        }
        try {
            // 简单的JSON解析，实际项目中建议使用Jackson或Gson
            String cleaned = imagesJson.replace("[", "").replace("]", "").replace("\"", "");
            if (cleaned.trim().isEmpty()) {
                return Collections.emptyList();
            }
            return Arrays.asList(cleaned.split(","));
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    /**
     * 获取图片列表的第一张作为封面
     */
    public static String getCoverImage(String imagesJson) {
        List<String> images = parseImageList(imagesJson);
        return images.isEmpty() ? null : images.get(0).trim();
    }

    /**
     * 计算完成百分比
     */
    public static BigDecimal calculateCompletionPercentage(BigDecimal currentAmount, BigDecimal targetAmount) {
        if (targetAmount == null || targetAmount.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        if (currentAmount == null) {
            currentAmount = BigDecimal.ZERO;
        }
        return currentAmount.divide(targetAmount, 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"))
                .setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 获取角色描述
     */
    public static String getRoleDescription(String role) {
        if (User.Role.ADMIN.getCode().equals(role)) {
            return User.Role.ADMIN.getDescription();
        } else if (User.Role.USER.getCode().equals(role)) {
            return User.Role.USER.getDescription();
        }
        return "未知角色";
    }

    /**
     * 获取用户状态描述
     */
    public static String getUserStatusDescription(Integer status) {
        if (User.Status.NORMAL.getCode().equals(status)) {
            return User.Status.NORMAL.getDescription();
        } else if (User.Status.DISABLED.getCode().equals(status)) {
            return User.Status.DISABLED.getDescription();
        }
        return "未知状态";
    }

    /**
     * 获取项目分类描述
     */
    public static String getProjectCategoryDescription(String category) {
        for (Project.Category cat : Project.Category.values()) {
            if (cat.getCode().equals(category)) {
                return cat.getDescription();
            }
        }
        return "未知分类";
    }

    /**
     * 获取项目状态描述
     */
    public static String getProjectStatusDescription(String status) {
        for (Project.Status stat : Project.Status.values()) {
            if (stat.getCode().equals(status)) {
                return stat.getDescription();
            }
        }
        return "未知状态";
    }

    /**
     * 获取捐赠状态描述
     */
    public static String getDonationStatusDescription(String status) {
        for (Donation.Status stat : Donation.Status.values()) {
            if (stat.getCode().equals(status)) {
                return stat.getDescription();
            }
        }
        return "未知状态";
    }

    /**
     * 获取支付方式描述
     */
    public static String getPaymentMethodDescription(String paymentMethod) {
        for (Donation.PaymentMethod method : Donation.PaymentMethod.values()) {
            if (method.getCode().equals(paymentMethod)) {
                return method.getDescription();
            }
        }
        return "未知支付方式";
    }

    /**
     * 获取对话类型描述
     */
    public static String getConversationTypeDescription(String conversationType) {
        for (Conversation.ConversationType type : Conversation.ConversationType.values()) {
            if (type.getCode().equals(conversationType)) {
                return type.getDescription();
            }
        }
        return "未知类型";
    }

    /**
     * 获取救助申请分类描述
     */
    public static String getHelpRequestCategoryDescription(String category) {
        for (HelpRequest.Category cat : HelpRequest.Category.values()) {
            if (cat.getCode().equals(category)) {
                return cat.getDescription();
            }
        }
        return "未知分类";
    }

    /**
     * 获取救助申请状态描述
     */
    public static String getHelpRequestStatusDescription(String status) {
        for (HelpRequest.Status stat : HelpRequest.Status.values()) {
            if (stat.getCode().equals(status)) {
                return stat.getDescription();
            }
        }
        return "未知状态";
    }

    /**
     * 转换User实体为UserResponseDTO
     */
    public static UserResponseDTO convertToUserResponseDTO(User user) {
        if (user == null) {
            return null;
        }
        
        UserResponseDTO dto = new UserResponseDTO();
        dto.setId(user.getId());
        dto.setUsername(user.getUsername());
        dto.setEmail(maskEmail(user.getEmail()));
        dto.setPhone(maskPhone(user.getPhone()));
        dto.setRealName(user.getRealName());
        dto.setAvatar(convertAvatarUrl(user.getAvatar()));
        dto.setRole(user.getRole());
        dto.setRoleDescription(getRoleDescription(user.getRole()));
        dto.setStatus(user.getStatus());
        dto.setStatusDescription(getUserStatusDescription(user.getStatus()));
        dto.setCreateTime(user.getCreateTime());
        dto.setUpdateTime(user.getUpdateTime());
        
        return dto;
    }

    /**
     * 转换User实体为UserListDTO
     */
    public static UserListDTO convertToUserListDTO(User user) {
        if (user == null) {
            return null;
        }
        
        UserListDTO dto = new UserListDTO();
        dto.setId(user.getId());
        dto.setUsername(user.getUsername());
        dto.setRealName(user.getRealName());
        dto.setEmail(maskEmail(user.getEmail()));
        dto.setPhone(maskPhone(user.getPhone()));
        dto.setAvatar(convertAvatarUrl(user.getAvatar()));
        dto.setRole(user.getRole());
        dto.setRoleDescription(getRoleDescription(user.getRole()));
        dto.setStatus(user.getStatus());
        dto.setStatusDescription(getUserStatusDescription(user.getStatus()));
        dto.setCreateTime(user.getCreateTime());
        
        return dto;
    }

    /**
     * 转换Project实体为ProjectResponseDTO
     */
    public static ProjectResponseDTO convertToProjectResponseDTO(Project project) {
        if (project == null) {
            return null;
        }
        
        ProjectResponseDTO dto = new ProjectResponseDTO();
        dto.setId(project.getId());
        dto.setTitle(project.getTitle());
        dto.setDescription(project.getDescription());
        dto.setCategory(project.getCategory());
        dto.setCategoryDescription(getProjectCategoryDescription(project.getCategory()));
        dto.setTargetAmount(project.getTargetAmount());
        dto.setCurrentAmount(project.getCurrentAmount());
        dto.setCompletionPercentage(calculateCompletionPercentage(project.getCurrentAmount(), project.getTargetAmount()));
        dto.setImages(parseImageList(project.getImages()));
        dto.setContactInfo(project.getContactInfo());
        dto.setStatus(project.getStatus());
        dto.setStatusDescription(getProjectStatusDescription(project.getStatus()));
        dto.setAdminId(project.getAdminId());
        dto.setCreateTime(project.getCreateTime());
        dto.setUpdateTime(project.getUpdateTime());
        
        return dto;
    }

    /**
     * 转换Project实体为ProjectListDTO
     */
    public static ProjectListDTO convertToProjectListDTO(Project project) {
        if (project == null) {
            return null;
        }
        
        ProjectListDTO dto = new ProjectListDTO();
        dto.setId(project.getId());
        dto.setTitle(project.getTitle());
        dto.setCategory(project.getCategory());
        dto.setCategoryDescription(getProjectCategoryDescription(project.getCategory()));
        dto.setTargetAmount(project.getTargetAmount());
        dto.setCurrentAmount(project.getCurrentAmount());
        dto.setCompletionPercentage(calculateCompletionPercentage(project.getCurrentAmount(), project.getTargetAmount()));
        dto.setCoverImage(getCoverImage(project.getImages()));
        dto.setStatus(project.getStatus());
        dto.setStatusDescription(getProjectStatusDescription(project.getStatus()));
        dto.setCreateTime(project.getCreateTime());
        
        return dto;
    }

    /**
     * 转换头像URL
     * 如果是相对路径，转换为完整URL；如果已经是完整URL，直接返回
     */
    private static String convertAvatarUrl(String avatar) {
        if (avatar == null || avatar.isEmpty()) {
            return avatar;
        }

        // 如果已经是完整URL（包含http://或https://），直接返回
        if (avatar.startsWith("http://") || avatar.startsWith("https://")) {
            return avatar;
        }

        // 如果是相对路径，转换为完整URL
        if (avatar.startsWith("/")) {
            return "http://localhost:8080/uploads" + avatar;
        }

        // 如果不是以/开头，添加/uploads/前缀
        return "http://localhost:8080/uploads/" + avatar;
    }
}
