package com.example.demo.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import jakarta.annotation.PreDestroy;

import java.util.Date;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * JWT黑名单服务
 * 管理被撤销的JWT令牌
 */
@Slf4j
@Service
public class JwtBlacklistService {

    /**
     * 黑名单存储（生产环境建议使用Redis）
     * Key: token, Value: expiration time
     */
    private final ConcurrentHashMap<String, Date> blacklist = new ConcurrentHashMap<>();

    /**
     * 定时清理过期的黑名单令牌
     */
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    public JwtBlacklistService() {
        // 每小时清理一次过期的黑名单令牌
        scheduler.scheduleAtFixedRate(this::cleanupExpiredTokens, 1, 1, TimeUnit.HOURS);
    }

    /**
     * 将令牌加入黑名单
     */
    public void addToBlacklist(String token, Date expiration) {
        if (token != null && expiration != null) {
            // 生成令牌的哈希值作为key，避免存储完整令牌
            String tokenHash = generateTokenHash(token);
            blacklist.put(tokenHash, expiration);
            log.debug("令牌已加入黑名单，过期时间: {}", expiration);
        }
    }

    /**
     * 检查令牌是否在黑名单中
     */
    public boolean isBlacklisted(String token) {
        if (token == null) {
            return false;
        }
        
        String tokenHash = generateTokenHash(token);
        Date expiration = blacklist.get(tokenHash);
        
        if (expiration == null) {
            return false;
        }
        
        // 如果令牌已过期，从黑名单中移除
        if (expiration.before(new Date())) {
            blacklist.remove(tokenHash);
            return false;
        }
        
        return true;
    }

    /**
     * 从黑名单中移除令牌
     */
    public void removeFromBlacklist(String token) {
        if (token != null) {
            String tokenHash = generateTokenHash(token);
            blacklist.remove(tokenHash);
            log.debug("令牌已从黑名单中移除");
        }
    }

    /**
     * 清理过期的黑名单令牌
     */
    public void cleanupExpiredTokens() {
        Date now = new Date();
        int removedCount = 0;
        
        blacklist.entrySet().removeIf(entry -> {
            if (entry.getValue().before(now)) {
                return true;
            }
            return false;
        });
        
        if (removedCount > 0) {
            log.info("清理了 {} 个过期的黑名单令牌", removedCount);
        }
    }

    /**
     * 获取黑名单大小
     */
    public int getBlacklistSize() {
        return blacklist.size();
    }

    /**
     * 清空黑名单（谨慎使用）
     */
    public void clearBlacklist() {
        blacklist.clear();
        log.warn("黑名单已被清空");
    }

    /**
     * 生成令牌哈希值
     * 使用简单的哈希算法，生产环境建议使用更安全的算法
     */
    private String generateTokenHash(String token) {
        return String.valueOf(token.hashCode());
    }

    /**
     * 关闭服务时清理资源
     */
    @PreDestroy
    public void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
