package com.example.demo.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 文件上传响应DTO
 */
@Schema(description = "文件上传响应")
public class FileUploadResponseDTO {

    @Schema(description = "文件名", example = "avatar_123_1640995200000.jpg")
    private String fileName;

    @Schema(description = "文件相对路径", example = "/uploads/avatars/avatar_123_1640995200000.jpg")
    private String filePath;

    @Schema(description = "文件访问URL", example = "http://localhost:8080/uploads/avatars/avatar_123_1640995200000.jpg")
    private String fileUrl;

    @Schema(description = "文件大小（字节）", example = "1024000")
    private Long fileSize;

    @Schema(description = "文件类型", example = "image/jpeg")
    private String contentType;

    @Schema(description = "上传时间戳", example = "1640995200000")
    private Long uploadTime;

    // 构造函数
    public FileUploadResponseDTO() {}

    public FileUploadResponseDTO(String fileName, String filePath, String fileUrl, 
                                Long fileSize, String contentType, Long uploadTime) {
        this.fileName = fileName;
        this.filePath = filePath;
        this.fileUrl = fileUrl;
        this.fileSize = fileSize;
        this.contentType = contentType;
        this.uploadTime = uploadTime;
    }

    // Getters and Setters
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public Long getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(Long uploadTime) {
        this.uploadTime = uploadTime;
    }
}
