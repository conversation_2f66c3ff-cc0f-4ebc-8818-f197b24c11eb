import request from './index'

// 上传头像
export const uploadAvatar = (formData) => {
  return request({
    url: '/files/upload/avatar',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 上传项目图片
export const uploadProjectImage = (formData, projectId) => {
  const url = projectId 
    ? `/files/upload/project?projectId=${projectId}`
    : '/files/upload/project'
  
  return request({
    url,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 上传申请证明材料
export const uploadRequestDocument = (formData, requestId) => {
  const url = requestId 
    ? `/files/upload/document?requestId=${requestId}`
    : '/files/upload/document'
  
  return request({
    url,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 通用文件上传
export const uploadFile = (formData, category, relatedId) => {
  let url = `/files/upload?category=${category}`
  if (relatedId) {
    url += `&relatedId=${relatedId}`
  }
  
  return request({
    url,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 删除文件
export const deleteFile = (filePath) => {
  return request({
    url: '/files/delete',
    method: 'delete',
    params: { filePath }
  })
}

// 获取文件上传配置
export const getUploadConfig = () => {
  return request({
    url: '/files/info',
    method: 'get'
  })
}
