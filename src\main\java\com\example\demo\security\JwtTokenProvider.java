package com.example.demo.security;

import com.example.demo.config.JwtProperties;
import com.example.demo.entity.User;
import com.example.demo.exception.JwtAuthenticationException;
import com.example.demo.service.UserService;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT令牌提供者
 * 负责JWT令牌的生成、验证和解析
 */
@Slf4j
@Component
public class JwtTokenProvider {

    @Autowired
    private JwtProperties jwtProperties;

    @Autowired
    private JwtBlacklistService jwtBlacklistService;

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 生成访问令牌
     */
    public String generateAccessToken(Long userId, String username, String role) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("username", username);
        claims.put("role", role);
        claims.put("type", "access");
        
        return generateToken(claims, jwtProperties.getAccessTokenExpiration());
    }

    /**
     * 生成刷新令牌
     */
    public String generateRefreshToken(Long userId, String username) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("username", username);
        claims.put("type", "refresh");
        
        return generateToken(claims, jwtProperties.getRefreshTokenExpiration());
    }

    /**
     * 生成令牌
     */
    private String generateToken(Map<String, Object> claims, Long expiration) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration);

        return Jwts.builder()
                .setClaims(claims)
                .setIssuer(jwtProperties.getIssuer())
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(getSigningKey(), SignatureAlgorithm.HS256)
                .compact();
    }

    /**
     * 从令牌中获取用户ID
     */
    public Long getUserIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        Object userId = claims.get("userId");
        if (userId instanceof Integer) {
            return ((Integer) userId).longValue();
        }
        return (Long) userId;
    }

    /**
     * 从令牌中获取用户名
     */
    public String getUsernameFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return (String) claims.get("username");
    }

    /**
     * 从令牌中获取用户角色
     */
    public String getRoleFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return (String) claims.get("role");
    }

    /**
     * 从令牌中获取令牌类型
     */
    public String getTokenTypeFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return (String) claims.get("type");
    }

    /**
     * 获取令牌过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.getExpiration();
    }

    /**
     * 验证令牌
     */
    public boolean validateToken(String token) {
        try {
            // 检查令牌是否在黑名单中
            if (jwtBlacklistService.isBlacklisted(token)) {
                log.warn("令牌已在黑名单中: {}", token.substring(0, Math.min(token.length(), 20)) + "...");
                return false;
            }

            // 解析令牌
            getClaimsFromToken(token);
            return true;
        } catch (JwtException | IllegalArgumentException e) {
            log.warn("令牌验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查令牌是否过期
     */
    public boolean isTokenExpired(String token) {
        try {
            Date expiration = getExpirationDateFromToken(token);
            return expiration.before(new Date());
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 检查是否为访问令牌
     */
    public boolean isAccessToken(String token) {
        try {
            String tokenType = getTokenTypeFromToken(token);
            return "access".equals(tokenType);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查是否为刷新令牌
     */
    public boolean isRefreshToken(String token) {
        try {
            String tokenType = getTokenTypeFromToken(token);
            return "refresh".equals(tokenType);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 从请求头中提取令牌
     */
    public String extractTokenFromHeader(String authHeader) {
        if (StringUtils.hasText(authHeader) && authHeader.startsWith(jwtProperties.getPrefix() + " ")) {
            return authHeader.substring(jwtProperties.getPrefix().length() + 1);
        }
        return null;
    }

    /**
     * 刷新访问令牌
     */
    public String refreshAccessToken(String refreshToken) {
        if (!validateToken(refreshToken) || !isRefreshToken(refreshToken)) {
            throw JwtAuthenticationException.invalid("无效的刷新令牌");
        }

        Long userId = getUserIdFromToken(refreshToken);
        String username = getUsernameFromToken(refreshToken);

        // 从数据库重新获取用户信息以确保角色是最新的
        try {
            UserService userService = applicationContext.getBean(UserService.class);
            User user = userService.findById(userId);
            if (user == null) {
                throw JwtAuthenticationException.invalid("用户不存在");
            }
            return generateAccessToken(userId, username, user.getRole());
        } catch (Exception e) {
            // 如果无法获取用户信息，使用默认角色
            log.warn("无法获取用户信息，使用默认角色: {}", e.getMessage());
            return generateAccessToken(userId, username, "user");
        }
    }

    /**
     * 将令牌加入黑名单
     */
    public void blacklistToken(String token) {
        if (StringUtils.hasText(token)) {
            Date expiration = getExpirationDateFromToken(token);
            jwtBlacklistService.addToBlacklist(token, expiration);
            log.info("令牌已加入黑名单");
        }
    }

    /**
     * 获取令牌剩余有效时间（毫秒）
     */
    public long getRemainingValidTime(String token) {
        try {
            Date expiration = getExpirationDateFromToken(token);
            return Math.max(0, expiration.getTime() - System.currentTimeMillis());
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 从令牌中获取声明
     */
    private Claims getClaimsFromToken(String token) {
        try {
            return Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (ExpiredJwtException e) {
            log.warn("令牌已过期: {}", e.getMessage());
            throw JwtAuthenticationException.expired();
        } catch (UnsupportedJwtException e) {
            log.warn("不支持的令牌: {}", e.getMessage());
            throw JwtAuthenticationException.invalid("不支持的令牌格式");
        } catch (MalformedJwtException e) {
            log.warn("令牌格式错误: {}", e.getMessage());
            throw JwtAuthenticationException.malformed();
        } catch (SecurityException e) {
            log.warn("令牌签名验证失败: {}", e.getMessage());
            throw JwtAuthenticationException.invalid("令牌签名验证失败");
        } catch (IllegalArgumentException e) {
            log.warn("令牌参数错误: {}", e.getMessage());
            throw JwtAuthenticationException.invalid("令牌参数错误");
        }
    }

    /**
     * 获取签名密钥
     */
    private SecretKey getSigningKey() {
        byte[] keyBytes = jwtProperties.getSecret().getBytes();
        return Keys.hmacShaKeyFor(keyBytes);
    }
}
