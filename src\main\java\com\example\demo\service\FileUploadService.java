package com.example.demo.service;

import com.example.demo.dto.response.FileUploadResponseDTO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传服务接口
 */
public interface FileUploadService {

    /**
     * 上传头像文件
     * @param file 文件
     * @param userId 用户ID
     * @return 上传结果
     */
    FileUploadResponseDTO uploadAvatar(MultipartFile file, Long userId);

    /**
     * 上传项目图片
     * @param file 文件
     * @param projectId 项目ID
     * @return 上传结果
     */
    FileUploadResponseDTO uploadProjectImage(MultipartFile file, Long projectId);

    /**
     * 上传申请证明材料
     * @param file 文件
     * @param requestId 申请ID
     * @return 上传结果
     */
    FileUploadResponseDTO uploadRequestDocument(MultipartFile file, Long requestId);

    /**
     * 通用文件上传
     * @param file 文件
     * @param category 文件分类（avatars, projects, documents等）
     * @param relatedId 关联ID
     * @return 上传结果
     */
    FileUploadResponseDTO uploadFile(MultipartFile file, String category, Long relatedId);

    /**
     * 删除文件
     * @param filePath 文件路径
     * @return 是否删除成功
     */
    boolean deleteFile(String filePath);

    /**
     * 验证文件类型
     * @param file 文件
     * @param allowedTypes 允许的文件类型
     * @return 是否有效
     */
    boolean validateFileType(MultipartFile file, String[] allowedTypes);

    /**
     * 验证文件大小
     * @param file 文件
     * @param maxSize 最大大小（字节）
     * @return 是否有效
     */
    boolean validateFileSize(MultipartFile file, long maxSize);
}
