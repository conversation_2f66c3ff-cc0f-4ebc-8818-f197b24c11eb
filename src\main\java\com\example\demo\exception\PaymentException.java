package com.example.demo.exception;

/**
 * 支付相关异常
 */
public class PaymentException extends BusinessException {

    public PaymentException() {
        super(ErrorCode.PAYMENT_FAILED);
    }

    public PaymentException(String message) {
        super(ErrorCode.PAYMENT_FAILED, message);
    }

    public PaymentException(ErrorCode errorCode) {
        super(errorCode);
    }

    public PaymentException(ErrorCode errorCode, String message) {
        super(errorCode, message);
    }

    public PaymentException(Throwable cause) {
        super(ErrorCode.PAYMENT_FAILED, cause);
    }

    /**
     * 支付超时异常
     */
    public static PaymentException timeout() {
        return new PaymentException(ErrorCode.PAYMENT_TIMEOUT);
    }

    /**
     * 支付取消异常
     */
    public static PaymentException cancelled() {
        return new PaymentException(ErrorCode.PAYMENT_CANCELLED);
    }

    /**
     * 退款失败异常
     */
    public static PaymentException refundFailed(String reason) {
        return new PaymentException(ErrorCode.REFUND_FAILED, "退款失败：" + reason);
    }
}
