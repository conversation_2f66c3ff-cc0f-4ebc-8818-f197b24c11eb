package com.example.demo.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.entity.User;
import com.example.demo.exception.*;
import com.example.demo.mapper.UserMapper;
import com.example.demo.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户服务实现类
 * 实现用户相关的业务逻辑
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    @Lazy
    private PasswordEncoder passwordEncoder;

    @Override
    public User register(User user) {
        log.info("用户注册: {}", user.getUsername());
        
        // 业务校验
        validateUserForRegister(user);
        
        // 检查用户名和邮箱唯一性
        if (isUsernameExists(user.getUsername(), null)) {
            throw new BusinessException(ErrorCode.USERNAME_DUPLICATE);
        }

        if (StringUtils.hasText(user.getEmail()) && isEmailExists(user.getEmail(), null)) {
            throw new BusinessException(ErrorCode.EMAIL_DUPLICATE);
        }

        // 密码加密
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        
        // 设置默认值
        if (user.getRole() == null) {
            user.setRole(User.Role.USER.getCode());
        }
        if (user.getStatus() == null) {
            user.setStatus(User.Status.NORMAL.getCode());
        }
        
        // 保存用户
        int result = userMapper.insert(user);
        if (result > 0) {
            log.info("用户注册成功: {}", user.getUsername());
            // 清除密码字段，避免返回敏感信息
            user.setPassword(null);
            return user;
        } else {
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "用户注册失败");
        }
    }

    @Override
    public User login(String username, String password) {
        log.info("用户登录: {}", username);
        
        if (!StringUtils.hasText(username) || !StringUtils.hasText(password)) {
            throw new ValidationException("用户名和密码不能为空");
        }

        User user = findByUsername(username);
        if (user == null) {
            throw new UserNotFoundException("username", username);
        }

        if (user.getStatus() != User.Status.NORMAL.getCode()) {
            throw new BusinessException(ErrorCode.ACCOUNT_DISABLED);
        }

        if (!passwordEncoder.matches(password, user.getPassword())) {
            throw new BusinessException(ErrorCode.LOGIN_FAILED);
        }
        
        log.info("用户登录成功: {}", username);
        // 清除密码字段
        user.setPassword(null);
        return user;
    }

    @Override
    @Transactional(readOnly = true)
    public User findByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return null;
        }
        return userMapper.findByUsername(username);
    }

    @Override
    @Transactional(readOnly = true)
    public User findByEmail(String email) {
        if (!StringUtils.hasText(email)) {
            return null;
        }
        return userMapper.findByEmail(email);
    }

    @Override
    @Transactional(readOnly = true)
    public User findById(Long id) {
        if (id == null || id <= 0) {
            return null;
        }
        User user = userMapper.selectById(id);
        if (user != null) {
            // 清除密码字段
            user.setPassword(null);
        }
        return user;
    }

    @Override
    public User updateUser(User user) {
        log.info("更新用户信息: {}", user.getId());
        
        if (user.getId() == null || user.getId() <= 0) {
            throw new RuntimeException("用户ID不能为空");
        }
        
        User existingUser = userMapper.selectById(user.getId());
        if (existingUser == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 检查用户名和邮箱唯一性
        if (StringUtils.hasText(user.getUsername()) && 
            !user.getUsername().equals(existingUser.getUsername()) &&
            isUsernameExists(user.getUsername(), user.getId())) {
            throw new RuntimeException("用户名已存在");
        }
        
        if (StringUtils.hasText(user.getEmail()) && 
            !user.getEmail().equals(existingUser.getEmail()) &&
            isEmailExists(user.getEmail(), user.getId())) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 不允许通过此方法更新密码
        user.setPassword(null);
        
        int result = userMapper.updateById(user);
        if (result > 0) {
            log.info("用户信息更新成功: {}", user.getId());
            return findById(user.getId());
        } else {
            throw new RuntimeException("用户信息更新失败");
        }
    }

    @Override
    public boolean updatePassword(Long userId, String oldPassword, String newPassword) {
        log.info("更新用户密码: {}", userId);
        
        if (userId == null || userId <= 0) {
            throw new RuntimeException("用户ID不能为空");
        }
        
        if (!StringUtils.hasText(oldPassword) || !StringUtils.hasText(newPassword)) {
            throw new ValidationException("旧密码和新密码不能为空");
        }

        if (newPassword.length() < 6) {
            throw new ValidationException(ErrorCode.PASSWORD_TOO_WEAK, "新密码长度不能少于6位");
        }

        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new UserNotFoundException(userId);
        }

        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new BusinessException(ErrorCode.OLD_PASSWORD_ERROR);
        }

        user.setPassword(passwordEncoder.encode(newPassword));
        int result = userMapper.updateById(user);
        
        if (result > 0) {
            log.info("用户密码更新成功: {}", userId);
            return true;
        } else {
            throw new RuntimeException("密码更新失败");
        }
    }

    @Override
    public boolean updateUserStatus(Long userId, Integer status) {
        log.info("更新用户状态: userId={}, status={}", userId, status);
        
        if (userId == null || userId <= 0) {
            throw new RuntimeException("用户ID不能为空");
        }
        
        if (status == null || (status != User.Status.NORMAL.getCode() && status != User.Status.DISABLED.getCode())) {
            throw new RuntimeException("状态值无效");
        }
        
        int result = userMapper.updateUserStatus(userId, status);
        if (result > 0) {
            log.info("用户状态更新成功: userId={}, status={}", userId, status);
            return true;
        } else {
            throw new RuntimeException("用户状态更新失败");
        }
    }

    @Override
    public boolean resetUserPassword(Long userId) {
        log.info("重置用户密码: userId={}", userId);

        if (userId == null || userId <= 0) {
            throw new RuntimeException("用户ID不能为空");
        }

        // 检查用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 重置密码为默认密码 12345678
        String defaultPassword = "12345678";
        String encodedPassword = passwordEncoder.encode(defaultPassword);

        // 更新密码
        User updateUser = new User();
        updateUser.setId(userId);
        updateUser.setPassword(encodedPassword);

        int result = userMapper.updateById(updateUser);
        if (result > 0) {
            log.info("用户密码重置成功: userId={}", userId);
            return true;
        } else {
            throw new RuntimeException("用户密码重置失败");
        }
    }

    @Override
    @Transactional(readOnly = true)
    public IPage<User> findUsersWithConditions(Page<User> page, String role, Integer status, String keyword) {
        IPage<User> result = userMapper.findUsersWithConditions(page, role, status, keyword);
        // 清除所有用户的密码字段
        result.getRecords().forEach(user -> user.setPassword(null));
        return result;
    }

    @Override
    @Transactional(readOnly = true)
    public List<User> findByRole(String role) {
        if (!StringUtils.hasText(role)) {
            throw new RuntimeException("角色不能为空");
        }
        
        List<User> users = userMapper.findByRole(role);
        // 清除密码字段
        users.forEach(user -> user.setPassword(null));
        return users;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isUsernameExists(String username, Long excludeId) {
        if (!StringUtils.hasText(username)) {
            return false;
        }
        Long count = userMapper.checkUsernameExists(username, excludeId);
        return count != null && count > 0;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isEmailExists(String email, Long excludeId) {
        if (!StringUtils.hasText(email)) {
            return false;
        }
        Long count = userMapper.checkEmailExists(email, excludeId);
        return count != null && count > 0;
    }

    @Override
    public boolean deleteUser(Long userId) {
        log.info("删除用户: {}", userId);
        
        if (userId == null || userId <= 0) {
            throw new RuntimeException("用户ID不能为空");
        }
        
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 不允许删除管理员
        if (User.Role.ADMIN.getCode().equals(user.getRole())) {
            throw new RuntimeException("不允许删除管理员用户");
        }
        
        int result = userMapper.deleteById(userId);
        if (result > 0) {
            log.info("用户删除成功: {}", userId);
            return true;
        } else {
            throw new RuntimeException("用户删除失败");
        }
    }

    @Override
    public boolean batchDeleteUsers(List<Long> userIds) {
        log.info("批量删除用户: {}", userIds);
        
        if (userIds == null || userIds.isEmpty()) {
            throw new RuntimeException("用户ID列表不能为空");
        }
        
        // 检查是否包含管理员
        for (Long userId : userIds) {
            User user = userMapper.selectById(userId);
            if (user != null && User.Role.ADMIN.getCode().equals(user.getRole())) {
                throw new RuntimeException("不允许删除管理员用户");
            }
        }
        
        int result = userMapper.deleteBatchIds(userIds);
        if (result > 0) {
            log.info("批量删除用户成功: 删除{}个用户", result);
            return true;
        } else {
            throw new RuntimeException("批量删除用户失败");
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Long countUsers(String role, Integer status) {
        return userMapper.countUsers(role, status);
    }

    @Override
    @Transactional(readOnly = true)
    public List<User> getAllAdmins() {
        List<User> admins = userMapper.findByRole(User.Role.ADMIN.getCode());
        // 清除密码字段
        admins.forEach(user -> user.setPassword(null));
        return admins;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasPermission(Long userId, String requiredRole) {
        if (userId == null || userId <= 0 || !StringUtils.hasText(requiredRole)) {
            return false;
        }
        
        User user = userMapper.selectById(userId);
        if (user == null || user.getStatus() != User.Status.NORMAL.getCode()) {
            return false;
        }
        
        // 管理员拥有所有权限
        if (User.Role.ADMIN.getCode().equals(user.getRole())) {
            return true;
        }
        
        // 检查具体角色权限
        return requiredRole.equals(user.getRole());
    }

    @Override
    public boolean resetPassword(Long userId, String newPassword) {
        log.info("重置用户密码: {}", userId);
        
        if (userId == null || userId <= 0) {
            throw new RuntimeException("用户ID不能为空");
        }
        
        if (!StringUtils.hasText(newPassword) || newPassword.length() < 6) {
            throw new RuntimeException("新密码不能为空且长度不能少于6位");
        }
        
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        user.setPassword(passwordEncoder.encode(newPassword));
        int result = userMapper.updateById(user);
        
        if (result > 0) {
            log.info("用户密码重置成功: {}", userId);
            return true;
        } else {
            throw new RuntimeException("密码重置失败");
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Object getUserStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 总用户数
        Long totalUsers = countUsers(null, null);
        statistics.put("totalUsers", totalUsers);
        
        // 正常用户数
        Long normalUsers = countUsers(null, User.Status.NORMAL.getCode());
        statistics.put("normalUsers", normalUsers);
        
        // 禁用用户数
        Long disabledUsers = countUsers(null, User.Status.DISABLED.getCode());
        statistics.put("disabledUsers", disabledUsers);
        
        // 管理员数量
        Long adminCount = countUsers(User.Role.ADMIN.getCode(), null);
        statistics.put("adminCount", adminCount);
        
        // 普通用户数量
        Long userCount = countUsers(User.Role.USER.getCode(), null);
        statistics.put("userCount", userCount);
        
        return statistics;
    }

    /**
     * 验证用户注册信息
     */
    private void validateUserForRegister(User user) {
        if (user == null) {
            throw new RuntimeException("用户信息不能为空");
        }
        
        if (!StringUtils.hasText(user.getUsername())) {
            throw new RuntimeException("用户名不能为空");
        }
        
        if (user.getUsername().length() < 3 || user.getUsername().length() > 20) {
            throw new RuntimeException("用户名长度必须在3-20位之间");
        }
        
        if (!StringUtils.hasText(user.getPassword())) {
            throw new RuntimeException("密码不能为空");
        }
        
        if (user.getPassword().length() < 6) {
            throw new RuntimeException("密码长度不能少于6位");
        }
        
        if (StringUtils.hasText(user.getEmail()) && !isValidEmail(user.getEmail())) {
            throw new RuntimeException("邮箱格式不正确");
        }
    }

    /**
     * 验证邮箱格式
     */
    private boolean isValidEmail(String email) {
        return email != null && email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    }

    @Override
    @Transactional(readOnly = true)
    public Long countTodayNewUsers() {
        // 使用现有的countUsers方法，这里简化实现
        // 实际应该查询今天注册的用户数量
        Long totalUsers = userMapper.countUsers(null, null);
        return totalUsers / 10; // 模拟今日新增用户数
    }
}
