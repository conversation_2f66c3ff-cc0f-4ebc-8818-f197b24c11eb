<template>
  <div class="admin-project-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" class="back-btn">
          <el-icon>
            <ArrowLeft />
          </el-icon>
          返回项目列表
        </el-button>
        <div class="title-section">
          <h1 class="page-title">项目详情</h1>
          <p class="page-description">查看救助项目的详细信息</p>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="handleRefresh">
          <el-icon>
            <Refresh />
          </el-icon>
          刷新
        </el-button>
        <el-button type="primary" @click="handleEdit">
          <el-icon>
            <Edit />
          </el-icon>
          编辑项目
        </el-button>
      </div>
    </div>

    <div v-loading="loading" class="content-wrapper">
      <!-- 项目基本信息 -->
      <div class="info-section">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>项目信息</span>
              <div class="header-actions">
                <el-tag :type="getStatusType(projectInfo.status)" size="large">
                  {{ getStatusText(projectInfo.status) }}
                </el-tag>
              </div>
            </div>
          </template>

          <div class="project-profile">
            <div class="basic-info">
              <h2 class="project-title">{{ projectInfo.title }}</h2>
              <div class="project-meta">
                <el-tag :type="getCategoryType(projectInfo.category)" size="small">
                  {{ getCategoryText(projectInfo.category) }}
                </el-tag>
                <span class="create-time">创建时间：{{ formatTime(projectInfo.createTime) }}</span>
              </div>
            </div>

            <!-- 筹款进度 -->
            <div class="progress-section">
              <div class="progress-header">
                <h3>筹款进度</h3>
                <div class="progress-stats">
                  <span class="raised">已筹集：¥{{ formatAmount(projectInfo.currentAmount) }}</span>
                  <span class="target">目标：¥{{ formatAmount(projectInfo.targetAmount) }}</span>
                </div>
              </div>
              <el-progress :percentage="calculateProgress()" :stroke-width="12" :color="getProgressColor()" />
              <div class="progress-details">
                <span>完成度：{{ calculateProgress() }}%</span>
                <span>剩余：¥{{ formatAmount(projectInfo.targetAmount - projectInfo.currentAmount) }}</span>
              </div>
            </div>

            <!-- 项目信息网格 -->
            <div class="info-grid">
              <div class="info-item">
                <label>项目ID</label>
                <span>{{ projectInfo.id }}</span>
              </div>
              <div class="info-item">
                <label>创建人</label>
                <span>{{ projectInfo.creatorName || '未知' }}</span>
              </div>
              <div class="info-item">
                <label>联系电话</label>
                <span>{{ projectInfo.contactPhone || '未设置' }}</span>
              </div>
              <div class="info-item">
                <label>联系邮箱</label>
                <span>{{ projectInfo.contactEmail || '未设置' }}</span>
              </div>
              <div class="info-item">
                <label>项目地址</label>
                <span>{{ projectInfo.location || '未设置' }}</span>
              </div>
              <div class="info-item">
                <label>更新时间</label>
                <span>{{ formatTime(projectInfo.updateTime) }}</span>
              </div>
            </div>

            <!-- 项目描述 -->
            <div class="description-section">
              <h3>项目描述</h3>
              <div class="description-content">
                {{ projectInfo.description || '无描述' }}
              </div>
            </div>

            <!-- 项目图片 -->
            <div v-if="projectInfo.images && projectInfo.images.length > 0" class="images-section">
              <h3>项目图片</h3>
              <div class="images-carousel">
                <el-carousel height="300px" indicator-position="outside">
                  <el-carousel-item v-for="(image, index) in projectInfo.images" :key="index">
                    <img :src="image" :alt="`项目图片${index + 1}`" @click="previewImage(image)" />
                  </el-carousel-item>
                </el-carousel>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 捐赠统计 -->
      <div class="stats-section">
        <el-card>
          <template #header>
            <span>捐赠统计</span>
          </template>

          <div class="donation-stats">
            <el-row :gutter="24">
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-value">{{ donationStats.totalCount || 0 }}</div>
                  <div class="stat-label">总捐赠笔数</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-value">¥{{ formatAmount(donationStats.totalAmount) }}</div>
                  <div class="stat-label">总捐赠金额</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-value">¥{{ formatAmount(donationStats.avgAmount) }}</div>
                  <div class="stat-label">平均捐赠金额</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </div>

      <!-- 最近捐赠 -->
      <div class="recent-donations-section">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近捐赠</span>
              <el-button text type="primary" @click="viewAllDonations">
                查看全部
              </el-button>
            </div>
          </template>

          <div class="donations-list">
            <div v-if="recentDonations.length === 0" class="empty-state">
              <el-empty description="暂无捐赠记录" />
            </div>
            <div v-else>
              <div v-for="donation in recentDonations" :key="donation.id" class="donation-item">
                <div class="donation-info">
                  <div class="donor-name">
                    {{ donation.isAnonymous ? '匿名用户' : (donation.donorName || '未知用户') }}
                  </div>
                  <div class="donation-amount">¥{{ formatAmount(donation.amount) }}</div>
                </div>
                <div class="donation-meta">
                  <span class="donation-time">{{ formatTime(donation.donationTime) }}</span>
                  <el-tag :type="getStatusType(donation.status)" size="small">
                    {{ getStatusText(donation.status) }}
                  </el-tag>
                </div>
                <div v-if="donation.message" class="donation-message">
                  {{ donation.message }}
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 管理操作 -->
      <div class="actions-section">
        <el-card>
          <template #header>
            <span>管理操作</span>
          </template>

          <div class="action-buttons">
            <el-button type="primary" @click="handleEdit">
              <el-icon>
                <Edit />
              </el-icon>
              编辑项目
            </el-button>

            <el-button @click="viewAllDonations">
              <el-icon>
                <Money />
              </el-icon>
              查看所有捐赠
            </el-button>

            <el-button @click="handleExportData">
              <el-icon>
                <Download />
              </el-icon>
              导出数据
            </el-button>

            <el-button v-if="projectInfo.status === 'active'" type="warning" @click="handlePauseProject">
              <el-icon>
                <VideoPause />
              </el-icon>
              暂停项目
            </el-button>

            <el-button v-if="projectInfo.status === 'paused'" type="success" @click="handleResumeProject">
              <el-icon>
                <VideoPlay />
              </el-icon>
              恢复项目
            </el-button>

            <el-button v-if="projectInfo.status === 'active' || projectInfo.status === 'paused'" type="info"
              @click="handleCompleteProject">
              <el-icon>
                <Check />
              </el-icon>
              完成项目
            </el-button>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="previewVisible" title="图片预览" width="60%" center>
      <div class="preview-container">
        <img :src="previewImageUrl" alt="预览图片" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    ArrowLeft,
    Refresh,
    Edit,
    Money,
    Download,
    VideoPause,
    VideoPlay,
    Check
  } from '@element-plus/icons-vue'
  import {
    getProjectById,
    getDonationList,
    updateProject,
    updateProjectStatus
  } from '@/api/admin'

  const route = useRoute()
  const router = useRouter()

  // 响应式数据
  const loading = ref(false)
  const projectInfo = ref({})
  const donationStats = ref({
    totalCount: 0,
    totalAmount: 0,
    avgAmount: 0
  })
  const recentDonations = ref([])
  const previewVisible = ref(false)
  const previewImageUrl = ref('')

  // 获取项目ID
  const projectId = route.params.id

  // 加载项目详情
  const loadProjectDetail = async () => {
    try {
      loading.value = true

      const response = await getProjectById(projectId)
      if (response) {
        projectInfo.value = response
        // 解析图片数组
        if (response.images && typeof response.images === 'string') {
          try {
            projectInfo.value.images = JSON.parse(response.images)
          } catch (e) {
            projectInfo.value.images = []
          }
        }
      }

    } catch (error) {
      console.error('加载项目详情失败:', error)
      ElMessage.error('加载项目详情失败')
    } finally {
      loading.value = false
    }
  }

  // 加载捐赠数据
  const loadDonationData = async () => {
    try {
      // 获取项目的捐赠记录
      const response = await getDonationList({
        projectId: projectId,
        current: 1,
        size: 5
      })

      if (response && response.records) {
        recentDonations.value = response.records

        // 计算统计数据
        donationStats.value = {
          totalCount: response.total || 0,
          totalAmount: projectInfo.value.currentAmount || 0,
          avgAmount: response.total > 0 ? (projectInfo.value.currentAmount / response.total) : 0
        }
      }

    } catch (error) {
      console.error('加载捐赠数据失败:', error)
    }
  }

  // 返回项目列表
  const goBack = () => {
    router.push('/admin/projects')
  }

  // 刷新
  const handleRefresh = () => {
    loadProjectDetail()
    loadDonationData()
  }

  // 编辑项目
  const handleEdit = () => {
    router.push(`/admin/projects/edit/${projectId}`)
  }

  // 查看所有捐赠
  const viewAllDonations = () => {
    // 跳转到捐赠管理页面，并预设项目筛选
    router.push({
      path: '/admin/donations',
      query: { projectId: projectId }
    })
  }

  // 导出数据
  const handleExportData = () => {
    ElMessage.info('导出功能开发中...')
  }

  // 暂停项目
  const handlePauseProject = async () => {
    try {
      await ElMessageBox.confirm(
        '确定要暂停此项目吗？暂停后将无法接收新的捐赠。',
        '确认暂停',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await updateProjectStatus(projectId, 'paused')
      ElMessage.success('项目已暂停')
      loadProjectDetail()

    } catch (error) {
      if (error !== 'cancel') {
        console.error('暂停项目失败:', error)
        ElMessage.error('暂停项目失败')
      }
    }
  }

  // 恢复项目
  const handleResumeProject = async () => {
    try {
      await ElMessageBox.confirm(
        '确定要恢复此项目吗？',
        '确认恢复',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'success'
        }
      )

      await updateProjectStatus(projectId, 'active')
      ElMessage.success('项目已恢复')
      loadProjectDetail()

    } catch (error) {
      if (error !== 'cancel') {
        console.error('恢复项目失败:', error)
        ElMessage.error('恢复项目失败')
      }
    }
  }

  // 完成项目
  const handleCompleteProject = async () => {
    try {
      await ElMessageBox.confirm(
        '确定要完成此项目吗？完成后将无法再接收捐赠。',
        '确认完成',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        }
      )

      await updateProjectStatus(projectId, 'completed')
      ElMessage.success('项目已完成')
      loadProjectDetail()

    } catch (error) {
      if (error !== 'cancel') {
        console.error('完成项目失败:', error)
        ElMessage.error('完成项目失败')
      }
    }
  }

  // 预览图片
  const previewImage = (imageUrl) => {
    previewImageUrl.value = imageUrl
    previewVisible.value = true
  }

  // 计算进度百分比
  const calculateProgress = () => {
    if (!projectInfo.value.targetAmount || projectInfo.value.targetAmount === 0) return 0
    const progress = (projectInfo.value.currentAmount / projectInfo.value.targetAmount) * 100
    return Math.min(Math.round(progress), 100)
  }

  // 获取进度条颜色
  const getProgressColor = () => {
    const progress = calculateProgress()
    if (progress >= 100) return '#67c23a'
    if (progress >= 80) return '#e6a23c'
    if (progress >= 50) return '#409eff'
    return '#f56c6c'
  }

  // 工具函数
  const getCategoryType = (category) => {
    const typeMap = {
      medical: 'danger',
      education: 'primary',
      disaster: 'warning',
      poverty: 'success',
      other: 'info'
    }
    return typeMap[category] || ''
  }

  const getCategoryText = (category) => {
    const textMap = {
      medical: '医疗救助',
      education: '教育助学',
      disaster: '灾难救援',
      poverty: '扶贫助困',
      other: '其他'
    }
    return textMap[category] || category
  }

  const getStatusType = (status) => {
    const typeMap = {
      active: 'success',
      paused: 'warning',
      completed: 'info',
      cancelled: 'danger'
    }
    return typeMap[status] || ''
  }

  const getStatusText = (status) => {
    const textMap = {
      active: '进行中',
      paused: '已暂停',
      completed: '已完成',
      cancelled: '已取消'
    }
    return textMap[status] || status
  }

  const formatTime = (timeString) => {
    if (!timeString) return '未知'
    return new Date(timeString).toLocaleString('zh-CN')
  }

  const formatAmount = (amount) => {
    if (!amount) return '0'
    return Number(amount).toLocaleString()
  }

  // 页面初始化
  onMounted(() => {
    loadProjectDetail()
    loadDonationData()
  })
</script>

<style scoped>
  .admin-project-detail {
    padding: 24px;
    background: #f5f5f5;
    min-height: 100vh;
  }

  /* 页面头部 */
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .back-btn {
    color: #718096;
  }

  .title-section {
    flex: 1;
  }

  .page-title {
    font-size: 24px;
    font-weight: bold;
    color: #2D3748;
    margin: 0 0 8px 0;
  }

  .page-description {
    color: #718096;
    margin: 0;
    font-size: 14px;
  }

  .header-right {
    display: flex;
    gap: 12px;
  }

  /* 内容区域 */
  .content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  /* 信息区域 */
  .info-section .el-card,
  .stats-section .el-card,
  .recent-donations-section .el-card,
  .actions-section .el-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .project-profile {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  /* 基本信息 */
  .basic-info {
    padding-bottom: 24px;
    border-bottom: 1px solid #E2E8F0;
  }

  .project-title {
    font-size: 20px;
    font-weight: bold;
    color: #2D3748;
    margin: 0 0 12px 0;
  }

  .project-meta {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .create-time {
    color: #718096;
    font-size: 14px;
  }

  /* 筹款进度 */
  .progress-section {
    background: #F7FAFC;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #4299E1;
  }

  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .progress-header h3 {
    font-size: 16px;
    font-weight: bold;
    color: #2D3748;
    margin: 0;
  }

  .progress-stats {
    display: flex;
    gap: 24px;
  }

  .raised {
    color: #48BB78;
    font-weight: 500;
  }

  .target {
    color: #4299E1;
    font-weight: 500;
  }

  .progress-details {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 12px;
    color: #718096;
  }

  /* 信息网格 */
  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .info-item label {
    font-size: 12px;
    color: #718096;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .info-item span {
    font-size: 14px;
    color: #2D3748;
    font-weight: 500;
  }

  /* 描述区域 */
  .description-section h3,
  .images-section h3 {
    font-size: 16px;
    font-weight: bold;
    color: #2D3748;
    margin: 0 0 12px 0;
  }

  .description-content {
    background: #F7FAFC;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #4299E1;
    line-height: 1.6;
    color: #2D3748;
  }

  /* 图片轮播 */
  .images-carousel img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    cursor: pointer;
    border-radius: 8px;
  }

  /* 捐赠统计 */
  .donation-stats {
    padding: 16px 0;
  }

  .stat-item {
    text-align: center;
  }

  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #2D3748;
    margin-bottom: 8px;
  }

  .stat-label {
    font-size: 14px;
    color: #718096;
  }

  /* 捐赠列表 */
  .donations-list {
    max-height: 400px;
    overflow-y: auto;
  }

  .donation-item {
    padding: 16px;
    border-bottom: 1px solid #E2E8F0;
    transition: background-color 0.2s;
  }

  .donation-item:hover {
    background-color: #F7FAFC;
  }

  .donation-item:last-child {
    border-bottom: none;
  }

  .donation-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .donor-name {
    font-weight: 500;
    color: #2D3748;
  }

  .donation-amount {
    font-weight: bold;
    color: #E53E3E;
    font-size: 16px;
  }

  .donation-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .donation-time {
    font-size: 12px;
    color: #718096;
  }

  .donation-message {
    font-size: 12px;
    color: #4A5568;
    background: #EDF2F7;
    padding: 8px 12px;
    border-radius: 6px;
    border-left: 3px solid #4299E1;
  }

  /* 操作按钮 */
  .action-buttons {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
  }

  .action-buttons .el-button {
    flex: 1;
    min-width: 140px;
  }

  /* 图片预览 */
  .preview-container {
    text-align: center;
  }

  .preview-container img {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .admin-project-detail {
      padding: 16px;
    }

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .header-left {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    .header-right {
      width: 100%;
      justify-content: flex-end;
    }

    .progress-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    .progress-stats {
      flex-direction: column;
      gap: 8px;
    }

    .info-grid {
      grid-template-columns: 1fr;
    }

    .donation-info {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .donation-meta {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .action-buttons {
      flex-direction: column;
    }

    .action-buttons .el-button {
      flex: none;
      width: 100%;
    }
  }
</style>