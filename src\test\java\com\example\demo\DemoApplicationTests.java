package com.example.demo;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = CharityPlatformApplication.class)
class CharityPlatformApplicationTests {

    @Test
    void contextLoads() {
        // 测试Spring上下文是否能正常加载
    }

    @Test
    void applicationStarts() {
        // 测试应用是否能正常启动
        // 如果能到这里说明Spring Boot应用启动成功
    }

}
