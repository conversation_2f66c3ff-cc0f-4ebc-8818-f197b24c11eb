package com.example.demo.controller;

import com.example.demo.dto.ApiResponse;
import com.example.demo.dto.PageDTO;
import com.example.demo.security.CustomUserDetailsService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.CrossOrigin;

import java.util.List;
import java.util.stream.Collectors;
import java.util.function.Function;

/**
 * 基础Controller类
 * 提供通用的响应处理方法
 */
@Slf4j
@CrossOrigin(origins = "*", maxAge = 3600)
public abstract class BaseController {

    /**
     * 返回成功响应
     */
    protected <T> ApiResponse<T> success(T data) {
        return ApiResponse.success(data);
    }

    /**
     * 返回成功响应（无数据）
     */
    protected <T> ApiResponse<T> success() {
        return ApiResponse.success();
    }

    /**
     * 返回成功响应（自定义消息）
     */
    protected <T> ApiResponse<T> success(String message, T data) {
        return ApiResponse.success(message, data);
    }

    /**
     * 返回错误响应
     */
    protected <T> ApiResponse<T> error(String message) {
        return ApiResponse.error(message);
    }

    /**
     * 返回错误响应（自定义状态码）
     */
    protected <T> ApiResponse<T> error(Integer code, String message) {
        return ApiResponse.error(code, message);
    }

    /**
     * 返回参数错误响应
     */
    protected <T> ApiResponse<T> badRequest(String message) {
        return ApiResponse.badRequest(message);
    }

    /**
     * 返回未授权响应
     */
    protected <T> ApiResponse<T> unauthorized(String message) {
        return ApiResponse.unauthorized(message);
    }

    /**
     * 返回禁止访问响应
     */
    protected <T> ApiResponse<T> forbidden(String message) {
        return ApiResponse.forbidden(message);
    }

    /**
     * 返回资源不存在响应
     */
    protected <T> ApiResponse<T> notFound(String message) {
        return ApiResponse.notFound(message);
    }

    /**
     * 转换分页结果
     */
    protected <T, R> ApiResponse<PageDTO<R>> convertPageResult(IPage<T> page, Function<T, R> converter) {
        List<R> records = page.getRecords().stream()
                .map(converter)
                .collect(Collectors.toList());
        
        PageDTO<R> pageDTO = PageDTO.of(records, page.getCurrent(), page.getSize(), page.getTotal());
        return success(pageDTO);
    }

    /**
     * 转换列表结果
     */
    protected <T, R> List<R> convertList(List<T> list, Function<T, R> converter) {
        return list.stream()
                .map(converter)
                .collect(Collectors.toList());
    }

    /**
     * 获取当前用户ID（从JWT token中获取）
     */
    protected Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof CustomUserDetailsService.CustomUserPrincipal) {
            CustomUserDetailsService.CustomUserPrincipal userPrincipal =
                (CustomUserDetailsService.CustomUserPrincipal) authentication.getPrincipal();
            return userPrincipal.getUserId();
        }
        return null;
    }

    /**
     * 获取当前用户角色
     */
    protected String getCurrentUserRole() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof CustomUserDetailsService.CustomUserPrincipal) {
            CustomUserDetailsService.CustomUserPrincipal userPrincipal =
                (CustomUserDetailsService.CustomUserPrincipal) authentication.getPrincipal();
            return userPrincipal.getRole();
        }
        return null;
    }

    /**
     * 获取当前用户名
     */
    protected String getCurrentUsername() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof CustomUserDetailsService.CustomUserPrincipal) {
            CustomUserDetailsService.CustomUserPrincipal userPrincipal =
                (CustomUserDetailsService.CustomUserPrincipal) authentication.getPrincipal();
            return userPrincipal.getUsername();
        }
        return null;
    }

    /**
     * 获取当前用户详情
     */
    protected CustomUserDetailsService.CustomUserPrincipal getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof CustomUserDetailsService.CustomUserPrincipal) {
            return (CustomUserDetailsService.CustomUserPrincipal) authentication.getPrincipal();
        }
        return null;
    }

    /**
     * 检查是否为管理员
     */
    protected boolean isAdmin() {
        CustomUserDetailsService.CustomUserPrincipal user = getCurrentUser();
        return user != null && user.isAdmin();
    }

    /**
     * 记录操作日志
     */
    protected void logOperation(String operation, Object... params) {
        log.info("用户操作: userId={}, operation={}, params={}", 
                getCurrentUserId(), operation, params);
    }
}
