package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 捐赠记录实体类
 * 对应数据库表: donations
 */
@Data
@TableName("donations")
@Schema(description = "捐赠记录实体")
public class Donation {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "捐赠ID", example = "1")
    private Long id;

    @TableField("user_id")
    @Schema(description = "捐赠用户ID", example = "2")
    private Long userId;

    @TableField("project_id")
    @Schema(description = "项目ID", example = "1")
    private Long projectId;

    @TableField("amount")
    @Schema(description = "捐赠金额", example = "1000.00")
    private BigDecimal amount;

    @TableField("payment_method")
    @Schema(description = "支付方式: alipay-支付宝, wechat-微信, bank-银行转账", example = "alipay")
    private String paymentMethod;

    @TableField("transaction_id")
    @Schema(description = "交易流水号", example = "ALI20241201001")
    private String transactionId;

    @TableField("message")
    @Schema(description = "捐赠留言", example = "希望小明早日康复！")
    private String message;

    @TableField("is_anonymous")
    @Schema(description = "是否匿名: 0-否, 1-是", example = "0")
    private Integer isAnonymous;

    @TableField("status")
    @Schema(description = "状态: pending-待支付, success-成功, failed-失败", example = "success")
    private String status;

    @TableField(value = "donation_time", fill = FieldFill.INSERT)
    @Schema(description = "捐赠时间")
    private LocalDateTime donationTime;

    /**
     * 支付方式枚举
     */
    public enum PaymentMethod {
        ALIPAY("alipay", "支付宝"),
        WECHAT("wechat", "微信支付"),
        BANK("bank", "银行转账");

        private final String code;
        private final String description;

        PaymentMethod(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 捐赠状态枚举
     */
    public enum Status {
        PENDING("pending", "待支付"),
        SUCCESS("success", "成功"),
        FAILED("failed", "失败");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 匿名状态枚举
     */
    public enum Anonymous {
        NO(0, "不匿名"),
        YES(1, "匿名");

        private final Integer code;
        private final String description;

        Anonymous(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
