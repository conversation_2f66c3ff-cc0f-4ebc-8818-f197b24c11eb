package com.example.demo.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据库测试控制器
 * 用于测试数据库连接和查看数据库信息
 */
@Tag(name = "数据库测试", description = "测试数据库连接状态和查看数据库信息")
@RestController
@RequestMapping("/database")
public class DatabaseTestController {

    @Autowired
    private DataSource dataSource;

    @Operation(summary = "测试数据库连接", description = "检查数据库连接是否正常")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "数据库连接正常"),
            @ApiResponse(responseCode = "500", description = "数据库连接失败")
    })
    @GetMapping("/test-connection")
    public Map<String, Object> testConnection() {
        Map<String, Object> result = new HashMap<>();
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            
            result.put("status", "success");
            result.put("message", "数据库连接成功！");
            result.put("database_name", metaData.getDatabaseProductName());
            result.put("database_version", metaData.getDatabaseProductVersion());
            result.put("driver_name", metaData.getDriverName());
            result.put("driver_version", metaData.getDriverVersion());
            result.put("url", metaData.getURL());
            result.put("username", metaData.getUserName());
            
        } catch (Exception e) {
            result.put("status", "error");
            result.put("message", "数据库连接失败: " + e.getMessage());
            result.put("error_type", e.getClass().getSimpleName());
        }
        return result;
    }

    @Operation(summary = "查看数据库表", description = "查看charity_platform数据库中的所有表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取表列表成功"),
            @ApiResponse(responseCode = "500", description = "获取表列表失败")
    })
    @GetMapping("/tables")
    public Map<String, Object> getTables() {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, String>> tables = new ArrayList<>();
        
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            ResultSet rs = metaData.getTables("charity_platform", null, "%", new String[]{"TABLE"});
            
            while (rs.next()) {
                Map<String, String> table = new HashMap<>();
                table.put("table_name", rs.getString("TABLE_NAME"));
                table.put("table_type", rs.getString("TABLE_TYPE"));
                table.put("remarks", rs.getString("REMARKS"));
                tables.add(table);
            }
            
            result.put("status", "success");
            result.put("message", "获取表列表成功");
            result.put("table_count", tables.size());
            result.put("tables", tables);
            
        } catch (Exception e) {
            result.put("status", "error");
            result.put("message", "获取表列表失败: " + e.getMessage());
            result.put("error_type", e.getClass().getSimpleName());
        }
        
        return result;
    }

    @Operation(summary = "数据库状态概览", description = "获取数据库的整体状态信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取状态成功")
    })
    @GetMapping("/status")
    public Map<String, Object> getDatabaseStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try (Connection connection = dataSource.getConnection()) {
            result.put("connection_status", "connected");
            result.put("auto_commit", connection.getAutoCommit());
            result.put("read_only", connection.isReadOnly());
            result.put("transaction_isolation", connection.getTransactionIsolation());
            result.put("catalog", connection.getCatalog());
            result.put("schema", connection.getSchema());
            
            // 检查连接池信息（如果使用HikariCP）
            if (dataSource.getClass().getName().contains("Hikari")) {
                result.put("connection_pool", "HikariCP");
            }
            
            result.put("status", "success");
            result.put("message", "数据库状态正常");
            
        } catch (Exception e) {
            result.put("status", "error");
            result.put("message", "获取数据库状态失败: " + e.getMessage());
        }
        
        return result;
    }
}
