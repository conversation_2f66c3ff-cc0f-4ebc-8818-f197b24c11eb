package com.example.demo.service.impl;

import com.example.demo.config.AiConfig;
import com.example.demo.service.AiService;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.service.ArkService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * AI服务实现类
 * 基于火山引擎豆包AI的实现
 */
@Slf4j
@Service
public class AiServiceImpl implements AiService {

    @Autowired
    private AiConfig aiConfig;

    private ArkService arkService;

    /**
     * 对话历史存储 (生产环境建议使用Redis)
     * Key: conversationId, Value: 消息历史列表
     */
    private final Map<Long, List<ChatMessage>> conversationHistories = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        try {
            // 创建连接池
            ConnectionPool connectionPool = new ConnectionPool(
                    aiConfig.getConnectionPool().getMaxIdleConnections(),
                    aiConfig.getConnectionPool().getKeepAliveDuration(),
                    TimeUnit.MINUTES
            );

            // 创建调度器
            Dispatcher dispatcher = new Dispatcher();
            dispatcher.setMaxRequests(aiConfig.getDispatcher().getMaxRequests());
            dispatcher.setMaxRequestsPerHost(aiConfig.getDispatcher().getMaxRequestsPerHost());

            // 初始化ArkService
            arkService = ArkService.builder()
                    .dispatcher(dispatcher)
                    .connectionPool(connectionPool)
                    .baseUrl(aiConfig.getBaseUrl())
                    .apiKey(aiConfig.getApiKey())
                    .build();

            log.info("AI服务初始化成功，模型: {}", aiConfig.getModel());
        } catch (Exception e) {
            log.error("AI服务初始化失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        if (arkService != null) {
            try {
                arkService.shutdownExecutor();
                log.info("AI服务已关闭");
            } catch (Exception e) {
                log.error("关闭AI服务时发生错误", e);
            }
        }
    }

    @Override
    public String chat(String message, Long conversationId) {
        if (!isServiceAvailable()) {
            return "抱歉，AI服务暂时不可用，请稍后再试。";
        }

        try {
            // 获取或创建对话历史
            List<ChatMessage> messages = getOrCreateConversationHistory(conversationId);

            // 添加用户消息
            ChatMessage userMessage = ChatMessage.builder()
                    .role(ChatMessageRole.USER)
                    .content(message)
                    .build();
            messages.add(userMessage);

            // 限制历史记录长度
            limitHistorySize(messages);

            // 创建聊天完成请求
            ChatCompletionRequest request = ChatCompletionRequest.builder()
                    .model(aiConfig.getModel())
                    .messages(new ArrayList<>(messages))
                    .build();

            // 调用AI服务
            Object contentObj = arkService.createChatCompletion(request)
                    .getChoices()
                    .get(0)
                    .getMessage()
                    .getContent();

            String response = contentObj != null ? contentObj.toString() : "抱歉，AI服务暂时无法回复。";

            // 添加AI回复到历史记录
            ChatMessage assistantMessage = ChatMessage.builder()
                    .role(ChatMessageRole.ASSISTANT)
                    .content(response)
                    .build();
            messages.add(assistantMessage);

            log.info("AI对话成功，对话ID: {}, 用户消息长度: {}, AI回复长度: {}", 
                    conversationId, message.length(), response.length());

            return response;

        } catch (Exception e) {
            log.error("AI对话失败，对话ID: {}, 错误: {}", conversationId, e.getMessage(), e);
            return "抱歉，处理您的请求时遇到了问题，请稍后再试。";
        }
    }

    @Override
    public String getWelcomeMessage() {
        return aiConfig.getWelcomeMessage();
    }

    @Override
    public List<String> getPopularQuestions() {
        return Arrays.asList(aiConfig.getPopularQuestions());
    }

    @Override
    public void clearConversationHistory(Long conversationId) {
        if (conversationId != null) {
            conversationHistories.remove(conversationId);
            log.info("已清除对话历史，对话ID: {}", conversationId);
        }
    }

    @Override
    public List<String> getConversationHistory(Long conversationId) {
        if (conversationId == null) {
            return new ArrayList<>();
        }

        List<ChatMessage> messages = conversationHistories.get(conversationId);
        if (messages == null) {
            return new ArrayList<>();
        }

        return messages.stream()
                .map(msg -> {
                    Object content = msg.getContent();
                    String contentStr = content != null ? content.toString() : "";
                    return msg.getRole().value() + ": " + contentStr;
                })
                .toList();
    }

    @Override
    public boolean isServiceAvailable() {
        return arkService != null && 
               aiConfig.getApiKey() != null && 
               !aiConfig.getApiKey().isEmpty() &&
               !"your-api-key-here".equals(aiConfig.getApiKey());
    }

    /**
     * 获取或创建对话历史
     */
    private List<ChatMessage> getOrCreateConversationHistory(Long conversationId) {
        if (conversationId == null) {
            // 如果没有对话ID，创建临时对话
            List<ChatMessage> messages = new ArrayList<>();
            // 添加系统提示词
            messages.add(ChatMessage.builder()
                    .role(ChatMessageRole.SYSTEM)
                    .content(aiConfig.getSystemPrompt())
                    .build());
            return messages;
        }

        return conversationHistories.computeIfAbsent(conversationId, k -> {
            List<ChatMessage> messages = new ArrayList<>();
            // 添加系统提示词
            messages.add(ChatMessage.builder()
                    .role(ChatMessageRole.SYSTEM)
                    .content(aiConfig.getSystemPrompt())
                    .build());
            return messages;
        });
    }

    /**
     * 限制历史记录长度
     */
    private void limitHistorySize(List<ChatMessage> messages) {
        int maxSize = aiConfig.getMaxHistorySize();
        if (messages.size() > maxSize) {
            // 保留系统消息和最近的消息
            ChatMessage systemMessage = messages.get(0);
            List<ChatMessage> recentMessages = messages.subList(
                    Math.max(1, messages.size() - maxSize + 1), 
                    messages.size()
            );
            
            messages.clear();
            messages.add(systemMessage);
            messages.addAll(recentMessages);
        }
    }
}
