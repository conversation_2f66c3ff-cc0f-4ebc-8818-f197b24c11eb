package com.example.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.entity.Donation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 捐赠记录Mapper接口
 * 继承MyBatis Plus的BaseMapper，提供基础的CRUD操作
 */
@Mapper
public interface DonationMapper extends BaseMapper<Donation> {

    /**
     * 根据用户ID查询捐赠记录
     * @param userId 用户ID
     * @return 捐赠记录列表
     */
    @Select("SELECT * FROM donations WHERE user_id = #{userId} ORDER BY donation_time DESC")
    List<Donation> findByUserId(@Param("userId") Long userId);

    /**
     * 根据项目ID查询捐赠记录
     * @param projectId 项目ID
     * @return 捐赠记录列表
     */
    @Select("SELECT * FROM donations WHERE project_id = #{projectId} AND status = 'success' ORDER BY donation_time DESC")
    List<Donation> findByProjectId(@Param("projectId") Long projectId);

    /**
     * 分页查询捐赠记录
     * @param page 分页参数
     * @param userId 用户ID（可选）
     * @param projectId 项目ID（可选）
     * @param status 捐赠状态（可选）
     * @param paymentMethod 支付方式（可选）
     * @return 分页捐赠记录列表
     */
    @Select("<script>" +
            "SELECT d.*, u.username, u.real_name, p.title as project_title " +
            "FROM donations d " +
            "LEFT JOIN users u ON d.user_id = u.id " +
            "LEFT JOIN projects p ON d.project_id = p.id " +
            "WHERE 1=1 " +
            "<if test='userId != null'> AND d.user_id = #{userId} </if>" +
            "<if test='projectId != null'> AND d.project_id = #{projectId} </if>" +
            "<if test='status != null and status != \"\"'> AND d.status = #{status} </if>" +
            "<if test='paymentMethod != null and paymentMethod != \"\"'> AND d.payment_method = #{paymentMethod} </if>" +
            "<if test='startTime != null'> AND d.donation_time >= #{startTime} </if>" +
            "<if test='endTime != null'> AND d.donation_time &lt;= #{endTime} </if>" +
            "ORDER BY d.donation_time DESC" +
            "</script>")
    IPage<Donation> findDonationsWithConditions(Page<Donation> page,
                                               @Param("userId") Long userId,
                                               @Param("projectId") Long projectId,
                                               @Param("status") String status,
                                               @Param("paymentMethod") String paymentMethod,
                                               @Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);

    /**
     * 查询用户总捐赠金额
     * @param userId 用户ID
     * @return 总捐赠金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM donations WHERE user_id = #{userId} AND status = 'success'")
    BigDecimal sumDonationAmountByUser(@Param("userId") Long userId);

    /**
     * 查询项目总捐赠金额
     * @param projectId 项目ID
     * @return 总捐赠金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM donations WHERE project_id = #{projectId} AND status = 'success'")
    BigDecimal sumDonationAmountByProject(@Param("projectId") Long projectId);

    /**
     * 查询项目捐赠人数
     * @param projectId 项目ID
     * @return 捐赠人数
     */
    @Select("SELECT COUNT(DISTINCT user_id) FROM donations WHERE project_id = #{projectId} AND status = 'success'")
    Long countDonorsByProject(@Param("projectId") Long projectId);

    /**
     * 查询最近的捐赠记录（用于展示）
     * @param limit 限制数量
     * @param isAnonymous 是否只查询非匿名捐赠
     * @return 最近捐赠记录列表
     */
    @Select("<script>" +
            "SELECT d.*, u.username, u.real_name, p.title as project_title " +
            "FROM donations d " +
            "LEFT JOIN users u ON d.user_id = u.id " +
            "LEFT JOIN projects p ON d.project_id = p.id " +
            "WHERE d.status = 'success' " +
            "<if test='isAnonymous != null and isAnonymous == false'> AND d.is_anonymous = 0 </if>" +
            "ORDER BY d.donation_time DESC LIMIT #{limit}" +
            "</script>")
    List<Donation> findRecentDonations(@Param("limit") Integer limit, @Param("isAnonymous") Boolean isAnonymous);

    /**
     * 查询大额捐赠记录（金额>=1000）
     * @param limit 限制数量
     * @return 大额捐赠记录列表
     */
    @Select("SELECT d.*, u.username, u.real_name, p.title as project_title " +
            "FROM donations d " +
            "LEFT JOIN users u ON d.user_id = u.id " +
            "LEFT JOIN projects p ON d.project_id = p.id " +
            "WHERE d.status = 'success' AND d.amount >= 1000 AND d.is_anonymous = 0 " +
            "ORDER BY d.amount DESC, d.donation_time DESC LIMIT #{limit}")
    List<Donation> findLargeDonations(@Param("limit") Integer limit);

    /**
     * 统计捐赠数量
     * @param status 捐赠状态（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 捐赠数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM donations WHERE 1=1 " +
            "<if test='status != null and status != \"\"'> AND status = #{status} </if>" +
            "<if test='startTime != null'> AND donation_time >= #{startTime} </if>" +
            "<if test='endTime != null'> AND donation_time &lt;= #{endTime} </if>" +
            "</script>")
    Long countDonations(@Param("status") String status,
                       @Param("startTime") LocalDateTime startTime,
                       @Param("endTime") LocalDateTime endTime);

    /**
     * 统计捐赠总金额
     * @param status 捐赠状态（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 捐赠总金额
     */
    @Select("<script>" +
            "SELECT COALESCE(SUM(amount), 0) FROM donations WHERE 1=1 " +
            "<if test='status != null and status != \"\"'> AND status = #{status} </if>" +
            "<if test='startTime != null'> AND donation_time >= #{startTime} </if>" +
            "<if test='endTime != null'> AND donation_time &lt;= #{endTime} </if>" +
            "</script>")
    BigDecimal sumDonationAmount(@Param("status") String status,
                                @Param("startTime") LocalDateTime startTime,
                                @Param("endTime") LocalDateTime endTime);

    /**
     * 更新捐赠状态
     * @param id 捐赠ID
     * @param status 新状态
     * @return 影响行数
     */
    @Update("UPDATE donations SET status = #{status} WHERE id = #{id}")
    int updateDonationStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 查询捐赠统计信息（按支付方式）
     * @return 支付方式统计列表
     */
    @Select("SELECT payment_method, COUNT(*) as count, COALESCE(SUM(amount), 0) as total_amount " +
            "FROM donations WHERE status = 'success' GROUP BY payment_method")
    List<Object> getDonationStatsByPaymentMethod();

    /**
     * 查询每日捐赠统计（最近30天）
     * @return 每日捐赠统计列表
     */
    @Select("SELECT DATE(donation_time) as donation_date, COUNT(*) as count, COALESCE(SUM(amount), 0) as total_amount " +
            "FROM donations WHERE status = 'success' AND donation_time >= DATE_SUB(NOW(), INTERVAL 30 DAY) " +
            "GROUP BY DATE(donation_time) ORDER BY donation_date DESC")
    List<Object> getDailyDonationStats();
}
