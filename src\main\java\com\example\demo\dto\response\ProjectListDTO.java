package com.example.demo.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 救助项目列表响应DTO（用于分页查询）
 */
@Schema(description = "救助项目列表响应")
public class ProjectListDTO {

    @Schema(description = "项目ID", example = "1")
    private Long id;

    @Schema(description = "项目标题", example = "小明白血病治疗救助")
    private String title;

    @Schema(description = "项目分类", example = "medical")
    private String category;

    @Schema(description = "分类描述", example = "医疗救助")
    private String categoryDescription;

    @Schema(description = "目标金额", example = "300000.00")
    private BigDecimal targetAmount;

    @Schema(description = "当前筹集金额", example = "85000.00")
    private BigDecimal currentAmount;

    @Schema(description = "完成百分比", example = "28.33")
    private BigDecimal completionPercentage;

    @Schema(description = "项目封面图片", example = "https://example.com/image1.jpg")
    private String coverImage;

    @Schema(description = "项目状态", example = "active")
    private String status;

    @Schema(description = "状态描述", example = "进行中")
    private String statusDescription;

    @Schema(description = "捐赠人数", example = "156")
    private Long donorCount;

    @Schema(description = "发布管理员姓名", example = "管理员")
    private String adminName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCategoryDescription() {
        return categoryDescription;
    }

    public void setCategoryDescription(String categoryDescription) {
        this.categoryDescription = categoryDescription;
    }

    public BigDecimal getTargetAmount() {
        return targetAmount;
    }

    public void setTargetAmount(BigDecimal targetAmount) {
        this.targetAmount = targetAmount;
    }

    public BigDecimal getCurrentAmount() {
        return currentAmount;
    }

    public void setCurrentAmount(BigDecimal currentAmount) {
        this.currentAmount = currentAmount;
    }

    public BigDecimal getCompletionPercentage() {
        return completionPercentage;
    }

    public void setCompletionPercentage(BigDecimal completionPercentage) {
        this.completionPercentage = completionPercentage;
    }

    public String getCoverImage() {
        return coverImage;
    }

    public void setCoverImage(String coverImage) {
        this.coverImage = coverImage;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusDescription() {
        return statusDescription;
    }

    public void setStatusDescription(String statusDescription) {
        this.statusDescription = statusDescription;
    }

    public Long getDonorCount() {
        return donorCount;
    }

    public void setDonorCount(Long donorCount) {
        this.donorCount = donorCount;
    }

    public String getAdminName() {
        return adminName;
    }

    public void setAdminName(String adminName) {
        this.adminName = adminName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
