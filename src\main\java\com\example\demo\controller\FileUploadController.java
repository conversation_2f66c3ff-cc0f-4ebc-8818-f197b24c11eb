package com.example.demo.controller;

import com.example.demo.dto.ApiResponse;
import com.example.demo.dto.response.FileUploadResponseDTO;
import com.example.demo.service.FileUploadService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传Controller
 * 提供通用的文件上传功能
 */
@Slf4j
@RestController
@RequestMapping("/api/files")
@Tag(name = "文件上传", description = "文件上传相关API")
public class FileUploadController extends BaseController {

    @Autowired
    private FileUploadService fileUploadService;

    @PostMapping("/upload/avatar")
    @Operation(summary = "上传头像", description = "用户上传头像文件")
    public ApiResponse<FileUploadResponseDTO> uploadAvatar(
            @Parameter(description = "头像文件") @RequestParam("file") MultipartFile file) {
        try {
            Long userId = getCurrentUserId();
            log.info("用户{}上传头像文件: {}", userId, file.getOriginalFilename());

            FileUploadResponseDTO result = fileUploadService.uploadAvatar(file, userId);
            return success("头像上传成功", result);
        } catch (Exception e) {
            log.error("头像上传失败", e);
            return error("头像上传失败：" + e.getMessage());
        }
    }

    @PostMapping("/upload/project")
    @Operation(summary = "上传项目图片", description = "上传救助项目相关图片")
    public ApiResponse<FileUploadResponseDTO> uploadProjectImage(
            @Parameter(description = "项目图片文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "项目ID") @RequestParam(value = "projectId", required = false) Long projectId) {
        try {
            Long userId = getCurrentUserId();
            log.info("用户{}上传项目图片: projectId={}, fileName={}", userId, projectId, file.getOriginalFilename());

            FileUploadResponseDTO result = fileUploadService.uploadProjectImage(file, projectId);
            return success("项目图片上传成功", result);
        } catch (Exception e) {
            log.error("项目图片上传失败", e);
            return error("项目图片上传失败：" + e.getMessage());
        }
    }

    @PostMapping("/upload/document")
    @Operation(summary = "上传申请证明材料", description = "上传救助申请的证明文件")
    public ApiResponse<FileUploadResponseDTO> uploadRequestDocument(
            @Parameter(description = "证明文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "申请ID") @RequestParam(value = "requestId", required = false) Long requestId) {
        try {
            Long userId = getCurrentUserId();
            log.info("用户{}上传申请证明材料: requestId={}, fileName={}", userId, requestId, file.getOriginalFilename());

            FileUploadResponseDTO result = fileUploadService.uploadRequestDocument(file, requestId);
            return success("证明材料上传成功", result);
        } catch (Exception e) {
            log.error("证明材料上传失败", e);
            return error("证明材料上传失败：" + e.getMessage());
        }
    }

    @PostMapping("/upload")
    @Operation(summary = "通用文件上传", description = "通用的文件上传接口")
    public ApiResponse<FileUploadResponseDTO> uploadFile(
            @Parameter(description = "文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "文件分类") @RequestParam("category") String category,
            @Parameter(description = "关联ID") @RequestParam(value = "relatedId", required = false) Long relatedId) {
        try {
            Long userId = getCurrentUserId();
            log.info("用户{}通用文件上传: category={}, relatedId={}, fileName={}", userId, category, relatedId, file.getOriginalFilename());

            FileUploadResponseDTO result = fileUploadService.uploadFile(file, category, relatedId);
            return success("文件上传成功", result);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return error("文件上传失败：" + e.getMessage());
        }
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除文件", description = "删除已上传的文件")
    public ApiResponse<Void> deleteFile(
            @Parameter(description = "文件相对路径") @RequestParam("filePath") String filePath) {
        try {
            Long userId = getCurrentUserId();
            log.info("用户{}删除文件: {}", userId, filePath);

            boolean success = fileUploadService.deleteFile(filePath);
            if (success) {
                return success("文件删除成功", null);
            } else {
                return error("文件删除失败");
            }
        } catch (Exception e) {
            log.error("文件删除失败", e);
            return error("文件删除失败：" + e.getMessage());
        }
    }

    @GetMapping("/info")
    @Operation(summary = "获取文件上传配置", description = "获取文件上传的配置信息")
    public ApiResponse<Map<String, Object>> getUploadConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("imageTypes", new String[]{"image/jpeg", "image/jpg", "image/png", "image/gif"});
            config.put("documentTypes", new String[]{"application/pdf", "application/msword",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document"});
            config.put("maxImageSize", 5 * 1024 * 1024); // 5MB
            config.put("maxDocumentSize", 10 * 1024 * 1024); // 10MB
            config.put("categories", new String[]{"avatars", "projects", "documents"});

            return success("获取配置成功", config);
        } catch (Exception e) {
            log.error("获取上传配置失败", e);
            return error("获取配置失败：" + e.getMessage());
        }
    }
}
