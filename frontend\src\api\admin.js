import request from '@/utils/request'

// 获取管理员仪表板统计数据
export const getDashboardStats = () => {
  return request({
    url: '/admin/dashboard-stats',
    method: 'get'
  })
}

// 获取系统信息
export const getSystemInfo = () => {
  return request({
    url: '/admin/system-info',
    method: 'get'
  })
}

// 获取公开统计数据（首页使用）
export const getPublicStats = () => {
  return request({
    url: '/admin/public-stats',
    method: 'get'
  })
}

// 项目管理相关API
export const getProjectList = (params) => {
  return request({
    url: '/projects',
    method: 'get',
    params
  })
}

export const getProjectById = (id) => {
  return request({
    url: `/projects/${id}`,
    method: 'get'
  })
}

export const createProject = (data) => {
  return request({
    url: '/projects',
    method: 'post',
    data
  })
}

export const updateProject = (id, data) => {
  return request({
    url: `/projects/${id}`,
    method: 'put',
    data
  })
}

export const deleteProject = (id) => {
  return request({
    url: `/projects/${id}`,
    method: 'delete'
  })
}

export const updateProjectStatus = (id, status) => {
  return request({
    url: `/projects/${id}/status`,
    method: 'put',
    params: { status }  // 改为params，发送为URL参数
  })
}

export const updateProjectCurrentAmount = (id, amount) => {
  return request({
    url: `/projects/${id}/current-amount`,
    method: 'put',
    params: { amount }
  })
}

export const getProjectStats = () => {
  return request({
    url: '/projects/stats',
    method: 'get'
  })
}

// 用户管理相关API
export const getUserList = (params) => {
  return request({
    url: '/users',
    method: 'get',
    params
  })
}

export const getUserById = (id) => {
  return request({
    url: `/users/${id}`,
    method: 'get'
  })
}

export const updateUserStatus = (id, status) => {
  return request({
    url: `/users/${id}/status`,
    method: 'put',
    params: { status }
  })
}

export const resetUserPassword = (id) => {
  return request({
    url: `/users/${id}/reset-password`,
    method: 'put'
  })
}

// 获取用户捐赠记录
export const getUserDonations = (userId, params = {}) => {
  return request({
    url: '/donations',
    method: 'get',
    params: {
      userId,
      ...params
    }
  })
}

// 获取用户救助申请记录
export const getUserHelpRequests = (userId, params = {}) => {
  return request({
    url: '/help-requests',
    method: 'get',
    params: {
      userId,
      ...params
    }
  })
}

export const getUserStats = () => {
  return request({
    url: '/users/stats',
    method: 'get'
  })
}

// 救助申请管理相关API
export const getHelpRequestList = (params) => {
  return request({
    url: '/help-requests',
    method: 'get',
    params
  })
}

export const getHelpRequestById = (id) => {
  return request({
    url: `/help-requests/${id}`,
    method: 'get'
  })
}

export const getPendingRequests = (limit = 10) => {
  return request({
    url: '/help-requests/pending',
    method: 'get',
    params: { limit }
  })
}

export const reviewHelpRequest = (data) => {
  return request({
    url: `/help-requests/${data.requestId}/review`,
    method: 'post',
    data
  })
}

export const markAsUrgent = (id) => {
  return request({
    url: `/help-requests/${id}/mark-urgent`,
    method: 'post'
  })
}

// 捐赠管理相关API
export const getDonationList = (params) => {
  return request({
    url: '/donations',
    method: 'get',
    params
  })
}

export const getDonationById = (id) => {
  return request({
    url: `/donations/${id}`,
    method: 'get'
  })
}

export const updateDonationStatus = (id, status) => {
  return request({
    url: `/donations/${id}/status`,
    method: 'put',
    params: { status }
  })
}

export const getDonationStats = () => {
  return request({
    url: '/donations/stats',
    method: 'get'
  })
}

export const refundDonation = (id, reason) => {
  return request({
    url: `/donations/${id}/refund`,
    method: 'post',
    params: { reason }
  })
}
