package com.example.demo.security;

import com.example.demo.entity.User;
import com.example.demo.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 自定义用户详情服务
 * 从数据库加载用户信息
 */
@Slf4j
@Service
public class CustomUserDetailsService implements UserDetailsService {

    @Autowired
    @Lazy
    private UserService userService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        log.debug("加载用户信息: {}", username);
        
        User user = userService.findByUsername(username);
        if (user == null) {
            log.warn("用户不存在: {}", username);
            throw new UsernameNotFoundException("用户不存在: " + username);
        }
        
        if (user.getStatus() != User.Status.NORMAL.getCode()) {
            log.warn("用户已被禁用: {}", username);
            throw new UsernameNotFoundException("用户已被禁用: " + username);
        }
        
        return new CustomUserPrincipal(user);
    }

    /**
     * 根据用户ID加载用户信息
     */
    public UserDetails loadUserById(Long userId) {
        log.debug("根据ID加载用户信息: {}", userId);
        
        User user = userService.findById(userId);
        if (user == null) {
            log.warn("用户不存在: {}", userId);
            throw new UsernameNotFoundException("用户不存在: " + userId);
        }
        
        if (user.getStatus() != User.Status.NORMAL.getCode()) {
            log.warn("用户已被禁用: {}", userId);
            throw new UsernameNotFoundException("用户已被禁用: " + userId);
        }
        
        return new CustomUserPrincipal(user);
    }

    /**
     * 自定义用户主体类
     */
    public static class CustomUserPrincipal implements UserDetails {
        
        private final User user;

        public CustomUserPrincipal(User user) {
            this.user = user;
        }

        @Override
        public Collection<? extends GrantedAuthority> getAuthorities() {
            List<GrantedAuthority> authorities = new ArrayList<>();
            
            // 添加角色权限
            if (user.getRole() != null) {
                authorities.add(new SimpleGrantedAuthority("ROLE_" + user.getRole().toUpperCase()));
            }
            
            return authorities;
        }

        @Override
        public String getPassword() {
            return user.getPassword();
        }

        @Override
        public String getUsername() {
            return user.getUsername();
        }

        @Override
        public boolean isAccountNonExpired() {
            return true;
        }

        @Override
        public boolean isAccountNonLocked() {
            return user.getStatus() == User.Status.NORMAL.getCode();
        }

        @Override
        public boolean isCredentialsNonExpired() {
            return true;
        }

        @Override
        public boolean isEnabled() {
            return user.getStatus() == User.Status.NORMAL.getCode();
        }

        /**
         * 获取用户实体
         */
        public User getUser() {
            return user;
        }

        /**
         * 获取用户ID
         */
        public Long getUserId() {
            return user.getId();
        }

        /**
         * 获取用户角色
         */
        public String getRole() {
            return user.getRole();
        }

        /**
         * 检查是否为管理员
         */
        public boolean isAdmin() {
            return User.Role.ADMIN.getCode().equals(user.getRole());
        }

        /**
         * 检查是否为普通用户
         */
        public boolean isUser() {
            return User.Role.USER.getCode().equals(user.getRole());
        }
    }
}
