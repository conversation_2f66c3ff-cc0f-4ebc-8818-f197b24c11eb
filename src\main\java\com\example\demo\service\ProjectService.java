package com.example.demo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.entity.Project;

import java.math.BigDecimal;
import java.util.List;

/**
 * 救助项目服务接口
 * 定义救助项目相关的业务操作
 */
public interface ProjectService {

    /**
     * 创建救助项目
     * @param project 项目信息
     * @param adminId 管理员ID
     * @return 创建成功的项目信息
     */
    Project createProject(Project project, Long adminId);

    /**
     * 更新救助项目
     * @param project 项目信息
     * @param adminId 管理员ID
     * @return 更新后的项目信息
     */
    Project updateProject(Project project, Long adminId);

    /**
     * 根据ID查询项目
     * @param id 项目ID
     * @return 项目信息
     */
    Project findById(Long id);

    /**
     * 根据ID查询项目详情（包含统计信息）
     * @param id 项目ID
     * @return 项目详情信息
     */
    Object findProjectDetail(Long id);

    /**
     * 分页查询项目列表
     * @param page 分页参数
     * @param category 项目分类
     * @param status 项目状态
     * @param keyword 关键词搜索
     * @return 分页项目列表
     */
    IPage<Project> findProjectsWithConditions(Page<Project> page, String category, String status, String keyword);

    /**
     * 根据分类和状态查询项目
     * @param category 项目分类
     * @param status 项目状态
     * @return 项目列表
     */
    List<Project> findByCategoryAndStatus(String category, String status);

    /**
     * 根据管理员ID查询项目
     * @param adminId 管理员ID
     * @return 项目列表
     */
    List<Project> findByAdminId(Long adminId);

    /**
     * 查询热门项目
     * @param limit 限制数量
     * @return 热门项目列表
     */
    List<Project> findHotProjects(Integer limit);

    /**
     * 查询最新项目
     * @param limit 限制数量
     * @return 最新项目列表
     */
    List<Project> findLatestProjects(Integer limit);

    /**
     * 查询即将完成的项目
     * @param limit 限制数量
     * @return 即将完成的项目列表
     */
    List<Project> findNearCompletionProjects(Integer limit);

    /**
     * 更新项目状态
     * @param projectId 项目ID
     * @param status 新状态
     * @param adminId 管理员ID
     * @return 是否更新成功
     */
    boolean updateProjectStatus(Long projectId, String status, Long adminId);

    /**
     * 更新项目筹集金额
     * @param projectId 项目ID
     * @param amount 增加的金额
     * @return 是否更新成功
     */
    boolean updateCurrentAmount(Long projectId, BigDecimal amount);

    /**
     * 减少项目当前筹集金额
     * @param projectId 项目ID
     * @param amount 减少的金额
     * @return 是否更新成功
     */
    boolean reduceCurrentAmount(Long projectId, BigDecimal amount);

    /**
     * 设置项目当前筹集金额
     * @param projectId 项目ID
     * @param amount 新的金额
     * @return 是否更新成功
     */
    boolean setCurrentAmount(Long projectId, BigDecimal amount);

    /**
     * 删除项目
     * @param projectId 项目ID
     * @param adminId 管理员ID
     * @return 是否删除成功
     */
    boolean deleteProject(Long projectId, Long adminId);

    /**
     * 批量删除项目
     * @param projectIds 项目ID列表
     * @param adminId 管理员ID
     * @return 是否删除成功
     */
    boolean batchDeleteProjects(List<Long> projectIds, Long adminId);

    /**
     * 统计项目数量
     * @param category 项目分类
     * @param status 项目状态
     * @return 项目数量
     */
    Long countProjects(String category, String status);

    /**
     * 获取项目捐赠人数
     * @param projectId 项目ID
     * @return 捐赠人数
     */
    Long getProjectDonorCount(Long projectId);

    /**
     * 统计总筹集金额
     * @param status 项目状态
     * @return 总筹集金额
     */
    BigDecimal sumCurrentAmount(String status);

    /**
     * 检查项目是否可以接受捐赠
     * @param projectId 项目ID
     * @return 是否可以接受捐赠
     */
    boolean canAcceptDonation(Long projectId);

    /**
     * 检查项目是否已完成筹集目标
     * @param projectId 项目ID
     * @return 是否已完成
     */
    boolean isTargetReached(Long projectId);

    /**
     * 自动更新已完成的项目状态
     * @return 更新的项目数量
     */
    int autoUpdateCompletedProjects();

    /**
     * 获取项目统计信息（按分类）
     * @return 分类统计信息
     */
    List<Object> getProjectStatsByCategory();

    /**
     * 获取项目概览统计
     * @return 项目概览统计
     */
    Object getProjectOverviewStats();

    /**
     * 获取项目详细统计
     * @param projectId 项目ID
     * @return 项目详细统计
     */
    Object getProjectDetailStats(Long projectId);

    /**
     * 验证项目权限
     * @param projectId 项目ID
     * @param adminId 管理员ID
     * @return 是否有权限
     */
    boolean hasProjectPermission(Long projectId, Long adminId);

    /**
     * 获取推荐项目列表
     * @param limit 限制数量
     * @return 推荐项目列表
     */
    List<Project> getRecommendedProjects(Integer limit);

    /**
     * 搜索项目
     * @param keyword 搜索关键词
     * @param category 分类筛选
     * @param limit 限制数量
     * @return 搜索结果
     */
    List<Project> searchProjects(String keyword, String category, Integer limit);

    /**
     * 统计今日新增项目数
     * @return 今日新增项目数
     */
    Long countTodayNewProjects();
}
