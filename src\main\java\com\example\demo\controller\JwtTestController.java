package com.example.demo.controller;

import com.example.demo.dto.ApiResponse;
import com.example.demo.security.JwtTokenProvider;
import com.example.demo.util.JwtTestUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT测试Controller
 * 提供JWT认证机制的测试接口
 */
@Slf4j
@RestController
@RequestMapping("/api/test/jwt")
@Tag(name = "JWT测试", description = "JWT认证机制测试API")
public class JwtTestController extends BaseController {

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Autowired
    private JwtTestUtils jwtTestUtils;

    @GetMapping("/generate-token")
    @Operation(summary = "生成测试令牌", description = "生成用于测试的JWT令牌")
    public ApiResponse<Map<String, Object>> generateTestToken(
            @Parameter(description = "用户ID") @RequestParam(defaultValue = "1") Long userId,
            @Parameter(description = "用户名") @RequestParam(defaultValue = "testuser") String username,
            @Parameter(description = "用户角色") @RequestParam(defaultValue = "user") String role) {
        
        try {
            logOperation("生成测试令牌", userId, username, role);
            
            String accessToken = jwtTokenProvider.generateAccessToken(userId, username, role);
            String refreshToken = jwtTokenProvider.generateRefreshToken(userId, username);
            
            Map<String, Object> result = new HashMap<>();
            result.put("accessToken", accessToken);
            result.put("refreshToken", refreshToken);
            result.put("authorizationHeader", "Bearer " + accessToken);
            result.put("expiresIn", 7200); // 2小时
            result.put("tokenType", "Bearer");
            
            return success("令牌生成成功", result);
        } catch (Exception e) {
            log.error("生成测试令牌失败", e);
            return error("生成令牌失败：" + e.getMessage());
        }
    }

    @PostMapping("/validate-token")
    @Operation(summary = "验证令牌", description = "验证JWT令牌的有效性")
    public ApiResponse<Map<String, Object>> validateToken(
            @Parameter(description = "JWT令牌") @RequestParam String token) {
        
        try {
            logOperation("验证令牌");
            
            Map<String, Object> result = new HashMap<>();
            
            boolean isValid = jwtTokenProvider.validateToken(token);
            result.put("valid", isValid);
            
            if (isValid) {
                result.put("userId", jwtTokenProvider.getUserIdFromToken(token));
                result.put("username", jwtTokenProvider.getUsernameFromToken(token));
                result.put("tokenType", jwtTokenProvider.getTokenTypeFromToken(token));
                result.put("expirationDate", jwtTokenProvider.getExpirationDateFromToken(token));
                result.put("remainingTime", jwtTokenProvider.getRemainingValidTime(token));
                result.put("isExpired", jwtTokenProvider.isTokenExpired(token));
                result.put("isAccessToken", jwtTokenProvider.isAccessToken(token));
                result.put("isRefreshToken", jwtTokenProvider.isRefreshToken(token));
                
                if (jwtTokenProvider.isAccessToken(token)) {
                    result.put("role", jwtTokenProvider.getRoleFromToken(token));
                }
            }
            
            return success("令牌验证完成", result);
        } catch (Exception e) {
            log.error("验证令牌失败", e);
            return error("令牌验证失败：" + e.getMessage());
        }
    }

    @PostMapping("/refresh-token")
    @Operation(summary = "刷新令牌", description = "使用刷新令牌生成新的访问令牌")
    public ApiResponse<Map<String, Object>> refreshToken(
            @Parameter(description = "刷新令牌") @RequestParam String refreshToken) {
        
        try {
            logOperation("刷新令牌");
            
            String newAccessToken = jwtTokenProvider.refreshAccessToken(refreshToken);
            
            Map<String, Object> result = new HashMap<>();
            result.put("newAccessToken", newAccessToken);
            result.put("refreshToken", refreshToken);
            result.put("authorizationHeader", "Bearer " + newAccessToken);
            result.put("expiresIn", 7200); // 2小时
            
            return success("令牌刷新成功", result);
        } catch (Exception e) {
            log.error("刷新令牌失败", e);
            return error("令牌刷新失败：" + e.getMessage());
        }
    }

    @PostMapping("/blacklist-token")
    @Operation(summary = "将令牌加入黑名单", description = "将指定令牌加入黑名单")
    public ApiResponse<Void> blacklistToken(
            @Parameter(description = "JWT令牌") @RequestParam String token) {
        
        try {
            logOperation("将令牌加入黑名单");
            
            jwtTokenProvider.blacklistToken(token);
            
            return success("令牌已加入黑名单", null);
        } catch (Exception e) {
            log.error("将令牌加入黑名单失败", e);
            return error("操作失败：" + e.getMessage());
        }
    }

    @GetMapping("/current-user")
    @Operation(summary = "获取当前用户信息", description = "从JWT令牌中获取当前用户信息")
    public ApiResponse<Map<String, Object>> getCurrentUser(HttpServletRequest request) {
        try {
            logOperation("获取当前用户信息");
            
            String authHeader = request.getHeader("Authorization");
            JwtTestUtils.UserInfo userInfo = jwtTestUtils.extractUserInfoFromHeader(authHeader);
            
            if (userInfo != null) {
                Map<String, Object> result = new HashMap<>();
                result.put("userId", userInfo.getUserId());
                result.put("username", userInfo.getUsername());
                result.put("role", userInfo.getRole());
                result.put("fromBaseController", Map.of(
                    "userId", getCurrentUserId(),
                    "username", getCurrentUsername(),
                    "role", getCurrentUserRole()
                ));
                
                return success("获取用户信息成功", result);
            } else {
                return unauthorized("未登录或令牌无效");
            }
        } catch (Exception e) {
            log.error("获取当前用户信息失败", e);
            return error("获取用户信息失败：" + e.getMessage());
        }
    }

    @GetMapping("/test-auth")
    @Operation(summary = "测试认证", description = "测试JWT认证是否正常工作")
    public ApiResponse<Map<String, Object>> testAuth() {
        try {
            logOperation("测试认证");
            
            Long userId = getCurrentUserId();
            String username = getCurrentUsername();
            String role = getCurrentUserRole();
            
            Map<String, Object> result = new HashMap<>();
            result.put("authenticated", userId != null);
            result.put("userId", userId);
            result.put("username", username);
            result.put("role", role);
            result.put("isAdmin", isAdmin());
            result.put("message", userId != null ? "认证成功" : "未认证");
            
            return success("认证测试完成", result);
        } catch (Exception e) {
            log.error("测试认证失败", e);
            return error("认证测试失败：" + e.getMessage());
        }
    }

    @GetMapping("/admin-only")
    @Operation(summary = "管理员专用接口", description = "只有管理员可以访问的测试接口")
    public ApiResponse<String> adminOnlyEndpoint() {
        try {
            if (!isAdmin()) {
                return forbidden("只有管理员可以访问此接口");
            }
            
            return success("管理员接口访问成功");
        } catch (Exception e) {
            log.error("管理员接口访问失败", e);
            return error("访问失败：" + e.getMessage());
        }
    }

    @PostMapping("/run-test-flow")
    @Operation(summary = "运行完整测试流程", description = "运行JWT认证的完整测试流程")
    public ApiResponse<String> runTestFlow() {
        try {
            logOperation("运行JWT测试流程");
            
            // 运行测试流程
            jwtTestUtils.testTokenFlow();
            
            return success("JWT测试流程执行完成，请查看日志");
        } catch (Exception e) {
            log.error("运行JWT测试流程失败", e);
            return error("测试流程失败：" + e.getMessage());
        }
    }
}
