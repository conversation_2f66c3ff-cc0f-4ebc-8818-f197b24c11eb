package com.example.demo.util;

import com.example.demo.security.JwtTokenProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * JWT测试工具类
 * 提供JWT令牌的测试和调试功能
 */
@Slf4j
@Component
public class JwtTestUtils {

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    /**
     * 生成测试用的访问令牌
     */
    public String generateTestAccessToken(Long userId, String username, String role) {
        return jwtTokenProvider.generateAccessToken(userId, username, role);
    }

    /**
     * 生成测试用的刷新令牌
     */
    public String generateTestRefreshToken(Long userId, String username) {
        return jwtTokenProvider.generateRefreshToken(userId, username);
    }

    /**
     * 解析并打印令牌信息
     */
    public void printTokenInfo(String token) {
        try {
            log.info("=== JWT令牌信息 ===");
            log.info("令牌: {}", token.substring(0, Math.min(token.length(), 50)) + "...");
            
            if (jwtTokenProvider.validateToken(token)) {
                log.info("令牌状态: 有效");
                log.info("用户ID: {}", jwtTokenProvider.getUserIdFromToken(token));
                log.info("用户名: {}", jwtTokenProvider.getUsernameFromToken(token));
                
                if (jwtTokenProvider.isAccessToken(token)) {
                    log.info("令牌类型: 访问令牌");
                    log.info("用户角色: {}", jwtTokenProvider.getRoleFromToken(token));
                } else if (jwtTokenProvider.isRefreshToken(token)) {
                    log.info("令牌类型: 刷新令牌");
                }
                
                Date expiration = jwtTokenProvider.getExpirationDateFromToken(token);
                log.info("过期时间: {}", expiration);
                
                long remainingTime = jwtTokenProvider.getRemainingValidTime(token);
                log.info("剩余有效时间: {} 毫秒 ({} 分钟)", remainingTime, remainingTime / 60000);
                
                log.info("是否过期: {}", jwtTokenProvider.isTokenExpired(token));
            } else {
                log.info("令牌状态: 无效");
            }
            log.info("==================");
        } catch (Exception e) {
            log.error("解析令牌失败: {}", e.getMessage());
        }
    }

    /**
     * 测试令牌生成和验证流程
     */
    public void testTokenFlow() {
        log.info("开始测试JWT令牌流程...");
        
        // 生成测试令牌
        Long testUserId = 1L;
        String testUsername = "testuser";
        String testRole = "user";
        
        String accessToken = generateTestAccessToken(testUserId, testUsername, testRole);
        String refreshToken = generateTestRefreshToken(testUserId, testUsername);
        
        log.info("生成访问令牌: {}", accessToken.substring(0, 50) + "...");
        log.info("生成刷新令牌: {}", refreshToken.substring(0, 50) + "...");
        
        // 验证访问令牌
        printTokenInfo(accessToken);
        
        // 验证刷新令牌
        printTokenInfo(refreshToken);
        
        // 测试令牌刷新
        try {
            String newAccessToken = jwtTokenProvider.refreshAccessToken(refreshToken);
            log.info("刷新后的访问令牌: {}", newAccessToken.substring(0, 50) + "...");
            printTokenInfo(newAccessToken);
        } catch (Exception e) {
            log.error("令牌刷新失败: {}", e.getMessage());
        }
        
        // 测试令牌黑名单
        log.info("将访问令牌加入黑名单...");
        jwtTokenProvider.blacklistToken(accessToken);
        
        log.info("验证黑名单中的令牌:");
        printTokenInfo(accessToken);
        
        log.info("JWT令牌流程测试完成");
    }

    /**
     * 生成用于测试的Authorization头
     */
    public String generateAuthorizationHeader(Long userId, String username, String role) {
        String token = generateTestAccessToken(userId, username, role);
        return "Bearer " + token;
    }

    /**
     * 验证Authorization头中的令牌
     */
    public boolean validateAuthorizationHeader(String authHeader) {
        try {
            String token = jwtTokenProvider.extractTokenFromHeader(authHeader);
            return token != null && jwtTokenProvider.validateToken(token);
        } catch (Exception e) {
            log.error("验证Authorization头失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 从Authorization头中提取用户信息
     */
    public UserInfo extractUserInfoFromHeader(String authHeader) {
        try {
            String token = jwtTokenProvider.extractTokenFromHeader(authHeader);
            if (token != null && jwtTokenProvider.validateToken(token)) {
                return new UserInfo(
                    jwtTokenProvider.getUserIdFromToken(token),
                    jwtTokenProvider.getUsernameFromToken(token),
                    jwtTokenProvider.getRoleFromToken(token)
                );
            }
        } catch (Exception e) {
            log.error("提取用户信息失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 用户信息类
     */
    public static class UserInfo {
        private final Long userId;
        private final String username;
        private final String role;

        public UserInfo(Long userId, String username, String role) {
            this.userId = userId;
            this.username = username;
            this.role = role;
        }

        public Long getUserId() {
            return userId;
        }

        public String getUsername() {
            return username;
        }

        public String getRole() {
            return role;
        }

        @Override
        public String toString() {
            return String.format("UserInfo{userId=%d, username='%s', role='%s'}", 
                    userId, username, role);
        }
    }
}
