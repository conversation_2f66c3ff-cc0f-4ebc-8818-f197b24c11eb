import request from './index'

// 创建救助申请
export const createHelpRequest = (data) => {
  return request.post('/help-requests', data)
}

// 获取我的救助申请列表（个人中心专用）
export const getMyHelpRequests = (params) => {
  return request.get('/help-requests/my-requests', { params })
}

// 获取救助申请详情
export const getHelpRequestById = (id) => {
  return request.get(`/help-requests/${id}`)
}

// 更新救助申请
export const updateHelpRequest = (id, data) => {
  return request.put(`/help-requests/${id}`, data)
}

// 删除救助申请
export const deleteHelpRequest = (id) => {
  return request.delete(`/help-requests/${id}`)
}

// 管理员获取所有救助申请（后台管理专用）
export const getAllHelpRequests = (params) => {
  return request.get('/help-requests', { params })
}

// 管理员获取救助申请列表（后台管理专用）
export const getHelpRequestList = (params) => {
  return request.get('/help-requests', { params })
}

// 管理员审核救助申请
export const reviewHelpRequest = (id, data) => {
  return request.put(`/help-requests/${id}/review`, data)
}

// 获取救助申请统计
export const getHelpRequestStats = () => {
  return request.get('/help-requests/stats')
}
