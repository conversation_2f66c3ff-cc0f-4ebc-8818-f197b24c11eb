package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * AI对话记录实体类
 * 对应数据库表: conversations
 */
@Data
@TableName("conversations")
@Schema(description = "AI对话记录实体")
public class Conversation {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "对话ID", example = "1")
    private Long id;

    @TableField("user_id")
    @Schema(description = "用户ID(可为空，支持游客对话)", example = "2")
    private Long userId;

    @TableField("session_id")
    @Schema(description = "会话ID", example = "session_001")
    private String sessionId;

    @TableField("message")
    @Schema(description = "用户消息", example = "你好，我想了解如何申请救助")
    private String message;

    @TableField("response")
    @Schema(description = "AI回复", example = "您好！很高兴为您服务。申请救助需要以下步骤...")
    private String response;

    @TableField("conversation_type")
    @Schema(description = "对话类型: general-通用, donation-捐赠咨询, help_request-求助咨询, project_info-项目信息", example = "help_request")
    private String conversationType;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 对话类型枚举
     */
    public enum ConversationType {
        GENERAL("general", "通用对话"),
        DONATION("donation", "捐赠咨询"),
        HELP_REQUEST("help_request", "求助咨询"),
        PROJECT_INFO("project_info", "项目信息");

        private final String code;
        private final String description;

        ConversationType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
