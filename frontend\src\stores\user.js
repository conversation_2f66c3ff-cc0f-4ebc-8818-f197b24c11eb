import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login, getUserInfo } from '@/api/user'

export const useUserStore = defineStore('user', () => {
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref({})

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const user = computed(() => userInfo.value)
  const isAdmin = computed(() => userInfo.value?.role === 'admin')

  // 登录
  const loginAction = async (loginForm) => {
    try {
      const data = await login(loginForm)
      token.value = data.accessToken
      userInfo.value = data.userInfo
      localStorage.setItem('token', data.accessToken)
      localStorage.setItem('refreshToken', data.refreshToken)
      return data
    } catch (error) {
      throw error
    }
  }

  // 获取用户信息
  const getUserInfoAction = async () => {
    try {
      const data = await getUserInfo()
      userInfo.value = data
      return data
    } catch (error) {
      throw error
    }
  }

  // 登出
  const logout = () => {
    token.value = ''
    userInfo.value = {}
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
  }

  return {
    token,
    userInfo,
    user,
    isLoggedIn,
    isAdmin,
    loginAction,
    getUserInfoAction,
    logout
  }
})
