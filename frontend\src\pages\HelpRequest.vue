<template>
  <div class="help-request-page">
    <div class="container">
      <div class="request-container">
        <div class="request-header">
          <h1>申请救助</h1>
          <p>我们将认真审核每一份申请，为真正需要帮助的人提供支持</p>
        </div>

        <el-form ref="requestFormRef" :model="requestForm" :rules="requestRules" label-width="120px">
          <!-- 基本信息 -->
          <div class="form-section">
            <h3>基本信息</h3>
            
            <el-form-item label="申请人姓名" prop="applicantName">
              <el-input v-model="requestForm.applicantName" placeholder="请输入真实姓名" />
            </el-form-item>

            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="requestForm.phone" placeholder="请输入联系电话" />
            </el-form-item>

            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="requestForm.idCard" placeholder="请输入身份证号码" />
            </el-form-item>

            <el-form-item label="所在地区" prop="address">
              <el-input v-model="requestForm.address" placeholder="请输入详细地址" />
            </el-form-item>
          </div>

          <!-- 救助信息 -->
          <div class="form-section">
            <h3>救助信息</h3>
            
            <el-form-item label="救助类型" prop="category">
              <el-select v-model="requestForm.category" placeholder="请选择救助类型" style="width: 100%">
                <el-option label="医疗救助" value="medical" />
                <el-option label="教育助学" value="education" />
                <el-option label="扶贫济困" value="poverty" />
                <el-option label="灾难救援" value="disaster" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>

            <el-form-item label="申请金额" prop="requestAmount">
              <el-input v-model.number="requestForm.requestAmount" type="number" placeholder="请输入申请金额">
                <template #prepend>¥</template>
              </el-input>
            </el-form-item>

            <el-form-item label="紧急程度" prop="urgency">
              <el-radio-group v-model="requestForm.urgency">
                <el-radio label="low">一般</el-radio>
                <el-radio label="medium">紧急</el-radio>
                <el-radio label="high">非常紧急</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="详细说明" prop="description">
              <el-input
                v-model="requestForm.description"
                type="textarea"
                :rows="6"
                placeholder="请详细描述您的困难情况和需要帮助的原因"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>
          </div>

          <!-- 证明材料 -->
          <div class="form-section">
            <h3>证明材料</h3>
            
            <el-form-item label="上传文件">
              <el-upload
                class="upload-demo"
                drag
                action="#"
                multiple
                :auto-upload="false"
                :file-list="fileList"
                :on-change="handleFileChange"
              >
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    请上传相关证明材料（医疗证明、贫困证明等），支持jpg/png/pdf文件，单个文件不超过10MB
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </div>

          <!-- 承诺声明 -->
          <div class="form-section">
            <el-form-item prop="agreement">
              <el-checkbox v-model="requestForm.agreement">
                我承诺所提供的信息真实有效，如有虚假将承担相应法律责任
              </el-checkbox>
            </el-form-item>
          </div>

          <el-form-item>
            <el-button type="primary" size="large" @click="handleSubmit" :loading="loading">
              提交申请
            </el-button>
            <el-button size="large" @click="resetForm">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()
const requestFormRef = ref()
const loading = ref(false)
const fileList = ref([])

const requestForm = reactive({
  applicantName: '',
  phone: '',
  idCard: '',
  address: '',
  category: '',
  requestAmount: '',
  urgency: 'medium',
  description: '',
  agreement: false
})

const requestRules = {
  applicantName: [
    { required: true, message: '请输入申请人姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  idCard: [
    { required: true, message: '请输入身份证号码', trigger: 'blur' },
    { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号码', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入详细地址', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择救助类型', trigger: 'change' }
  ],
  requestAmount: [
    { required: true, message: '请输入申请金额', trigger: 'blur' },
    { type: 'number', min: 1, message: '申请金额必须大于0', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请详细描述困难情况', trigger: 'blur' },
    { min: 50, message: '描述内容不能少于50字', trigger: 'blur' }
  ],
  agreement: [
    { validator: (rule, value, callback) => {
      if (!value) {
        callback(new Error('请同意承诺声明'))
      } else {
        callback()
      }
    }, trigger: 'change' }
  ]
}

const handleFileChange = (file, fileList) => {
  // 处理文件上传
  console.log('文件变化:', file, fileList)
}

const handleSubmit = async () => {
  try {
    await requestFormRef.value.validate()
    loading.value = true
    
    // 这里应该调用提交申请的API
    // const result = await submitHelpRequest(requestForm)
    
    // 模拟提交过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('申请提交成功，我们将在3个工作日内审核')
    router.push('/profile')
  } catch (error) {
    console.error('提交申请失败:', error)
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  requestFormRef.value.resetFields()
  fileList.value = []
}
</script>

<style scoped>
.help-request-page {
  min-height: calc(100vh - 120px);
  padding: 40px 0;
  background: #f5f5f5;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.request-container {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.request-header {
  text-align: center;
  margin-bottom: 40px;
}

.request-header h1 {
  font-size: 28px;
  color: #2c3e50;
  margin-bottom: 12px;
}

.request-header p {
  color: #7f8c8d;
  font-size: 16px;
}

.form-section {
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 1px solid #eee;
}

.form-section:last-child {
  border-bottom: none;
}

.form-section h3 {
  color: #2c3e50;
  margin-bottom: 24px;
  font-size: 18px;
}

.upload-demo {
  width: 100%;
}
</style>
