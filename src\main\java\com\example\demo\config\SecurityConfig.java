package com.example.demo.config;

import com.example.demo.security.CustomUserDetailsService;
import com.example.demo.security.JwtAuthenticationFilter;
import com.example.demo.security.JwtAuthenticationEntryPoint;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

/**
 * Spring Security配置类
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    @Autowired
    @Lazy
    private CustomUserDetailsService userDetailsService;

    @Autowired
    @Lazy
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    @Autowired
    private JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;

    /**
     * 密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * 认证管理器
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    /**
     * 安全过滤器链
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF（使用JWT时通常禁用）
            .csrf(csrf -> csrf.disable())
            
            // 配置CORS
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            
            // 配置会话管理
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            
            // 配置异常处理
            .exceptionHandling(exception -> exception.authenticationEntryPoint(jwtAuthenticationEntryPoint))
            
            // 配置请求授权
            .authorizeHttpRequests(auth -> auth
                // 用户注册和登录 - 必须放在最前面
                .requestMatchers("/api/users/register").permitAll()
                .requestMatchers("/api/users/login").permitAll()
                .requestMatchers("/api/auth/**").permitAll()

                // AI对话接口
                .requestMatchers("/api/conversations/chat").permitAll()
                .requestMatchers("/api/conversations/welcome").permitAll()
                .requestMatchers("/api/conversations/popular-questions").permitAll()

                // 项目查看接口 - 只允许GET请求
                .requestMatchers(HttpMethod.GET, "/api/projects").permitAll()
                .requestMatchers(HttpMethod.GET, "/api/projects/*").permitAll()
                .requestMatchers("/api/projects/hot").permitAll()
                .requestMatchers("/api/projects/recommended").permitAll()
                .requestMatchers("/api/projects/categories").permitAll()
                .requestMatchers("/api/projects/stats").permitAll()

                // 捐赠查看接口
                .requestMatchers("/api/donations/recent").permitAll()
                .requestMatchers("/api/donations/large").permitAll()
                .requestMatchers("/api/donations/stats").permitAll()

                // 救助申请查看接口
                .requestMatchers("/api/help-requests/recent-approved").permitAll()
                .requestMatchers("/api/help-requests/stats").permitAll()

                // 测试接口
                .requestMatchers("/api/test/**").permitAll()

                // Swagger文档
                .requestMatchers(
                    "/swagger-ui/**",
                    "/swagger-ui.html",
                    "/api/swagger-ui/**",
                    "/api/swagger-ui.html",
                    "/v3/api-docs/**",
                    "/swagger-resources/**",
                    "/webjars/**"
                ).permitAll()

                // 静态文件访问
                .requestMatchers("/uploads/**").permitAll()

                // 静态资源
                .requestMatchers("/favicon.ico", "/error").permitAll()

                // 健康检查
                .requestMatchers("/actuator/**").permitAll()

                // 管理员接口 - 用户管理
                .requestMatchers(
                    "/api/users",
                    "/api/users/*/status"
                ).hasRole("ADMIN")

                // 管理员接口 - 项目管理
                .requestMatchers(HttpMethod.PUT, "/api/projects/*").hasRole("ADMIN")
                .requestMatchers(HttpMethod.DELETE, "/api/projects/*").hasRole("ADMIN")
                .requestMatchers(HttpMethod.POST, "/api/projects").hasRole("ADMIN")
                .requestMatchers(
                    "/api/projects/*/status"
                ).hasRole("ADMIN")

                // 管理员接口 - 捐赠管理
                .requestMatchers(
                    "/api/donations/*/refund"
                ).hasRole("ADMIN")

                // 管理员接口 - 对话管理
                .requestMatchers(
                    "/api/conversations",
                    "/api/conversations/stats",
                    "/api/conversations/*/resolve"
                ).hasRole("ADMIN")

                // 管理员接口 - 申请管理
                .requestMatchers(
                    "/api/help-requests/pending",
                    "/api/help-requests/*/review",
                    "/api/help-requests/*/report",
                    "/api/help-requests/*/mark-urgent",
                    "/api/help-requests/*/similar"
                ).hasRole("ADMIN")

                // 其他所有请求需要认证
                .anyRequest().authenticated()
            );

        // 添加JWT过滤器
        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    /**
     * CORS配置
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        
        // 允许的源
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        
        // 允许的方法
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        
        // 允许的头
        configuration.setAllowedHeaders(Arrays.asList("*"));
        
        // 允许凭证
        configuration.setAllowCredentials(true);
        
        // 预检请求的缓存时间
        configuration.setMaxAge(3600L);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        
        return source;
    }
}
