package com.example.demo.exception;

/**
 * 项目不存在异常
 */
public class ProjectNotFoundException extends BusinessException {

    public ProjectNotFoundException() {
        super(ErrorCode.PROJECT_NOT_FOUND);
    }

    public ProjectNotFoundException(String message) {
        super(ErrorCode.PROJECT_NOT_FOUND, message);
    }

    public ProjectNotFoundException(Long projectId) {
        super(ErrorCode.PROJECT_NOT_FOUND, "项目不存在，ID: " + projectId);
    }

    public ProjectNotFoundException(Throwable cause) {
        super(ErrorCode.PROJECT_NOT_FOUND, cause);
    }
}
