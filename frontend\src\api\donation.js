import request from './index'

// 获取我的捐赠记录（个人中心专用）
export const getMyDonations = (params) => {
  return request.get('/donations/my-donations', { params })
}

// 获取捐赠详情
export const getDonationById = (id) => {
  return request.get(`/donations/${id}`)
}

// 创建捐赠
export const createDonation = (data) => {
  return request.post('/donations', data)
}

// 管理员获取捐赠记录列表（后台管理专用）
export const getDonationList = (params) => {
  return request.get('/donations', { params })
}

// 获取项目的捐赠记录（项目详情页专用）
export const getProjectDonations = (projectId, params = {}) => {
  return request.get('/donations', {
    params: {
      projectId: projectId,
      status: 'success', // 只显示成功的捐赠
      ...params
    }
  })
}

// 获取捐赠统计
export const getDonationStats = () => {
  return request.get('/donations/stats')
}

// 获取用户捐赠统计
export const getUserDonationStats = (userId) => {
  return request.get(`/donations/user/${userId}/stats`)
}
