package com.example.demo.exception;

import com.example.demo.dto.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.sql.SQLException;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 统一处理所有Controller层抛出的异常
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ApiResponse<Object>> handleBusinessException(
            BusinessException e, HttpServletRequest request) {
        
        String traceId = ExceptionUtils.generateTraceId();
        
        // 记录日志
        if (ExceptionUtils.shouldLogDetail(e)) {
            log.error("业务异常 [{}] - URI: {}, TraceId: {}", 
                    e.getErrorCode(), request.getRequestURI(), traceId, e);
        } else {
            log.warn("业务异常 [{}] - URI: {}, TraceId: {}, Message: {}", 
                    e.getErrorCode(), request.getRequestURI(), traceId, e.getMessage());
        }
        
        // 构建响应
        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage());
        response.setTraceId(traceId);
        
        // 开发环境返回详细错误信息
        if ("dev".equals(activeProfile)) {
            response.setData(ExceptionUtils.buildErrorDetail(e, traceId));
        }
        
        // 发送告警
        if (ExceptionUtils.shouldAlert(e)) {
            sendAlert(e, request, traceId);
        }
        
        return ResponseEntity.status(getHttpStatus(e.getErrorCode())).body(response);
    }

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Object>> handleValidationException(
            MethodArgumentNotValidException e, HttpServletRequest request) {
        
        String traceId = ExceptionUtils.generateTraceId();
        
        // 提取验证错误信息
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining("; "));
        
        log.warn("参数验证异常 - URI: {}, TraceId: {}, Errors: {}", 
                request.getRequestURI(), traceId, errorMessage);
        
        ApiResponse<Object> response = ApiResponse.error(ErrorCode.PARAM_ERROR.getCode(), 
                "参数验证失败：" + errorMessage);
        response.setTraceId(traceId);
        
        if ("dev".equals(activeProfile)) {
            response.setData(e.getBindingResult().getFieldErrors());
        }
        
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理约束验证异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ApiResponse<Object>> handleConstraintViolationException(
            ConstraintViolationException e, HttpServletRequest request) {
        
        String traceId = ExceptionUtils.generateTraceId();
        
        String errorMessage = e.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining("; "));
        
        log.warn("约束验证异常 - URI: {}, TraceId: {}, Errors: {}", 
                request.getRequestURI(), traceId, errorMessage);
        
        ApiResponse<Object> response = ApiResponse.error(ErrorCode.PARAM_ERROR.getCode(), 
                "参数验证失败：" + errorMessage);
        response.setTraceId(traceId);
        
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<ApiResponse<Object>> handleBindException(
            BindException e, HttpServletRequest request) {
        
        String traceId = ExceptionUtils.generateTraceId();
        
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining("; "));
        
        log.warn("参数绑定异常 - URI: {}, TraceId: {}, Errors: {}", 
                request.getRequestURI(), traceId, errorMessage);
        
        ApiResponse<Object> response = ApiResponse.error(ErrorCode.PARAM_ERROR.getCode(), 
                "参数绑定失败：" + errorMessage);
        response.setTraceId(traceId);
        
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理缺少请求参数异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<ApiResponse<Object>> handleMissingParameterException(
            MissingServletRequestParameterException e, HttpServletRequest request) {
        
        String traceId = ExceptionUtils.generateTraceId();
        
        log.warn("缺少请求参数异常 - URI: {}, TraceId: {}, Parameter: {}", 
                request.getRequestURI(), traceId, e.getParameterName());
        
        ApiResponse<Object> response = ApiResponse.error(ErrorCode.PARAM_MISSING.getCode(), 
                "缺少必要参数：" + e.getParameterName());
        response.setTraceId(traceId);
        
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ApiResponse<Object>> handleTypeMismatchException(
            MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        
        String traceId = ExceptionUtils.generateTraceId();
        
        log.warn("参数类型不匹配异常 - URI: {}, TraceId: {}, Parameter: {}, Value: {}", 
                request.getRequestURI(), traceId, e.getName(), e.getValue());
        
        ApiResponse<Object> response = ApiResponse.error(ErrorCode.PARAM_TYPE_ERROR.getCode(), 
                String.format("参数 %s 类型错误，值：%s", e.getName(), e.getValue()));
        response.setTraceId(traceId);
        
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理JSON解析异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<ApiResponse<Object>> handleJsonParseException(
            HttpMessageNotReadableException e, HttpServletRequest request) {
        
        String traceId = ExceptionUtils.generateTraceId();
        
        log.warn("JSON解析异常 - URI: {}, TraceId: {}, Message: {}", 
                request.getRequestURI(), traceId, e.getMessage());
        
        ApiResponse<Object> response = ApiResponse.error(ErrorCode.JSON_PARSE_ERROR.getCode(), 
                "请求数据格式错误");
        response.setTraceId(traceId);
        
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理请求方法不支持异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<ApiResponse<Object>> handleMethodNotSupportedException(
            HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
        
        String traceId = ExceptionUtils.generateTraceId();
        
        log.warn("请求方法不支持异常 - URI: {}, TraceId: {}, Method: {}", 
                request.getRequestURI(), traceId, e.getMethod());
        
        ApiResponse<Object> response = ApiResponse.error(ErrorCode.REQUEST_METHOD_ERROR.getCode(), 
                "请求方法不支持：" + e.getMethod());
        response.setTraceId(traceId);
        
        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).body(response);
    }

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<ApiResponse<Object>> handleMaxUploadSizeExceededException(
            MaxUploadSizeExceededException e, HttpServletRequest request) {
        
        String traceId = ExceptionUtils.generateTraceId();
        
        log.warn("文件上传大小超限异常 - URI: {}, TraceId: {}, MaxSize: {}", 
                request.getRequestURI(), traceId, e.getMaxUploadSize());
        
        ApiResponse<Object> response = ApiResponse.error(ErrorCode.FILE_UPLOAD_ERROR.getCode(), 
                "文件大小超出限制");
        response.setTraceId(traceId);
        
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理JWT认证异常
     */
    @ExceptionHandler(JwtAuthenticationException.class)
    public ResponseEntity<ApiResponse<Object>> handleJwtAuthenticationException(
            JwtAuthenticationException e, HttpServletRequest request) {

        String traceId = ExceptionUtils.generateTraceId();

        log.warn("JWT认证异常 - URI: {}, TraceId: {}, Message: {}",
                request.getRequestURI(), traceId, e.getMessage());

        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage());
        response.setTraceId(traceId);

        HttpStatus status = HttpStatus.UNAUTHORIZED;
        if (e.getErrorCode() == ErrorCode.ACCESS_DENIED) {
            status = HttpStatus.FORBIDDEN;
        }

        return ResponseEntity.status(status).body(response);
    }

    /**
     * 处理权限异常
     */
    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ApiResponse<Object>> handleAccessDeniedException(
            AccessDeniedException e, HttpServletRequest request) {

        String traceId = ExceptionUtils.generateTraceId();

        log.warn("权限异常 - URI: {}, TraceId: {}, Message: {}",
                request.getRequestURI(), traceId, e.getMessage());

        ApiResponse<Object> response = ApiResponse.error(ErrorCode.ACCESS_DENIED.getCode(),
                "权限不足");
        response.setTraceId(traceId);

        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
    }

    /**
     * 处理数据库异常
     */
    @ExceptionHandler({DataAccessException.class, SQLException.class})
    public ResponseEntity<ApiResponse<Object>> handleDatabaseException(
            Exception e, HttpServletRequest request) {
        
        String traceId = ExceptionUtils.generateTraceId();
        
        log.error("数据库异常 - URI: {}, TraceId: {}", 
                request.getRequestURI(), traceId, e);
        
        ApiResponse<Object> response = ApiResponse.error(ErrorCode.DATABASE_ERROR.getCode(), 
                "数据库操作异常");
        response.setTraceId(traceId);
        
        if ("dev".equals(activeProfile)) {
            response.setData(ExceptionUtils.buildErrorDetail(e, traceId));
        }
        
        // 数据库异常需要告警
        sendAlert(e, request, traceId);
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ApiResponse<Object>> handleRuntimeException(
            RuntimeException e, HttpServletRequest request) {
        
        String traceId = ExceptionUtils.generateTraceId();
        
        log.error("运行时异常 - URI: {}, TraceId: {}", 
                request.getRequestURI(), traceId, e);
        
        ApiResponse<Object> response = ApiResponse.error(ErrorCode.SYSTEM_ERROR.getCode(), 
                "系统内部错误");
        response.setTraceId(traceId);
        
        if ("dev".equals(activeProfile)) {
            response.setData(ExceptionUtils.buildErrorDetail(e, traceId));
        }
        
        // 运行时异常需要告警
        sendAlert(e, request, traceId);
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Object>> handleException(
            Exception e, HttpServletRequest request) {
        
        String traceId = ExceptionUtils.generateTraceId();
        
        log.error("未知异常 - URI: {}, TraceId: {}", 
                request.getRequestURI(), traceId, e);
        
        ApiResponse<Object> response = ApiResponse.error(ErrorCode.SYSTEM_ERROR.getCode(), 
                "系统繁忙，请稍后重试");
        response.setTraceId(traceId);
        
        if ("dev".equals(activeProfile)) {
            response.setData(ExceptionUtils.buildErrorDetail(e, traceId));
        }
        
        // 未知异常需要告警
        sendAlert(e, request, traceId);
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 根据错误码获取HTTP状态码
     */
    private HttpStatus getHttpStatus(ErrorCode errorCode) {
        if (errorCode.getCode() >= 3000 && errorCode.getCode() < 4000) {
            // 认证授权错误
            if (errorCode == ErrorCode.UNAUTHORIZED) {
                return HttpStatus.UNAUTHORIZED;
            } else if (errorCode == ErrorCode.ACCESS_DENIED) {
                return HttpStatus.FORBIDDEN;
            }
        } else if (errorCode.getCode() >= 2000 && errorCode.getCode() < 3000) {
            // 参数错误
            return HttpStatus.BAD_REQUEST;
        } else if (errorCode.getCode() >= 9000) {
            // 业务逻辑错误
            if (errorCode == ErrorCode.DATA_NOT_FOUND) {
                return HttpStatus.NOT_FOUND;
            }
        }
        
        return HttpStatus.OK;
    }

    /**
     * 发送告警
     */
    private void sendAlert(Throwable e, HttpServletRequest request, String traceId) {
        try {
            // TODO: 实现告警逻辑，如发送邮件、短信、钉钉等
            log.error("系统告警 - TraceId: {}, URI: {}, Exception: {}", 
                    traceId, request.getRequestURI(), e.getClass().getSimpleName());
        } catch (Exception alertException) {
            log.error("发送告警失败", alertException);
        }
    }
}
