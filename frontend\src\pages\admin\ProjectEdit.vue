<template>
  <div class="admin-project-edit">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button text @click="$router.back()">
          <el-icon>
            <ArrowLeft />
          </el-icon>
          返回
        </el-button>
        <div class="title-section">
          <h1 class="page-title">编辑项目</h1>
          <p class="page-description">修改项目信息和设置</p>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="saving" @click="handleSave">
          保存修改
        </el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 编辑表单 -->
    <div v-else-if="projectData" class="form-container">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px" size="large">
        <el-row :gutter="24">
          <!-- 左侧表单 -->
          <el-col :span="16">
            <el-card title="基本信息">
              <el-form-item label="项目标题" prop="title">
                <el-input v-model="formData.title" placeholder="请输入项目标题" maxlength="200" show-word-limit />
              </el-form-item>

              <el-form-item label="项目分类" prop="category">
                <el-select v-model="formData.category" placeholder="请选择项目分类" style="width: 100%">
                  <el-option label="医疗救助" value="medical" />
                  <el-option label="教育助学" value="education" />
                  <el-option label="灾难救援" value="disaster" />
                  <el-option label="扶贫济困" value="poverty" />
                  <el-option label="其他" value="other" />
                </el-select>
              </el-form-item>

              <el-form-item label="目标金额" prop="targetAmount">
                <el-input-number v-model="formData.targetAmount" :min="1" :max="10000000" :step="100" :precision="2"
                  style="width: 100%" placeholder="请输入目标金额" />
              </el-form-item>

              <el-form-item label="项目描述" prop="description">
                <el-input v-model="formData.description" type="textarea" :rows="6" placeholder="请详细描述项目情况、救助对象、资金用途等信息"
                  maxlength="2000" show-word-limit />
              </el-form-item>

              <el-form-item label="联系信息" prop="contactInfo">
                <el-input v-model="formData.contactInfo" type="textarea" :rows="3" placeholder="请输入联系人姓名、电话等联系方式"
                  maxlength="500" show-word-limit />
              </el-form-item>
            </el-card>

            <!-- 项目图片 -->
            <el-card title="项目图片" style="margin-top: 24px;">
              <div class="image-upload-section">
                <div class="current-images" v-if="imageList.length > 0">
                  <div class="image-grid">
                    <div v-for="(image, index) in imageList" :key="index" class="image-item">
                      <el-image :src="image" fit="cover" class="image-preview" :preview-src-list="imageList"
                        :initial-index="index" />
                      <div class="image-actions">
                        <el-button text type="danger" size="small" @click="removeImage(index)">
                          <el-icon>
                            <Delete />
                          </el-icon>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>

                <el-upload ref="uploadRef" :action="uploadAction" :headers="uploadHeaders" :data="uploadData"
                  :on-success="handleUploadSuccess" :on-error="handleUploadError" :before-upload="beforeUpload"
                  :show-file-list="false" accept="image/*" multiple>
                  <el-button type="primary">
                    <el-icon>
                      <Plus />
                    </el-icon>
                    上传图片
                  </el-button>
                  <template #tip>
                    <div class="upload-tip">
                      支持 JPG、PNG 格式，单个文件不超过 5MB，最多上传 5 张图片
                    </div>
                  </template>
                </el-upload>
              </div>
            </el-card>
          </el-col>

          <!-- 右侧信息 -->
          <el-col :span="8">
            <el-card title="项目设置">
              <el-form-item label="项目状态" prop="status">
                <el-select v-model="formData.status" placeholder="请选择项目状态" style="width: 100%">
                  <el-option label="进行中" value="active" />
                  <el-option label="已完成" value="completed" />
                  <el-option label="已关闭" value="closed" />
                  <el-option label="草稿" value="draft" />
                </el-select>
              </el-form-item>

              <el-form-item label="当前金额">
                <div style="display: flex; gap: 8px;">
                  <el-input-number v-model="formData.currentAmount" :min="0" :max="formData.targetAmount" :step="100"
                    :precision="2" style="flex: 1" placeholder="当前已筹金额" />
                  <el-button type="primary" size="small" :loading="updatingAmount" @click="handleUpdateAmount">
                    更新金额
                  </el-button>
                </div>
                <div style="font-size: 12px; color: #999; margin-top: 4px;">
                  注意：修改当前金额会立即生效，请谨慎操作
                </div>
              </el-form-item>

              <el-form-item label="创建时间">
                <el-input :value="formatTime(projectData.createTime)" readonly style="width: 100%" />
              </el-form-item>

              <el-form-item label="更新时间">
                <el-input :value="formatTime(projectData.updateTime)" readonly style="width: 100%" />
              </el-form-item>
            </el-card>

            <!-- 项目统计 -->
            <el-card title="项目统计" style="margin-top: 24px;">
              <div class="stats-item">
                <span class="stats-label">筹款进度</span>
                <div class="stats-value">
                  <el-progress :percentage="getProgress()" :stroke-width="8" />
                  <span class="progress-text">{{ getProgress() }}%</span>
                </div>
              </div>

              <div class="stats-item">
                <span class="stats-label">已筹金额</span>
                <span class="stats-value">¥{{ formatAmount(formData.currentAmount) }}</span>
              </div>

              <div class="stats-item">
                <span class="stats-label">目标金额</span>
                <span class="stats-value">¥{{ formatAmount(formData.targetAmount) }}</span>
              </div>

              <div class="stats-item">
                <span class="stats-label">剩余金额</span>
                <span class="stats-value">¥{{ formatAmount(formData.targetAmount - formData.currentAmount) }}</span>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 项目不存在 -->
    <div v-else class="not-found">
      <el-empty description="项目不存在或已被删除">
        <el-button type="primary" @click="$router.push('/admin/projects')">
          返回项目列表
        </el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted, computed } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    ArrowLeft,
    Plus,
    Delete
  } from '@element-plus/icons-vue'
  import {
    getProjectById,
    updateProject,
    updateProjectCurrentAmount
  } from '@/api/admin'
  import { uploadProjectImage } from '@/api/file'
  import { useUserStore } from '@/stores/user'

  const route = useRoute()
  const router = useRouter()
  const userStore = useUserStore()

  // 响应式数据
  const loading = ref(true)
  const saving = ref(false)
  const updatingAmount = ref(false)
  const projectData = ref(null)
  const formRef = ref()

  // 表单数据
  const formData = reactive({
    title: '',
    category: '',
    description: '',
    contactInfo: '',
    targetAmount: 0,
    currentAmount: 0,
    status: 'active'
  })

  // 图片相关
  const imageList = ref([])
  const uploadRef = ref()

  // 上传配置
  const uploadAction = computed(() => 'http://localhost:8080/api/files/upload/project')
  const uploadHeaders = computed(() => ({
    'Authorization': `Bearer ${userStore.token}`
  }))
  const uploadData = computed(() => ({
    category: 'project'
  }))

  // 表单验证规则
  const formRules = {
    title: [
      { required: true, message: '请输入项目标题', trigger: 'blur' },
      { min: 5, max: 200, message: '标题长度应在 5 到 200 个字符', trigger: 'blur' }
    ],
    category: [
      { required: true, message: '请选择项目分类', trigger: 'change' }
    ],
    description: [
      { required: true, message: '请输入项目描述', trigger: 'blur' },
      { min: 20, max: 2000, message: '描述长度应在 20 到 2000 个字符', trigger: 'blur' }
    ],
    contactInfo: [
      { required: true, message: '请输入联系信息', trigger: 'blur' },
      { min: 5, max: 500, message: '联系信息长度应在 5 到 500 个字符', trigger: 'blur' }
    ],
    targetAmount: [
      { required: true, message: '请输入目标金额', trigger: 'blur' },
      { type: 'number', min: 1, message: '目标金额必须大于 0', trigger: 'blur' }
    ],
    status: [
      { required: true, message: '请选择项目状态', trigger: 'change' }
    ]
  }

  // 获取项目ID
  const projectId = computed(() => route.params.id)

  // 加载项目数据
  const loadProjectData = async () => {
    try {
      loading.value = true

      // 获取项目详情
      const response = await getProjectById(projectId.value)

      if (response) {
        projectData.value = response

        // 填充表单数据
        Object.assign(formData, {
          title: projectData.value.title || '',
          category: projectData.value.category || '',
          description: projectData.value.description || '',
          contactInfo: projectData.value.contactInfo || '',
          targetAmount: projectData.value.targetAmount || 0,
          currentAmount: projectData.value.currentAmount || 0,
          status: projectData.value.status || 'active'
        })

        // 处理图片列表
        if (projectData.value.images) {
          if (Array.isArray(projectData.value.images)) {
            imageList.value = projectData.value.images
          } else if (typeof projectData.value.images === 'string') {
            // 如果是JSON字符串，尝试解析
            try {
              const parsed = JSON.parse(projectData.value.images)
              imageList.value = Array.isArray(parsed) ? parsed : []
            } catch (e) {
              // 如果不是JSON，按逗号分割
              imageList.value = projectData.value.images.split(',').filter(img => img.trim())
            }
          }
        } else {
          imageList.value = []
        }

      } else {
        ElMessage.error('项目不存在')
        projectData.value = null
      }

    } catch (error) {
      console.error('加载项目数据失败:', error)
      ElMessage.error('加载项目数据失败')
      projectData.value = null
    } finally {
      loading.value = false
    }
  }

  // 更新当前金额
  const handleUpdateAmount = async () => {
    try {
      // 验证金额
      if (formData.currentAmount < 0) {
        ElMessage.error('金额不能为负数')
        return
      }

      if (formData.currentAmount > formData.targetAmount) {
        ElMessage.error('当前金额不能超过目标金额')
        return
      }

      await ElMessageBox.confirm(
        `确定要将当前金额修改为 ¥${formData.currentAmount.toFixed(2)} 吗？`,
        '确认修改金额',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )

      updatingAmount.value = true

      await updateProjectCurrentAmount(projectId.value, formData.currentAmount)

      ElMessage.success('项目金额更新成功')

      // 重新加载项目数据
      await loadProjectData()

    } catch (error) {
      if (error === 'cancel') {
        return
      }
      console.error('更新金额失败:', error)
      ElMessage.error('更新金额失败: ' + (error.response?.data?.message || error.message))
    } finally {
      updatingAmount.value = false
    }
  }

  // 保存项目
  const handleSave = async () => {
    try {
      // 表单验证
      await formRef.value.validate()

      saving.value = true

      // 准备更新数据 - 按照后端DTO格式（不包含当前金额）
      const updateData = {
        id: parseInt(projectId.value),
        title: formData.title,
        description: formData.description,
        category: formData.category,
        targetAmount: formData.targetAmount,
        contactInfo: formData.contactInfo,
        status: formData.status,
        images: imageList.value // 后端期望List<String>格式
        // 注意：不包含 currentAmount，当前金额需要单独更新
      }

      await updateProject(projectId.value, updateData)

      ElMessage.success('项目更新成功')
      router.push('/admin/projects')

    } catch (error) {
      if (error.errors) {
        // 表单验证错误
        ElMessage.error('请检查表单填写是否正确')
      } else {
        console.error('保存项目失败:', error)
        // 显示后端返回的具体错误信息
        if (error.message) {
          ElMessage.error(error.message)
        }
      }
    } finally {
      saving.value = false
    }
  }

  // 取消编辑
  const handleCancel = async () => {
    try {
      await ElMessageBox.confirm(
        '确定要取消编辑吗？未保存的修改将会丢失。',
        '确认取消',
        {
          confirmButtonText: '确定',
          cancelButtonText: '继续编辑',
          type: 'warning'
        }
      )

      router.push('/admin/projects')

    } catch (error) {
      // 用户取消了取消操作，继续编辑
    }
  }

  // 图片上传处理
  const beforeUpload = (file) => {
    // 检查文件类型
    const isImage = file.type.startsWith('image/')
    if (!isImage) {
      ElMessage.error('只能上传图片文件！')
      return false
    }

    // 检查文件大小
    const isLt5M = file.size / 1024 / 1024 < 5
    if (!isLt5M) {
      ElMessage.error('图片大小不能超过 5MB！')
      return false
    }

    // 检查图片数量
    if (imageList.value.length >= 5) {
      ElMessage.error('最多只能上传 5 张图片！')
      return false
    }

    return true
  }

  const handleUploadSuccess = (response) => {
    // 处理响应数据 - 可能是直接的数据或包装在data中
    let fileData = response
    if (response && response.data) {
      fileData = response.data
    }

    if (fileData && fileData.fileUrl) {
      imageList.value.push(fileData.fileUrl)
      ElMessage.success('图片上传成功')
    } else {
      ElMessage.error('图片上传失败：响应格式错误')
    }
  }

  const handleUploadError = (error) => {
    console.error('图片上传失败:', error)
    ElMessage.error('图片上传失败')
  }

  // 删除图片
  const removeImage = (index) => {
    imageList.value.splice(index, 1)
    ElMessage.success('图片已删除')
  }

  // 工具函数
  const formatAmount = (amount) => {
    if (!amount) return '0'
    return Number(amount).toLocaleString()
  }

  const formatTime = (time) => {
    if (!time) return '-'
    return new Date(time).toLocaleString()
  }

  const getProgress = () => {
    if (!formData.targetAmount || formData.targetAmount === 0) return 0
    const progress = Math.round((formData.currentAmount / formData.targetAmount) * 100)
    return Math.min(progress, 100)
  }

  // 页面初始化
  onMounted(() => {
    loadProjectData()
  })
</script>

<style scoped>
  .admin-project-edit {
    max-width: 1400px;
    margin: 0 auto;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e8e8e8;
  }

  .header-left {
    display: flex;
    align-items: flex-start;
    gap: 16px;
  }

  .title-section {
    flex: 1;
  }

  .page-title {
    margin: 0 0 4px 0;
    color: #333;
    font-size: 24px;
    font-weight: 600;
  }

  .page-description {
    margin: 0;
    color: #666;
    font-size: 14px;
  }

  .header-right {
    display: flex;
    gap: 12px;
  }

  .loading-container {
    padding: 40px;
  }

  .form-container {
    margin-bottom: 24px;
  }

  .image-upload-section {
    space-y: 16px;
  }

  .image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 16px;
    margin-bottom: 16px;
  }

  .image-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e8e8e8;
  }

  .image-preview {
    width: 100%;
    height: 120px;
    cursor: pointer;
  }

  .image-actions {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 4px;
    padding: 4px;
  }

  .upload-tip {
    color: #999;
    font-size: 12px;
    margin-top: 8px;
  }

  .stats-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .stats-item:last-child {
    border-bottom: none;
  }

  .stats-label {
    color: #666;
    font-size: 14px;
  }

  .stats-value {
    color: #333;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .progress-text {
    font-size: 12px;
    color: #666;
  }

  .not-found {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .page-header {
      flex-direction: column;
      gap: 16px;
    }

    .header-left {
      width: 100%;
    }

    .header-right {
      width: 100%;
      justify-content: flex-start;
    }

    .el-col {
      margin-bottom: 24px;
    }

    .image-grid {
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      gap: 12px;
    }

    .image-preview {
      height: 100px;
    }
  }
</style>