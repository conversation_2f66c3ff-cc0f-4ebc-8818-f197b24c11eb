import request from './index'

// 获取项目列表
export const getProjects = (params) => {
  return request({
    url: '/projects',
    method: 'get',
    params
  })
}

// 获取项目详情
export const getProjectDetail = (id) => {
  return request({
    url: `/projects/${id}`,
    method: 'get'
  })
}

// 获取热门项目
export const getHotProjects = (limit = 6) => {
  return request({
    url: '/projects/hot',
    method: 'get',
    params: { limit }
  })
}

// 获取推荐项目
export const getRecommendedProjects = (limit = 6) => {
  return request({
    url: '/projects/recommended',
    method: 'get',
    params: { limit }
  })
}

// 创建项目
export const createProject = (data) => {
  return request({
    url: '/projects',
    method: 'post',
    data
  })
}

// 更新项目
export const updateProject = (id, data) => {
  return request({
    url: `/projects/${id}`,
    method: 'put',
    data
  })
}

// 获取项目统计信息
export const getProjectStats = () => {
  return request({
    url: '/projects/stats',
    method: 'get'
  })
}
