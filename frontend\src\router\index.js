import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

// 直接导入管理员组件（临时解决动态导入问题）
import AdminLayout from '@/pages/admin/AdminLayout.vue'
import Dashboard from '@/pages/admin/Dashboard.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/pages/Home.vue'),
    meta: { title: '首页' }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/pages/Login.vue'),
    meta: { title: '登录' }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/pages/Register.vue'),
    meta: { title: '注册' }
  },
  {
    path: '/chat',
    name: 'Chat',
    component: () => import('@/pages/Chat.vue'),
    meta: { title: 'AI助手' }
  },
  {
    path: '/projects',
    name: 'Projects',
    component: () => import('@/pages/Projects.vue'),
    meta: { title: '救助项目' }
  },
  {
    path: '/projects/:id',
    name: 'ProjectDetail',
    component: () => import('@/pages/ProjectDetail.vue'),
    meta: { title: '项目详情' }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/pages/Profile.vue'),
    meta: { title: '个人中心', requiresAuth: true }
  },
  {
    path: '/help-request/create',
    name: 'HelpRequestCreate',
    component: () => import('@/pages/HelpRequestCreate.vue'),
    meta: { title: '申请救助', requiresAuth: true }
  },
  {
    path: '/help-request/:id',
    name: 'HelpRequestDetail',
    component: () => import('@/pages/HelpRequestDetail.vue'),
    meta: { title: '申请详情', requiresAuth: true }
  },
  {
    path: '/admin',
    name: 'Admin',
    component: AdminLayout,
    meta: { title: '管理后台', requiresAuth: true, requiresAdmin: true },
    children: [
      {
        path: '',
        redirect: '/admin/dashboard'
      },
      {
        path: 'dashboard',
        name: 'AdminDashboard',
        component: Dashboard,
        meta: { title: '仪表板', requiresAuth: true, requiresAdmin: true }
      },
      {
        path: 'projects',
        name: 'AdminProjects',
        component: () => import('@/pages/admin/Projects.vue'),
        meta: { title: '项目管理', requiresAuth: true, requiresAdmin: true }
      },
      {
        path: 'projects/create',
        name: 'AdminProjectCreate',
        component: () => import('@/pages/admin/ProjectCreate.vue'),
        meta: { title: '创建项目', requiresAuth: true, requiresAdmin: true }
      },
      {
        path: 'projects/:id',
        name: 'AdminProjectDetail',
        component: () => import('@/pages/admin/ProjectDetail.vue'),
        meta: { title: '项目详情', requiresAuth: true, requiresAdmin: true }
      },
      {
        path: 'projects/edit/:id',
        name: 'AdminProjectEdit',
        component: () => import('@/pages/admin/ProjectEdit.vue'),
        meta: { title: '编辑项目', requiresAuth: true, requiresAdmin: true }
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: () => import('@/pages/admin/Users.vue'),
        meta: { title: '用户管理', requiresAuth: true, requiresAdmin: true }
      },
      {
        path: 'users/:id',
        name: 'AdminUserDetail',
        component: () => import('@/pages/admin/UserDetail.vue'),
        meta: { title: '用户详情', requiresAuth: true, requiresAdmin: true }
      },
      {
        path: 'help-requests',
        name: 'AdminHelpRequests',
        component: () => import('@/pages/admin/HelpRequests.vue'),
        meta: { title: '救助申请管理', requiresAuth: true, requiresAdmin: true }
      },
      {
        path: 'help-requests/:id',
        name: 'AdminHelpRequestDetail',
        component: () => import('@/pages/admin/HelpRequestDetail.vue'),
        meta: { title: '申请详情', requiresAuth: true, requiresAdmin: true }
      },
      {
        path: 'donations',
        name: 'AdminDonations',
        component: () => import('@/pages/admin/Donations.vue'),
        meta: { title: '捐赠管理', requiresAuth: true, requiresAdmin: true }
      },
      {
        path: 'donations/:id',
        name: 'AdminDonationDetail',
        component: () => import('@/pages/admin/DonationDetail.vue'),
        meta: { title: '捐赠详情', requiresAuth: true, requiresAdmin: true }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 爱心救助平台`
  }

  // 检查是否需要登录
  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    next('/login')
    return
  }

  // 检查是否需要管理员权限
  if (to.meta.requiresAdmin && userStore.isLoggedIn) {
    if (userStore.user?.role !== 'admin') {
      ElMessage.error('无权限访问管理后台')
      next('/')
      return
    }
  }

  next()
})

export default router
