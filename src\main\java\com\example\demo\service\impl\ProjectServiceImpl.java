package com.example.demo.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.entity.Project;
import com.example.demo.mapper.ProjectMapper;
import com.example.demo.mapper.DonationMapper;
import com.example.demo.service.ProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 救助项目服务实现类
 * 实现救助项目相关的业务逻辑
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class ProjectServiceImpl implements ProjectService {

    @Autowired
    private ProjectMapper projectMapper;

    @Autowired
    private DonationMapper donationMapper;

    // 暂时注释掉UserService依赖，避免循环依赖
    // @Autowired
    // private UserService userService;

    @Override
    public Project createProject(Project project, Long adminId) {
        log.info("创建救助项目: {}, 管理员ID: {}", project.getTitle(), adminId);
        
        // 业务校验
        validateProjectForCreate(project, adminId);
        
        // 设置管理员ID
        project.setAdminId(adminId);
        
        // 设置默认值
        if (project.getCurrentAmount() == null) {
            project.setCurrentAmount(BigDecimal.ZERO);
        }
        if (project.getStatus() == null) {
            project.setStatus(Project.Status.ACTIVE.getCode());
        }
        
        // 保存项目
        int result = projectMapper.insert(project);
        if (result > 0) {
            log.info("救助项目创建成功: {}", project.getTitle());
            return project;
        } else {
            throw new RuntimeException("救助项目创建失败");
        }
    }

    @Override
    public Project updateProject(Project project, Long adminId) {
        log.info("更新救助项目: {}, 管理员ID: {}", project.getId(), adminId);
        
        if (project.getId() == null || project.getId() <= 0) {
            throw new RuntimeException("项目ID不能为空");
        }
        
        // 检查项目是否存在
        Project existingProject = projectMapper.selectById(project.getId());
        if (existingProject == null) {
            throw new RuntimeException("项目不存在");
        }
        
        // 检查权限
        if (!hasProjectPermission(project.getId(), adminId)) {
            throw new RuntimeException("没有权限修改此项目");
        }
        
        // 业务校验
        validateProjectForUpdate(project);
        
        // 不允许直接修改当前金额
        project.setCurrentAmount(null);
        
        int result = projectMapper.updateById(project);
        if (result > 0) {
            log.info("救助项目更新成功: {}", project.getId());
            return findById(project.getId());
        } else {
            throw new RuntimeException("救助项目更新失败");
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Project findById(Long id) {
        if (id == null || id <= 0) {
            return null;
        }
        return projectMapper.selectById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Object findProjectDetail(Long id) {
        Project project = findById(id);
        if (project == null) {
            throw new RuntimeException("项目不存在");
        }
        
        Map<String, Object> detail = new HashMap<>();
        detail.put("project", project);
        
        // 获取项目统计信息
        BigDecimal totalDonation = donationMapper.sumDonationAmountByProject(id);
        Long donorCount = donationMapper.countDonorsByProject(id);
        
        detail.put("totalDonation", totalDonation != null ? totalDonation : BigDecimal.ZERO);
        detail.put("donorCount", donorCount != null ? donorCount : 0);
        
        // 计算完成百分比
        if (project.getTargetAmount() != null && project.getTargetAmount().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal currentAmount = project.getCurrentAmount() != null ? project.getCurrentAmount() : BigDecimal.ZERO;
            BigDecimal percentage = currentAmount.divide(project.getTargetAmount(), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            detail.put("completionPercentage", percentage);
        } else {
            detail.put("completionPercentage", BigDecimal.ZERO);
        }
        
        return detail;
    }

    @Override
    @Transactional(readOnly = true)
    public IPage<Project> findProjectsWithConditions(Page<Project> page, String category, String status, String keyword) {
        return projectMapper.findProjectsWithConditions(page, category, status, keyword);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Project> findByCategoryAndStatus(String category, String status) {
        if (!StringUtils.hasText(category) || !StringUtils.hasText(status)) {
            throw new RuntimeException("分类和状态不能为空");
        }
        return projectMapper.findByCategoryAndStatus(category, status);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Project> findByAdminId(Long adminId) {
        if (adminId == null || adminId <= 0) {
            throw new RuntimeException("管理员ID不能为空");
        }
        return projectMapper.findByAdminId(adminId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Project> findHotProjects(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        return projectMapper.findHotProjects(limit);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Project> findLatestProjects(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        return projectMapper.findLatestProjects(limit);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Project> findNearCompletionProjects(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        return projectMapper.findNearCompletionProjects(limit);
    }

    @Override
    public boolean updateProjectStatus(Long projectId, String status, Long adminId) {
        log.info("更新项目状态: projectId={}, status={}, adminId={}", projectId, status, adminId);
        
        if (projectId == null || projectId <= 0) {
            throw new RuntimeException("项目ID不能为空");
        }
        
        if (!StringUtils.hasText(status)) {
            throw new RuntimeException("状态不能为空");
        }
        
        // 验证状态值
        if (!isValidProjectStatus(status)) {
            throw new RuntimeException("无效的项目状态");
        }
        
        // 检查权限
        if (!hasProjectPermission(projectId, adminId)) {
            throw new RuntimeException("没有权限修改此项目");
        }
        
        int result = projectMapper.updateProjectStatus(projectId, status);
        if (result > 0) {
            log.info("项目状态更新成功: projectId={}, status={}", projectId, status);
            return true;
        } else {
            throw new RuntimeException("项目状态更新失败");
        }
    }

    @Override
    public boolean updateCurrentAmount(Long projectId, BigDecimal amount) {
        log.info("更新项目筹集金额: projectId={}, amount={}", projectId, amount);
        
        if (projectId == null || projectId <= 0) {
            throw new RuntimeException("项目ID不能为空");
        }
        
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("金额必须大于0");
        }
        
        // 检查项目是否可以接受捐赠
        if (!canAcceptDonation(projectId)) {
            throw new RuntimeException("项目当前状态不能接受捐赠");
        }
        
        int result = projectMapper.updateCurrentAmount(projectId, amount);
        if (result > 0) {
            log.info("项目筹集金额更新成功: projectId={}, amount={}", projectId, amount);
            
            // 检查是否达到目标金额，自动更新状态
            if (isTargetReached(projectId)) {
                updateProjectStatus(projectId, Project.Status.COMPLETED.getCode(), null);
            }
            
            return true;
        } else {
            throw new RuntimeException("项目筹集金额更新失败");
        }
    }

    @Override
    public boolean reduceCurrentAmount(Long projectId, BigDecimal amount) {
        log.info("减少项目筹集金额: projectId={}, amount={}", projectId, amount);

        if (projectId == null || projectId <= 0) {
            throw new RuntimeException("项目ID不能为空");
        }

        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("减少金额必须大于0");
        }

        // 获取项目当前金额
        Project project = findById(projectId);
        if (project == null) {
            throw new RuntimeException("项目不存在");
        }

        BigDecimal currentAmount = project.getCurrentAmount();
        BigDecimal newAmount = currentAmount.subtract(amount);

        // 确保金额不为负数
        if (newAmount.compareTo(BigDecimal.ZERO) < 0) {
            newAmount = BigDecimal.ZERO;
        }

        // 直接设置新金额
        int result = projectMapper.setCurrentAmount(projectId, newAmount);
        if (result > 0) {
            log.info("项目筹集金额减少成功: projectId={}, oldAmount={}, newAmount={}, reducedAmount={}",
                    projectId, currentAmount, newAmount, amount);
            return true;
        } else {
            throw new RuntimeException("项目筹集金额减少失败");
        }
    }

    @Override
    public boolean setCurrentAmount(Long projectId, BigDecimal amount) {
        log.info("设置项目筹集金额: projectId={}, amount={}", projectId, amount);

        if (projectId == null || projectId <= 0) {
            throw new RuntimeException("项目ID不能为空");
        }

        if (amount == null || amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new RuntimeException("金额不能为负数");
        }

        // 获取项目信息验证
        Project project = findById(projectId);
        if (project == null) {
            throw new RuntimeException("项目不存在");
        }

        // 验证金额不超过目标金额
        if (amount.compareTo(project.getTargetAmount()) > 0) {
            throw new RuntimeException("当前金额不能超过目标金额");
        }

        // 直接设置新金额
        int result = projectMapper.setCurrentAmount(projectId, amount);
        if (result > 0) {
            log.info("项目筹集金额设置成功: projectId={}, oldAmount={}, newAmount={}",
                    projectId, project.getCurrentAmount(), amount);

            // 检查是否达到目标金额，自动更新状态
            if (amount.compareTo(project.getTargetAmount()) >= 0) {
                updateProjectStatus(projectId, Project.Status.COMPLETED.getCode(), null);
            }

            return true;
        } else {
            throw new RuntimeException("项目筹集金额设置失败");
        }
    }

    @Override
    public boolean deleteProject(Long projectId, Long adminId) {
        log.info("删除项目: projectId={}, adminId={}", projectId, adminId);
        
        if (projectId == null || projectId <= 0) {
            throw new RuntimeException("项目ID不能为空");
        }
        
        Project project = projectMapper.selectById(projectId);
        if (project == null) {
            throw new RuntimeException("项目不存在");
        }
        
        // 检查权限
        if (!hasProjectPermission(projectId, adminId)) {
            throw new RuntimeException("没有权限删除此项目");
        }
        
        // 检查是否有捐赠记录
        BigDecimal totalDonation = donationMapper.sumDonationAmountByProject(projectId);
        if (totalDonation != null && totalDonation.compareTo(BigDecimal.ZERO) > 0) {
            throw new RuntimeException("已有捐赠记录的项目不能删除，只能关闭");
        }
        
        int result = projectMapper.deleteById(projectId);
        if (result > 0) {
            log.info("项目删除成功: projectId={}", projectId);
            return true;
        } else {
            throw new RuntimeException("项目删除失败");
        }
    }

    @Override
    public boolean batchDeleteProjects(List<Long> projectIds, Long adminId) {
        log.info("批量删除项目: projectIds={}, adminId={}", projectIds, adminId);
        
        if (projectIds == null || projectIds.isEmpty()) {
            throw new RuntimeException("项目ID列表不能为空");
        }
        
        // 逐个检查权限和业务规则
        for (Long projectId : projectIds) {
            if (!hasProjectPermission(projectId, adminId)) {
                throw new RuntimeException("没有权限删除项目ID: " + projectId);
            }
            
            BigDecimal totalDonation = donationMapper.sumDonationAmountByProject(projectId);
            if (totalDonation != null && totalDonation.compareTo(BigDecimal.ZERO) > 0) {
                throw new RuntimeException("项目ID " + projectId + " 已有捐赠记录，不能删除");
            }
        }
        
        int result = projectMapper.deleteBatchIds(projectIds);
        if (result > 0) {
            log.info("批量删除项目成功: 删除{}个项目", result);
            return true;
        } else {
            throw new RuntimeException("批量删除项目失败");
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Long countProjects(String category, String status) {
        return projectMapper.countProjects(category, status);
    }

    @Override
    @Transactional(readOnly = true)
    public BigDecimal sumCurrentAmount(String status) {
        BigDecimal sum = projectMapper.sumCurrentAmount(status);
        return sum != null ? sum : BigDecimal.ZERO;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canAcceptDonation(Long projectId) {
        if (projectId == null || projectId <= 0) {
            return false;
        }
        
        Project project = projectMapper.selectById(projectId);
        if (project == null) {
            return false;
        }
        
        // 只有进行中的项目可以接受捐赠
        return Project.Status.ACTIVE.getCode().equals(project.getStatus());
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isTargetReached(Long projectId) {
        if (projectId == null || projectId <= 0) {
            return false;
        }
        
        Project project = projectMapper.selectById(projectId);
        if (project == null || project.getTargetAmount() == null || project.getCurrentAmount() == null) {
            return false;
        }
        
        return project.getCurrentAmount().compareTo(project.getTargetAmount()) >= 0;
    }

    @Override
    public int autoUpdateCompletedProjects() {
        log.info("自动更新已完成的项目状态");
        
        // 查询所有进行中的项目
        List<Project> activeProjects = projectMapper.findByCategoryAndStatus(null, Project.Status.ACTIVE.getCode());
        
        int updatedCount = 0;
        for (Project project : activeProjects) {
            if (isTargetReached(project.getId())) {
                updateProjectStatus(project.getId(), Project.Status.COMPLETED.getCode(), null);
                updatedCount++;
            }
        }
        
        log.info("自动更新完成，共更新{}个项目状态", updatedCount);
        return updatedCount;
    }

    @Override
    @Transactional(readOnly = true)
    public List<Object> getProjectStatsByCategory() {
        return projectMapper.getProjectStatsByCategory();
    }

    @Override
    @Transactional(readOnly = true)
    public Object getProjectOverviewStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 总项目数
        Long totalProjects = countProjects(null, null);
        stats.put("totalProjects", totalProjects);
        
        // 进行中项目数
        Long activeProjects = countProjects(null, Project.Status.ACTIVE.getCode());
        stats.put("activeProjects", activeProjects);
        
        // 已完成项目数
        Long completedProjects = countProjects(null, Project.Status.COMPLETED.getCode());
        stats.put("completedProjects", completedProjects);
        
        // 已关闭项目数
        Long closedProjects = countProjects(null, Project.Status.CLOSED.getCode());
        stats.put("closedProjects", closedProjects);
        
        // 总筹集金额
        BigDecimal totalAmount = sumCurrentAmount(null);
        stats.put("totalAmount", totalAmount);
        
        // 进行中项目筹集金额
        BigDecimal activeAmount = sumCurrentAmount(Project.Status.ACTIVE.getCode());
        stats.put("activeAmount", activeAmount);
        
        return stats;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasProjectPermission(Long projectId, Long adminId) {
        if (projectId == null || projectId <= 0 || adminId == null || adminId <= 0) {
            return false;
        }
        
        // 检查用户是否为管理员 - 暂时注释掉避免循环依赖
        // User admin = userService.findById(adminId);
        // if (admin == null || !User.Role.ADMIN.getCode().equals(admin.getRole())) {
        //     return false;
        // }
        
        Project project = projectMapper.selectById(projectId);
        if (project == null) {
            return false;
        }
        
        // 项目创建者或超级管理员有权限
        return project.getAdminId().equals(adminId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Project> getRecommendedProjects(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 6;
        }
        
        // 推荐策略：优先推荐即将完成的项目，然后是最新项目
        List<Project> nearCompletion = findNearCompletionProjects(limit / 2);
        List<Project> latest = findLatestProjects(limit - nearCompletion.size());
        
        nearCompletion.addAll(latest);
        return nearCompletion;
    }

    @Override
    @Transactional(readOnly = true)
    public List<Project> searchProjects(String keyword, String category, Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 20;
        }
        
        Page<Project> page = new Page<>(1, limit);
        IPage<Project> result = findProjectsWithConditions(page, category, Project.Status.ACTIVE.getCode(), keyword);
        return result.getRecords();
    }

    /**
     * 验证项目创建信息
     */
    private void validateProjectForCreate(Project project, Long adminId) {
        if (project == null) {
            throw new RuntimeException("项目信息不能为空");
        }
        
        if (adminId == null || adminId <= 0) {
            throw new RuntimeException("管理员ID不能为空");
        }
        
        // 检查管理员权限 - 暂时注释掉避免循环依赖
        // User admin = userService.findById(adminId);
        // if (admin == null || !User.Role.ADMIN.getCode().equals(admin.getRole())) {
        //     throw new RuntimeException("只有管理员可以创建项目");
        // }
        
        validateProjectBasicInfo(project);
    }

    /**
     * 验证项目更新信息
     */
    private void validateProjectForUpdate(Project project) {
        if (project == null) {
            throw new RuntimeException("项目信息不能为空");
        }
        
        validateProjectBasicInfo(project);
    }

    /**
     * 验证项目基本信息
     */
    private void validateProjectBasicInfo(Project project) {
        if (!StringUtils.hasText(project.getTitle())) {
            throw new RuntimeException("项目标题不能为空");
        }
        
        if (project.getTitle().length() > 200) {
            throw new RuntimeException("项目标题长度不能超过200字符");
        }
        
        if (!StringUtils.hasText(project.getCategory())) {
            throw new RuntimeException("项目分类不能为空");
        }
        
        if (!isValidProjectCategory(project.getCategory())) {
            throw new RuntimeException("无效的项目分类");
        }
        
        if (project.getTargetAmount() == null || project.getTargetAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("目标金额必须大于0");
        }
        
        if (project.getTargetAmount().compareTo(new BigDecimal("10000000")) > 0) {
            throw new RuntimeException("目标金额不能超过1000万");
        }
    }

    /**
     * 验证项目分类是否有效
     */
    private boolean isValidProjectCategory(String category) {
        for (Project.Category cat : Project.Category.values()) {
            if (cat.getCode().equals(category)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 验证项目状态是否有效
     */
    private boolean isValidProjectStatus(String status) {
        for (Project.Status stat : Project.Status.values()) {
            if (stat.getCode().equals(status)) {
                return true;
            }
        }
        return false;
    }

    @Override
    @Transactional(readOnly = true)
    public Long getProjectDonorCount(Long projectId) {
        if (projectId == null || projectId <= 0) {
            return 0L;
        }

        // 这里应该查询捐赠表统计不同用户的数量
        // 暂时返回模拟数据
        return 10L; // TODO: 实现真实的捐赠人数统计
    }

    @Override
    @Transactional(readOnly = true)
    public Object getProjectDetailStats(Long projectId) {
        if (projectId == null || projectId <= 0) {
            return null;
        }

        Map<String, Object> stats = new HashMap<>();

        // 获取项目基本信息
        Project project = projectMapper.selectById(projectId);
        if (project == null) {
            return null;
        }

        stats.put("projectId", projectId);
        stats.put("title", project.getTitle());
        stats.put("targetAmount", project.getTargetAmount());
        stats.put("currentAmount", project.getCurrentAmount());
        stats.put("donorCount", getProjectDonorCount(projectId));

        // TODO: 添加更多详细统计信息，如捐赠趋势、捐赠分布等

        return stats;
    }

    @Override
    @Transactional(readOnly = true)
    public Long countTodayNewProjects() {
        // 简化实现，实际应该查询今天创建的项目数量
        Long totalProjects = projectMapper.countProjects(null, null);
        return totalProjects / 20; // 模拟今日新增项目数
    }
}
