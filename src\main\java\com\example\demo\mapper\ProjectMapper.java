package com.example.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.entity.Project;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;

/**
 * 救助项目Mapper接口
 * 继承MyBatis Plus的BaseMapper，提供基础的CRUD操作
 */
@Mapper
public interface ProjectMapper extends BaseMapper<Project> {

    /**
     * 根据分类查询项目列表
     * @param category 项目分类
     * @param status 项目状态
     * @return 项目列表
     */
    @Select("SELECT * FROM projects WHERE category = #{category} AND status = #{status} ORDER BY create_time DESC")
    List<Project> findByCategoryAndStatus(@Param("category") String category, @Param("status") String status);

    /**
     * 根据管理员ID查询项目列表
     * @param adminId 管理员ID
     * @return 项目列表
     */
    @Select("SELECT * FROM projects WHERE admin_id = #{adminId} ORDER BY create_time DESC")
    List<Project> findByAdminId(@Param("adminId") Long adminId);

    /**
     * 分页查询项目列表
     * @param page 分页参数
     * @param category 项目分类（可选）
     * @param status 项目状态（可选）
     * @param keyword 关键词搜索（标题或描述）
     * @return 分页项目列表
     */
    @Select("<script>" +
            "SELECT * FROM projects WHERE 1=1 " +
            "<if test='category != null and category != \"\"'> AND category = #{category} </if>" +
            "<if test='status != null and status != \"\"'> AND status = #{status} </if>" +
            "<if test='keyword != null and keyword != \"\"'> " +
            "AND (title LIKE CONCAT('%', #{keyword}, '%') OR description LIKE CONCAT('%', #{keyword}, '%')) " +
            "</if>" +
            "ORDER BY create_time DESC" +
            "</script>")
    IPage<Project> findProjectsWithConditions(Page<Project> page,
                                             @Param("category") String category,
                                             @Param("status") String status,
                                             @Param("keyword") String keyword);

    /**
     * 查询热门项目（按捐赠金额排序）
     * @param limit 限制数量
     * @return 热门项目列表
     */
    @Select("SELECT * FROM projects WHERE status = 'active' ORDER BY current_amount DESC LIMIT #{limit}")
    List<Project> findHotProjects(@Param("limit") Integer limit);

    /**
     * 查询最新项目
     * @param limit 限制数量
     * @return 最新项目列表
     */
    @Select("SELECT * FROM projects WHERE status = 'active' ORDER BY create_time DESC LIMIT #{limit}")
    List<Project> findLatestProjects(@Param("limit") Integer limit);

    /**
     * 查询即将完成的项目（筹集进度>80%）
     * @param limit 限制数量
     * @return 即将完成的项目列表
     */
    @Select("SELECT * FROM projects WHERE status = 'active' " +
            "AND current_amount / target_amount >= 0.8 " +
            "ORDER BY (current_amount / target_amount) DESC LIMIT #{limit}")
    List<Project> findNearCompletionProjects(@Param("limit") Integer limit);

    /**
     * 统计项目数量
     * @param category 项目分类（可选）
     * @param status 项目状态（可选）
     * @return 项目数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM projects WHERE 1=1 " +
            "<if test='category != null and category != \"\"'> AND category = #{category} </if>" +
            "<if test='status != null and status != \"\"'> AND status = #{status} </if>" +
            "</script>")
    Long countProjects(@Param("category") String category, @Param("status") String status);

    /**
     * 统计总筹集金额
     * @param status 项目状态（可选）
     * @return 总筹集金额
     */
    @Select("<script>" +
            "SELECT COALESCE(SUM(current_amount), 0) FROM projects WHERE 1=1 " +
            "<if test='status != null and status != \"\"'> AND status = #{status} </if>" +
            "</script>")
    BigDecimal sumCurrentAmount(@Param("status") String status);

    /**
     * 更新项目当前筹集金额
     * @param projectId 项目ID
     * @param amount 增加的金额
     * @return 影响行数
     */
    @Update("UPDATE projects SET current_amount = current_amount + #{amount}, update_time = NOW() WHERE id = #{projectId}")
    int updateCurrentAmount(@Param("projectId") Long projectId, @Param("amount") BigDecimal amount);

    /**
     * 设置项目筹集金额
     * @param projectId 项目ID
     * @param amount 新的金额
     * @return 影响行数
     */
    @Update("UPDATE projects SET current_amount = #{amount}, update_time = NOW() WHERE id = #{projectId}")
    int setCurrentAmount(@Param("projectId") Long projectId, @Param("amount") BigDecimal amount);

    /**
     * 更新项目状态
     * @param id 项目ID
     * @param status 新状态
     * @return 影响行数
     */
    @Update("UPDATE projects SET status = #{status}, update_time = NOW() WHERE id = #{id}")
    int updateProjectStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 查询项目统计信息（按分类）
     * @return 分类统计列表
     */
    @Select("SELECT category, COUNT(*) as count, COALESCE(SUM(current_amount), 0) as total_amount " +
            "FROM projects GROUP BY category")
    List<Object> getProjectStatsByCategory();
}
