package com.example.demo.controller;

import com.example.demo.dto.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Spring Security测试Controller
 * 用于验证Spring Security配置是否正常工作
 */
@Slf4j
@RestController
@RequestMapping("/api/test/security")
@Tag(name = "Spring Security测试", description = "验证Spring Security配置的测试API")
public class SecurityTestController extends BaseController {

    @GetMapping("/public")
    @Operation(summary = "公开接口测试", description = "无需认证的公开接口")
    public ApiResponse<String> publicEndpoint() {
        return success("公开接口访问成功，无需认证");
    }

    @GetMapping("/authenticated")
    @Operation(summary = "认证接口测试", description = "需要认证的接口")
    public ApiResponse<Map<String, Object>> authenticatedEndpoint() {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "认证接口访问成功");
        result.put("userId", getCurrentUserId());
        result.put("username", getCurrentUsername());
        result.put("role", getCurrentUserRole());
        result.put("isAdmin", isAdmin());
        
        return success("认证成功", result);
    }

    @GetMapping("/admin-only")
    @Operation(summary = "管理员专用接口", description = "只有管理员可以访问")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<String> adminOnlyEndpoint() {
        return success("管理员接口访问成功");
    }

    @GetMapping("/user-only")
    @Operation(summary = "用户专用接口", description = "只有普通用户可以访问")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<String> userOnlyEndpoint() {
        return success("用户接口访问成功");
    }

    @GetMapping("/check-permissions")
    @Operation(summary = "权限检查", description = "检查当前用户的权限信息")
    public ApiResponse<Map<String, Object>> checkPermissions() {
        Map<String, Object> result = new HashMap<>();
        
        Long userId = getCurrentUserId();
        if (userId != null) {
            result.put("authenticated", true);
            result.put("userId", userId);
            result.put("username", getCurrentUsername());
            result.put("role", getCurrentUserRole());
            result.put("isAdmin", isAdmin());
            
            // 检查具体权限
            result.put("canAccessUserEndpoints", getCurrentUserRole() != null);
            result.put("canAccessAdminEndpoints", isAdmin());
        } else {
            result.put("authenticated", false);
            result.put("message", "未认证用户");
        }
        
        return success("权限检查完成", result);
    }

    @PostMapping("/test-method-security")
    @Operation(summary = "方法级安全测试", description = "测试方法级别的安全注解")
    public ApiResponse<String> testMethodSecurity(@RequestParam String action) {
        switch (action) {
            case "admin":
                return testAdminMethod();
            case "user":
                return testUserMethod();
            default:
                return success("无特殊权限要求的方法");
        }
    }

    @PreAuthorize("hasRole('ADMIN')")
    private ApiResponse<String> testAdminMethod() {
        return success("管理员方法执行成功");
    }

    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    private ApiResponse<String> testUserMethod() {
        return success("用户方法执行成功");
    }
}
