-- 爱心救助平台初始化数据
USE charity_platform;

-- 插入管理员用户 (密码: admin123，实际使用时需要加密)
INSERT INTO users (username, password, email, phone, real_name, role, status) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIgLbJJbYoKsY6Vw6g0pJZ9w8W', '<EMAIL>', '13800138000', '系统管理员', 'admin', 1),
('testuser', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIgLbJJbYoKsY6Vw6g0pJZ9w8W', '<EMAIL>', '***********', '测试用户', 'user', 1);

-- 插入示例救助项目
INSERT INTO projects (title, description, category, target_amount, current_amount, images, contact_info, status, admin_id) VALUES
('小明白血病治疗救助', '小明今年8岁，患有急性白血病，家庭经济困难，急需医疗费用支持。孩子病情严重，需要进行骨髓移植手术，医疗费用预计需要30万元。', 'medical', 300000.00, 85000.00, '["https://example.com/image1.jpg", "https://example.com/image2.jpg"]', '联系人：小明妈妈，电话：138****1234', 'active', 1),

('山区儿童助学计划', '为贫困山区的孩子们提供学习用品和生活补助，帮助他们完成学业。目前有50名儿童需要资助，每人每年需要2000元。', 'education', 100000.00, 45000.00, '["https://example.com/edu1.jpg", "https://example.com/edu2.jpg"]', '联系人：李老师，电话：139****5678', 'active', 1),

('洪灾紧急救援', '某地区遭受洪灾，100多户家庭受灾，急需生活物资和重建资金支持。', 'disaster', 200000.00, 120000.00, '["https://example.com/disaster1.jpg"]', '联系人：救援队长王先生，电话：137****9012', 'active', 1),

('孤寡老人关爱行动', '为社区孤寡老人提供生活照料和医疗支持，改善他们的生活条件。', 'poverty', 50000.00, 50000.00, '["https://example.com/elderly1.jpg"]', '联系人：社区主任张女士，电话：136****3456', 'completed', 1);

-- 插入示例捐赠记录
INSERT INTO donations (user_id, project_id, amount, payment_method, transaction_id, message, is_anonymous, status, donation_time) VALUES
(2, 1, 1000.00, 'alipay', 'ALI20241201001', '希望小明早日康复！', 0, 'success', '2024-12-01 10:30:00'),
(2, 1, 500.00, 'wechat', 'WX20241201002', '加油！', 0, 'success', '2024-12-01 14:20:00'),
(2, 2, 2000.00, 'alipay', 'ALI20241202001', '支持教育事业', 1, 'success', '2024-12-02 09:15:00'),
(2, 3, 1500.00, 'bank', 'BANK20241203001', '天灾无情人有情', 0, 'success', '2024-12-03 16:45:00');

-- 插入示例救助申请
INSERT INTO help_requests (user_id, title, description, category, amount_needed, contact_phone, contact_address, proof_images, status, admin_response, admin_id) VALUES
(2, '父亲突发心脏病急需手术费', '我父亲突发急性心肌梗塞，需要立即进行心脏搭桥手术，手术费用约15万元，家庭经济困难，恳请社会爱心人士帮助。', 'medical', 150000.00, '***********', '北京市朝阳区某某街道', '["https://example.com/proof1.jpg", "https://example.com/proof2.jpg"]', 'approved', '经审核，情况属实，已安排志愿者跟进。', 1),

(2, '失业后生活困难求助', '因公司倒闭失业3个月，积蓄用尽，家中还有老人和孩子需要照顾，生活陷入困境。', 'poverty', 5000.00, '***********', '上海市浦东新区某某小区', '["https://example.com/proof3.jpg"]', 'processing', '正在核实相关情况，请耐心等待。', 1);

-- 插入示例AI对话记录
INSERT INTO conversations (user_id, session_id, message, response, conversation_type, create_time) VALUES
(2, 'session_001', '你好，我想了解如何申请救助', '您好！很高兴为您服务。申请救助需要以下步骤：1. 注册并登录账户 2. 填写救助申请表 3. 上传相关证明材料 4. 等待管理员审核。请问您遇到了什么困难呢？', 'help_request', '2024-12-01 09:00:00'),

(2, 'session_001', '我想为白血病患者捐款', '非常感谢您的爱心！目前我们有一个小明白血病治疗救助项目，已筹集8.5万元，还需要21.5万元。您可以通过支付宝、微信或银行转账进行捐赠。需要我为您介绍详细情况吗？', 'donation', '2024-12-01 10:00:00'),

(NULL, 'session_002', '你们平台可靠吗？', '我们是正规的爱心救助平台，所有项目都经过严格审核，资金使用透明公开。我们会定期公布项目进展和资金使用情况，确保每一分钱都用到需要帮助的人身上。您可以查看我们的项目详情和捐赠记录。', 'general', '2024-12-02 15:30:00');

-- 插入系统配置数据（可选，用于存储一些系统参数）
CREATE TABLE IF NOT EXISTS system_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    description VARCHAR(255),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '系统配置表';

INSERT INTO system_config (config_key, config_value, description) VALUES
('platform_name', '温暖之家爱心救助平台', '平台名称'),
('contact_email', '<EMAIL>', '联系邮箱'),
('contact_phone', '************', '联系电话'),
('ai_welcome_message', '您好！我是爱心助手小暖，我可以帮您了解救助项目、捐赠流程，或解答相关问题。请问需要什么帮助？', 'AI欢迎语'),
('min_donation_amount', '1.00', '最小捐赠金额'),
('max_donation_amount', '50000.00', '单次最大捐赠金额');
