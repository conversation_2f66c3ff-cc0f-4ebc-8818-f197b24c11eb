package com.example.demo.exception;

/**
 * 权限不足异常
 */
public class InsufficientPermissionException extends BusinessException {

    public InsufficientPermissionException() {
        super(ErrorCode.ACCESS_DENIED);
    }

    public InsufficientPermissionException(String message) {
        super(ErrorCode.ACCESS_DENIED, message);
    }

    public InsufficientPermissionException(String operation, String resource) {
        super(ErrorCode.ACCESS_DENIED, String.format("无权限执行操作：%s，资源：%s", operation, resource));
    }

    public InsufficientPermissionException(Throwable cause) {
        super(ErrorCode.ACCESS_DENIED, cause);
    }
}
