package com.example.demo.dto.request;

import com.example.demo.dto.PageRequestDTO;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 捐赠查询请求DTO
 */
@Schema(description = "捐赠查询请求")
public class DonationQueryDTO extends PageRequestDTO {

    @Schema(description = "用户ID", example = "1")
    private Long userId;

    @Schema(description = "项目ID", example = "1")
    private Long projectId;

    @Schema(description = "捐赠状态", example = "success")
    private String status;

    @Schema(description = "支付方式", example = "alipay")
    private String paymentMethod;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "是否匿名", example = "false")
    private Boolean isAnonymous;

    // Getters and Setters
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Boolean getIsAnonymous() {
        return isAnonymous;
    }

    public void setIsAnonymous(Boolean isAnonymous) {
        this.isAnonymous = isAnonymous;
    }
}
