package com.example.demo.controller;

import com.example.demo.dto.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Swagger测试Controller
 * 用于验证Swagger配置是否正常工作
 */
@Slf4j
@RestController
@RequestMapping("/api/test/swagger")
@Tag(name = "Swagger测试", description = "验证Swagger配置的测试API")
public class SwaggerTestController extends BaseController {

    @GetMapping("/info")
    @Operation(summary = "获取Swagger信息", description = "获取当前Swagger配置信息")
    public ApiResponse<Map<String, Object>> getSwaggerInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("title", "爱心救助平台 API 文档");
        info.put("version", "1.0.0");
        info.put("description", "温暖之家爱心救助平台的RESTful API接口文档");
        info.put("swaggerUiUrl", "http://localhost:8080/swagger-ui/index.html");
        info.put("apiDocsUrl", "http://localhost:8080/v3/api-docs");
        info.put("message", "Swagger配置正常");
        
        return success("Swagger信息获取成功", info);
    }

    @GetMapping("/public")
    @Operation(summary = "公开测试接口", description = "无需认证的公开测试接口")
    public ApiResponse<String> publicTest() {
        return success("公开接口访问成功，Swagger配置正常");
    }

    @GetMapping("/paths")
    @Operation(summary = "获取Swagger访问路径", description = "获取所有可用的Swagger访问路径")
    public ApiResponse<Map<String, String>> getSwaggerPaths() {
        Map<String, String> paths = new HashMap<>();
        paths.put("Swagger UI (推荐)", "http://localhost:8080/swagger-ui/index.html");
        paths.put("Swagger UI (备用)", "http://localhost:8080/swagger-ui.html");
        paths.put("API文档 JSON", "http://localhost:8080/v3/api-docs");
        paths.put("API文档 YAML", "http://localhost:8080/v3/api-docs.yaml");
        
        return success("Swagger路径获取成功", paths);
    }
}
