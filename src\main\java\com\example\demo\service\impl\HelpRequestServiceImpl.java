package com.example.demo.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.entity.HelpRequest;
import com.example.demo.entity.User;
import com.example.demo.mapper.HelpRequestMapper;
import com.example.demo.service.HelpRequestService;
import com.example.demo.service.UserService;
import com.example.demo.service.ProjectService;
import com.example.demo.entity.Project;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 救助申请服务实现类
 * 实现救助申请相关的业务逻辑
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class HelpRequestServiceImpl implements HelpRequestService {

    @Autowired
    private HelpRequestMapper helpRequestMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private ProjectService projectService;

    @Override
    public HelpRequest createHelpRequest(HelpRequest helpRequest) {
        log.info("创建救助申请: userId={}, title={}, category={}", 
                helpRequest.getUserId(), helpRequest.getTitle(), helpRequest.getCategory());
        
        // 业务校验
        validateHelpRequestForCreate(helpRequest);
        
        // 检查用户是否可以申请救助
        if (!canUserApplyForHelp(helpRequest.getUserId(), helpRequest.getCategory())) {
            throw new RuntimeException("当前无法申请救助");
        }
        
        // 设置默认值
        if (helpRequest.getStatus() == null) {
            helpRequest.setStatus(HelpRequest.Status.PENDING.getCode());
        }
        
        // 保存申请记录
        int result = helpRequestMapper.insert(helpRequest);
        if (result > 0) {
            log.info("救助申请创建成功: {}", helpRequest.getId());
            
            // 自动分配审核员
            autoAssignReviewer(helpRequest.getId());
            
            return helpRequest;
        } else {
            throw new RuntimeException("救助申请创建失败");
        }
    }

    @Override
    public HelpRequest updateHelpRequest(HelpRequest helpRequest, Long userId) {
        log.info("更新救助申请: requestId={}, userId={}", helpRequest.getId(), userId);
        
        if (helpRequest.getId() == null || helpRequest.getId() <= 0) {
            throw new RuntimeException("申请ID不能为空");
        }
        
        // 检查申请是否存在
        HelpRequest existingRequest = helpRequestMapper.selectById(helpRequest.getId());
        if (existingRequest == null) {
            throw new RuntimeException("申请记录不存在");
        }
        
        // 检查权限
        if (!hasRequestPermission(helpRequest.getId(), userId)) {
            throw new RuntimeException("没有权限修改此申请");
        }
        
        // 只有待审核状态的申请可以修改
        if (!HelpRequest.Status.PENDING.getCode().equals(existingRequest.getStatus())) {
            throw new RuntimeException("只有待审核状态的申请可以修改");
        }
        
        // 业务校验
        validateHelpRequestForUpdate(helpRequest);
        
        // 不允许修改某些字段
        helpRequest.setUserId(null);
        helpRequest.setStatus(null);
        helpRequest.setAdminId(null);
        helpRequest.setAdminResponse(null);
        
        int result = helpRequestMapper.updateById(helpRequest);
        if (result > 0) {
            log.info("救助申请更新成功: {}", helpRequest.getId());
            return findById(helpRequest.getId());
        } else {
            throw new RuntimeException("救助申请更新失败");
        }
    }

    @Override
    public boolean reviewHelpRequest(Long requestId, String status, String adminResponse, Long adminId) {
        log.info("审核救助申请: requestId={}, status={}, adminId={}", requestId, status, adminId);
        
        if (requestId == null || requestId <= 0) {
            throw new RuntimeException("申请ID不能为空");
        }
        
        if (!StringUtils.hasText(status)) {
            throw new RuntimeException("审核状态不能为空");
        }
        
        if (adminId == null || adminId <= 0) {
            throw new RuntimeException("管理员ID不能为空");
        }
        
        // 验证状态值
        if (!isValidRequestStatus(status)) {
            throw new RuntimeException("无效的申请状态");
        }
        
        // 检查管理员权限
        User admin = userService.findById(adminId);
        if (admin == null || !User.Role.ADMIN.getCode().equals(admin.getRole())) {
            throw new RuntimeException("只有管理员可以审核申请");
        }
        
        HelpRequest helpRequest = helpRequestMapper.selectById(requestId);
        if (helpRequest == null) {
            throw new RuntimeException("申请记录不存在");
        }
        
        // 更新申请状态
        int result = helpRequestMapper.updateRequestStatus(requestId, status, adminResponse, adminId);
        if (result > 0) {
            log.info("救助申请审核成功: requestId={}, status={}", requestId, status);

            // 如果申请通过，自动创建救助项目
            if (HelpRequest.Status.APPROVED.getCode().equals(status)) {
                try {
                    createProjectFromApprovedRequest(helpRequest, adminId);
                    log.info("已为通过的申请自动创建救助项目: requestId={}", requestId);
                } catch (Exception e) {
                    log.error("为申请创建项目失败: requestId={}, error={}", requestId, e.getMessage());
                    // 不抛出异常，避免影响审核流程
                }
            }

            // 发送状态通知
            sendStatusNotification(requestId, status);

            return true;
        } else {
            throw new RuntimeException("救助申请审核失败");
        }
    }

    @Override
    @Transactional(readOnly = true)
    public HelpRequest findById(Long id) {
        if (id == null || id <= 0) {
            return null;
        }
        return helpRequestMapper.selectById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HelpRequest> findByUserId(Long userId) {
        if (userId == null || userId <= 0) {
            throw new RuntimeException("用户ID不能为空");
        }
        return helpRequestMapper.findByUserId(userId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HelpRequest> findByStatus(String status) {
        if (!StringUtils.hasText(status)) {
            throw new RuntimeException("状态不能为空");
        }
        return helpRequestMapper.findByStatus(status);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HelpRequest> findByCategoryAndStatus(String category, String status) {
        if (!StringUtils.hasText(category)) {
            throw new RuntimeException("分类不能为空");
        }
        return helpRequestMapper.findByCategoryAndStatus(category, status);
    }

    @Override
    @Transactional(readOnly = true)
    public IPage<HelpRequest> findHelpRequestsWithConditions(Page<HelpRequest> page, Long userId, 
                                                           String category, String status, String keyword) {
        return helpRequestMapper.findHelpRequestsWithConditions(page, userId, category, status, keyword);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HelpRequest> findPendingRequests(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        return helpRequestMapper.findPendingRequests(limit);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HelpRequest> findRecentApprovedRequests(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        return helpRequestMapper.findRecentApprovedRequests(limit);
    }

    @Override
    @Transactional(readOnly = true)
    public Long countHelpRequests(String category, String status, LocalDateTime startTime, LocalDateTime endTime) {
        Long count = helpRequestMapper.countHelpRequests(category, status, startTime, endTime);
        return count != null ? count : 0L;
    }

    @Override
    @Transactional(readOnly = true)
    public BigDecimal sumAmountNeeded(String status) {
        BigDecimal amount = helpRequestMapper.sumAmountNeeded(status);
        return amount != null ? amount : BigDecimal.ZERO;
    }

    @Override
    @Transactional(readOnly = true)
    public Long countRequestsByUser(Long userId) {
        if (userId == null || userId <= 0) {
            return 0L;
        }
        Long count = helpRequestMapper.countRequestsByUser(userId);
        return count != null ? count : 0L;
    }

    @Override
    @Transactional(readOnly = true)
    public Long countRequestsByAdmin(Long adminId) {
        if (adminId == null || adminId <= 0) {
            return 0L;
        }
        Long count = helpRequestMapper.countRequestsByAdmin(adminId);
        return count != null ? count : 0L;
    }

    @Override
    public boolean deleteHelpRequest(Long requestId, Long userId) {
        log.info("删除救助申请: requestId={}, userId={}", requestId, userId);
        
        if (requestId == null || requestId <= 0) {
            throw new RuntimeException("申请ID不能为空");
        }
        
        if (userId == null || userId <= 0) {
            throw new RuntimeException("用户ID不能为空");
        }
        
        HelpRequest helpRequest = helpRequestMapper.selectById(requestId);
        if (helpRequest == null) {
            throw new RuntimeException("申请记录不存在");
        }
        
        // 检查权限
        if (!hasRequestPermission(requestId, userId)) {
            throw new RuntimeException("没有权限删除此申请");
        }
        
        // 只有待审核状态的申请可以删除
        if (!HelpRequest.Status.PENDING.getCode().equals(helpRequest.getStatus())) {
            throw new RuntimeException("只有待审核状态的申请可以删除");
        }
        
        int result = helpRequestMapper.deleteById(requestId);
        if (result > 0) {
            log.info("救助申请删除成功: requestId={}", requestId);
            return true;
        } else {
            throw new RuntimeException("救助申请删除失败");
        }
    }

    @Override
    public boolean batchReviewHelpRequests(List<Long> requestIds, String status, String adminResponse, Long adminId) {
        log.info("批量审核救助申请: requestIds={}, status={}, adminId={}", requestIds, status, adminId);
        
        if (requestIds == null || requestIds.isEmpty()) {
            throw new RuntimeException("申请ID列表不能为空");
        }
        
        if (!StringUtils.hasText(status)) {
            throw new RuntimeException("审核状态不能为空");
        }
        
        if (adminId == null || adminId <= 0) {
            throw new RuntimeException("管理员ID不能为空");
        }
        
        // 检查管理员权限
        User admin = userService.findById(adminId);
        if (admin == null || !User.Role.ADMIN.getCode().equals(admin.getRole())) {
            throw new RuntimeException("只有管理员可以审核申请");
        }
        
        int successCount = 0;
        for (Long requestId : requestIds) {
            try {
                if (reviewHelpRequest(requestId, status, adminResponse, adminId)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量审核失败: requestId={}, error={}", requestId, e.getMessage());
            }
        }
        
        log.info("批量审核完成: 成功{}个，总共{}个", successCount, requestIds.size());
        return successCount > 0;
    }

    @Override
    @Transactional(readOnly = true)
    public List<Object> getRequestStatsByCategory() {
        return helpRequestMapper.getRequestStatsByCategory();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Object> getRequestStatsByStatus() {
        return helpRequestMapper.getRequestStatsByStatus();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Object> getDailyRequestStats() {
        return helpRequestMapper.getDailyRequestStats();
    }

    @Override
    @Transactional(readOnly = true)
    public Object getRequestOverviewStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 总申请数
        Long totalRequests = countHelpRequests(null, null, null, null);
        stats.put("totalRequests", totalRequests);
        
        // 待审核申请数
        Long pendingRequests = countHelpRequests(null, HelpRequest.Status.PENDING.getCode(), null, null);
        stats.put("pendingRequests", pendingRequests);
        
        // 已通过申请数
        Long approvedRequests = countHelpRequests(null, HelpRequest.Status.APPROVED.getCode(), null, null);
        stats.put("approvedRequests", approvedRequests);
        
        // 已拒绝申请数
        Long rejectedRequests = countHelpRequests(null, HelpRequest.Status.REJECTED.getCode(), null, null);
        stats.put("rejectedRequests", rejectedRequests);
        
        // 处理中申请数
        Long processingRequests = countHelpRequests(null, HelpRequest.Status.PROCESSING.getCode(), null, null);
        stats.put("processingRequests", processingRequests);
        
        // 总申请金额
        BigDecimal totalAmount = sumAmountNeeded(null);
        stats.put("totalAmount", totalAmount);
        
        // 已通过申请金额
        BigDecimal approvedAmount = sumAmountNeeded(HelpRequest.Status.APPROVED.getCode());
        stats.put("approvedAmount", approvedAmount);
        
        // 今日申请统计
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        LocalDateTime todayEnd = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
        
        Long todayRequests = countHelpRequests(null, null, todayStart, todayEnd);
        stats.put("todayRequests", todayRequests);
        
        return stats;
    }

    @Override
    @Transactional(readOnly = true)
    public Object getProcessEfficiencyStats(Long adminId) {
        return helpRequestMapper.getProcessEfficiencyStats(adminId);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canUserApplyForHelp(Long userId, String category) {
        if (userId == null || userId <= 0 || !StringUtils.hasText(category)) {
            return false;
        }
        
        // 检查用户是否存在且状态正常
        User user = userService.findById(userId);
        if (user == null || user.getStatus() != User.Status.NORMAL.getCode()) {
            return false;
        }
        
        // 检查用户是否有过多的待审核申请
        List<HelpRequest> pendingRequests = helpRequestMapper.findByUserId(userId);
        long pendingCount = pendingRequests.stream()
                .filter(r -> HelpRequest.Status.PENDING.getCode().equals(r.getStatus()))
                .count();
        
        // 限制用户同时只能有3个待审核申请
        if (pendingCount >= 3) {
            return false;
        }
        
        // 检查分类是否有效
        if (!isValidRequestCategory(category)) {
            return false;
        }
        
        return true;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasRequestPermission(Long requestId, Long userId) {
        if (requestId == null || requestId <= 0 || userId == null || userId <= 0) {
            return false;
        }
        
        HelpRequest helpRequest = helpRequestMapper.selectById(requestId);
        if (helpRequest == null) {
            return false;
        }
        
        // 用户只能操作自己的申请
        return helpRequest.getUserId().equals(userId);
    }

    @Override
    @Transactional(readOnly = true)
    public Object getUserRequestStats(Long userId) {
        if (userId == null || userId <= 0) {
            throw new RuntimeException("用户ID不能为空");
        }
        
        Map<String, Object> stats = new HashMap<>();
        
        List<HelpRequest> requests = findByUserId(userId);
        
        // 总申请数
        stats.put("totalRequests", requests.size());
        
        // 各状态申请数
        long pendingCount = requests.stream()
                .filter(r -> HelpRequest.Status.PENDING.getCode().equals(r.getStatus()))
                .count();
        long approvedCount = requests.stream()
                .filter(r -> HelpRequest.Status.APPROVED.getCode().equals(r.getStatus()))
                .count();
        long rejectedCount = requests.stream()
                .filter(r -> HelpRequest.Status.REJECTED.getCode().equals(r.getStatus()))
                .count();
        long processingCount = requests.stream()
                .filter(r -> HelpRequest.Status.PROCESSING.getCode().equals(r.getStatus()))
                .count();
        
        stats.put("pendingCount", pendingCount);
        stats.put("approvedCount", approvedCount);
        stats.put("rejectedCount", rejectedCount);
        stats.put("processingCount", processingCount);
        
        // 总申请金额
        BigDecimal totalAmount = requests.stream()
                .filter(r -> r.getAmountNeeded() != null)
                .map(HelpRequest::getAmountNeeded)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        stats.put("totalAmount", totalAmount);
        
        // 最近申请时间
        requests.stream()
                .map(HelpRequest::getCreateTime)
                .max(LocalDateTime::compareTo)
                .ifPresent(lastTime -> stats.put("lastRequestTime", lastTime));
        
        return stats;
    }

    @Override
    public Long autoAssignReviewer(Long requestId) {
        log.info("自动分配审核员: requestId={}", requestId);
        
        if (requestId == null || requestId <= 0) {
            return null;
        }
        
        // 获取所有管理员
        List<User> admins = userService.getAllAdmins();
        if (admins.isEmpty()) {
            log.warn("没有可用的管理员进行分配");
            return null;
        }
        
        // 简单的负载均衡：选择处理申请数量最少的管理员
        User selectedAdmin = admins.stream()
                .min((a1, a2) -> {
                    Long count1 = countRequestsByAdmin(a1.getId());
                    Long count2 = countRequestsByAdmin(a2.getId());
                    return count1.compareTo(count2);
                })
                .orElse(admins.get(0));
        
        log.info("自动分配审核员成功: requestId={}, adminId={}", requestId, selectedAdmin.getId());
        return selectedAdmin.getId();
    }

    @Override
    public boolean sendStatusNotification(Long requestId, String status) {
        log.info("发送申请状态通知: requestId={}, status={}", requestId, status);
        
        if (requestId == null || requestId <= 0) {
            return false;
        }
        
        HelpRequest helpRequest = helpRequestMapper.selectById(requestId);
        if (helpRequest == null) {
            return false;
        }
        
        // 这里可以实现具体的通知逻辑，比如发送邮件、短信等
        // 目前只是记录日志
        log.info("状态通知发送成功: requestId={}, userId={}, status={}", 
                requestId, helpRequest.getUserId(), status);
        
        return true;
    }

    @Override
    @Transactional(readOnly = true)
    public Object generateRequestReport(Long requestId) {
        if (requestId == null || requestId <= 0) {
            throw new RuntimeException("申请ID不能为空");
        }
        
        HelpRequest helpRequest = helpRequestMapper.selectById(requestId);
        if (helpRequest == null) {
            throw new RuntimeException("申请记录不存在");
        }
        
        Map<String, Object> report = new HashMap<>();
        report.put("request", helpRequest);
        
        // 获取申请人信息
        User user = userService.findById(helpRequest.getUserId());
        if (user != null) {
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("username", user.getUsername());
            userInfo.put("realName", user.getRealName());
            userInfo.put("phone", user.getPhone());
            userInfo.put("email", user.getEmail());
            report.put("applicant", userInfo);
        }
        
        // 获取审核人信息
        if (helpRequest.getAdminId() != null) {
            User admin = userService.findById(helpRequest.getAdminId());
            if (admin != null) {
                Map<String, Object> adminInfo = new HashMap<>();
                adminInfo.put("username", admin.getUsername());
                adminInfo.put("realName", admin.getRealName());
                report.put("reviewer", adminInfo);
            }
        }
        
        // 生成报告编号
        report.put("reportNo", "RPT" + requestId + System.currentTimeMillis());
        report.put("generateTime", LocalDateTime.now());
        
        return report;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean needsAdditionalDocuments(Long requestId) {
        if (requestId == null || requestId <= 0) {
            return false;
        }
        
        HelpRequest helpRequest = helpRequestMapper.selectById(requestId);
        if (helpRequest == null) {
            return false;
        }
        
        // 检查是否缺少必要的证明材料
        if (!StringUtils.hasText(helpRequest.getProofImages()) || 
            "[]".equals(helpRequest.getProofImages().trim())) {
            return true;
        }
        
        // 根据申请类型检查特定材料
        if (HelpRequest.Category.MEDICAL.getCode().equals(helpRequest.getCategory())) {
            // 医疗类申请需要医疗证明
            return !helpRequest.getDescription().toLowerCase().contains("医院") &&
                   !helpRequest.getDescription().toLowerCase().contains("诊断");
        }
        
        return false;
    }

    @Override
    public boolean markAsUrgent(Long requestId, Long adminId) {
        log.info("标记申请为紧急: requestId={}, adminId={}", requestId, adminId);
        
        if (requestId == null || requestId <= 0) {
            throw new RuntimeException("申请ID不能为空");
        }
        
        if (adminId == null || adminId <= 0) {
            throw new RuntimeException("管理员ID不能为空");
        }
        
        // 检查管理员权限
        User admin = userService.findById(adminId);
        if (admin == null || !User.Role.ADMIN.getCode().equals(admin.getRole())) {
            throw new RuntimeException("只有管理员可以标记紧急申请");
        }
        
        // 这里可以添加紧急标记逻辑，比如在申请记录中添加紧急标记字段
        // 由于当前表结构没有相关字段，这里只是记录日志
        log.info("申请已标记为紧急: requestId={}", requestId);
        return true;
    }

    @Override
    @Transactional(readOnly = true)
    public List<HelpRequest> findSimilarRequests(Long requestId, Integer limit) {
        if (requestId == null || requestId <= 0) {
            throw new RuntimeException("申请ID不能为空");
        }
        
        if (limit == null || limit <= 0) {
            limit = 5;
        }
        
        HelpRequest helpRequest = helpRequestMapper.selectById(requestId);
        if (helpRequest == null) {
            throw new RuntimeException("申请记录不存在");
        }
        
        // 查找相同分类的申请
        List<HelpRequest> similarRequests = helpRequestMapper.findByCategoryAndStatus(
                helpRequest.getCategory(), null);
        
        // 排除当前申请，并限制数量
        return similarRequests.stream()
                .filter(r -> !r.getId().equals(requestId))
                .limit(limit)
                .toList();
    }

    /**
     * 验证救助申请创建信息
     */
    private void validateHelpRequestForCreate(HelpRequest helpRequest) {
        if (helpRequest == null) {
            throw new RuntimeException("申请信息不能为空");
        }
        
        if (helpRequest.getUserId() == null || helpRequest.getUserId() <= 0) {
            throw new RuntimeException("用户ID不能为空");
        }
        
        validateHelpRequestBasicInfo(helpRequest);
    }

    /**
     * 验证救助申请更新信息
     */
    private void validateHelpRequestForUpdate(HelpRequest helpRequest) {
        if (helpRequest == null) {
            throw new RuntimeException("申请信息不能为空");
        }
        
        validateHelpRequestBasicInfo(helpRequest);
    }

    /**
     * 验证救助申请基本信息
     */
    private void validateHelpRequestBasicInfo(HelpRequest helpRequest) {
        if (!StringUtils.hasText(helpRequest.getTitle())) {
            throw new RuntimeException("申请标题不能为空");
        }
        
        if (helpRequest.getTitle().length() > 200) {
            throw new RuntimeException("申请标题长度不能超过200字符");
        }
        
        if (!StringUtils.hasText(helpRequest.getDescription())) {
            throw new RuntimeException("申请描述不能为空");
        }
        
        if (helpRequest.getDescription().length() > 2000) {
            throw new RuntimeException("申请描述长度不能超过2000字符");
        }
        
        if (!StringUtils.hasText(helpRequest.getCategory())) {
            throw new RuntimeException("申请分类不能为空");
        }
        
        if (!isValidRequestCategory(helpRequest.getCategory())) {
            throw new RuntimeException("无效的申请分类");
        }
        
        if (!StringUtils.hasText(helpRequest.getContactPhone())) {
            throw new RuntimeException("联系电话不能为空");
        }
        
        if (helpRequest.getAmountNeeded() != null && helpRequest.getAmountNeeded().compareTo(BigDecimal.ZERO) < 0) {
            throw new RuntimeException("所需金额不能为负数");
        }
        
        if (helpRequest.getAmountNeeded() != null && 
            helpRequest.getAmountNeeded().compareTo(new BigDecimal("10000000")) > 0) {
            throw new RuntimeException("所需金额不能超过1000万");
        }
    }

    /**
     * 验证申请分类是否有效
     */
    private boolean isValidRequestCategory(String category) {
        for (HelpRequest.Category cat : HelpRequest.Category.values()) {
            if (cat.getCode().equals(category)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 验证申请状态是否有效
     */
    private boolean isValidRequestStatus(String status) {
        for (HelpRequest.Status stat : HelpRequest.Status.values()) {
            if (stat.getCode().equals(status)) {
                return true;
            }
        }
        return false;
    }

    @Override
    @Transactional(readOnly = true)
    public Object getHelpRequestOverviewStats() {
        Map<String, Object> stats = new HashMap<>();

        // 总申请数
        Long totalRequests = helpRequestMapper.countHelpRequests(null, null, null, null);
        stats.put("totalRequests", totalRequests);

        // 待审核申请数
        Long pendingRequests = helpRequestMapper.countHelpRequests(null, HelpRequest.Status.PENDING.getCode(), null, null);
        stats.put("pendingRequests", pendingRequests);

        // 已通过申请数
        Long approvedRequests = helpRequestMapper.countHelpRequests(null, HelpRequest.Status.APPROVED.getCode(), null, null);
        stats.put("approvedRequests", approvedRequests);

        // 已拒绝申请数
        Long rejectedRequests = helpRequestMapper.countHelpRequests(null, HelpRequest.Status.REJECTED.getCode(), null, null);
        stats.put("rejectedRequests", rejectedRequests);

        return stats;
    }

    @Override
    @Transactional(readOnly = true)
    public Long countPendingRequests() {
        return helpRequestMapper.countHelpRequests(null, HelpRequest.Status.PENDING.getCode(), null, null);
    }

    /**
     * 为通过审核的申请创建救助项目
     */
    private void createProjectFromApprovedRequest(HelpRequest helpRequest, Long adminId) {
        log.info("为通过的申请创建救助项目: requestId={}, title={}", helpRequest.getId(), helpRequest.getTitle());

        try {
            // 创建项目对象
            Project project = new Project();
            project.setTitle(helpRequest.getTitle());
            project.setDescription(helpRequest.getDescription());
            project.setCategory(helpRequest.getCategory());
            project.setTargetAmount(helpRequest.getAmountNeeded());
            project.setCurrentAmount(BigDecimal.ZERO);
            project.setContactInfo(buildContactInfo(helpRequest));
            project.setStatus(Project.Status.ACTIVE.getCode());
            project.setAdminId(adminId);

            // 调用项目服务创建项目
            Project createdProject = projectService.createProject(project, adminId);

            log.info("救助项目创建成功: projectId={}, title={}, 来源申请ID={}",
                    createdProject.getId(), createdProject.getTitle(), helpRequest.getId());

        } catch (Exception e) {
            log.error("为申请创建项目失败: requestId={}, error={}", helpRequest.getId(), e.getMessage(), e);
            throw new RuntimeException("创建救助项目失败: " + e.getMessage());
        }
    }

    /**
     * 构建项目联系信息
     */
    private String buildContactInfo(HelpRequest helpRequest) {
        StringBuilder contactInfo = new StringBuilder();

        if (StringUtils.hasText(helpRequest.getContactPhone())) {
            contactInfo.append("联系电话: ").append(helpRequest.getContactPhone());
        }

        if (StringUtils.hasText(helpRequest.getContactAddress())) {
            if (contactInfo.length() > 0) {
                contactInfo.append("\n");
            }
            contactInfo.append("联系地址: ").append(helpRequest.getContactAddress());
        }

        return contactInfo.toString();
    }
}
