<template>
  <div class="project-detail-page">
    <div class="container" v-loading="loading">
      <!-- 项目头部信息 -->
      <div class="project-header">
        <div class="project-header-content">
          <div class="project-image">
            <!-- 轮播图 -->
            <el-carousel v-if="getProjectImages().length > 0" :height="'400px'" :autoplay="true" :interval="5000"
              indicator-position="outside" arrow="hover">
              <el-carousel-item v-for="(image, index) in getProjectImages()" :key="index">
                <img :src="image" :alt="`${project.title} - 图片${index + 1}`" />
              </el-carousel-item>
            </el-carousel>

            <!-- 无图片时的占位图 -->
            <div v-else class="no-image-placeholder">
              <el-empty description="暂无项目图片">
                <template #image>
                  <el-icon class="empty-icon">
                    <Picture />
                  </el-icon>
                </template>
              </el-empty>
            </div>

            <!-- 项目状态标签 -->
            <div class="project-status" :class="project.status">
              {{ getStatusText(project.status) }}
            </div>

            <!-- 图片数量指示器 -->
            <div v-if="getProjectImages().length > 1" class="image-count">
              <el-icon>
                <Picture />
              </el-icon>
              {{ getProjectImages().length }} 张图片
            </div>
          </div>

          <div class="project-info">
            <div class="project-category">
              <el-tag :type="getCategoryType(project.category)" size="large">
                {{ getCategoryText(project.category) }}
              </el-tag>
            </div>

            <h1 class="project-title">{{ project.title }}</h1>
            <p class="project-description">{{ project.description }}</p>

            <div class="project-meta">
              <div class="meta-item">
                <el-icon>
                  <Location />
                </el-icon>
                <span>{{ project.location || '全国' }}</span>
              </div>
              <div class="meta-item">
                <el-icon>
                  <Clock />
                </el-icon>
                <span>{{ formatDate(project.createTime) }}</span>
              </div>
              <div class="meta-item">
                <el-icon>
                  <User />
                </el-icon>
                <span>{{ project.donorsCount || 0 }} 人参与</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 项目进度和捐赠 -->
      <div class="project-progress-section">
        <div class="progress-card">
          <div class="progress-header">
            <h3>筹款进度</h3>
            <div class="progress-percentage">{{ getProgressPercentage(project) }}%</div>
          </div>

          <div class="progress-bar">
            <el-progress :percentage="getProgressPercentage(project)" :stroke-width="12" :show-text="false" />
          </div>

          <div class="progress-stats">
            <div class="stat-item">
              <div class="stat-value">¥{{ formatAmount(project.currentAmount) }}</div>
              <div class="stat-label">已筹金额</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">¥{{ formatAmount(project.targetAmount) }}</div>
              <div class="stat-label">目标金额</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ project.donorsCount || 0 }}</div>
              <div class="stat-label">参与人数</div>
            </div>
          </div>

          <div class="donate-actions">
            <el-button type="primary" size="large" @click="showDonateDialog = true">
              立即捐赠
            </el-button>
            <el-button size="large" @click="shareProject">
              分享项目
            </el-button>
          </div>
        </div>
      </div>

      <!-- 项目详情内容 -->
      <div class="project-content-section">
        <div class="content-tabs">
          <el-tabs v-model="activeTab">
            <el-tab-pane label="项目详情" name="detail">
              <div class="project-detail-content">
                <div v-html="project.content || '暂无详细内容'"></div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="捐赠记录" name="donations">
              <div class="donations-list">
                <div v-if="donations.length === 0" class="empty-state">
                  <el-icon class="empty-icon">
                    <DocumentRemove />
                  </el-icon>
                  <p>暂无捐赠记录</p>
                </div>
                <div v-else>
                  <div v-for="donation in donations" :key="donation.id" class="donation-item">
                    <div class="donor-info">
                      <div class="donor-avatar">
                        <el-icon>
                          <User />
                        </el-icon>
                      </div>
                      <div class="donor-details">
                        <div class="donor-name">{{ donation.donorName || '匿名用户' }}</div>
                        <div class="donation-time">{{ formatDate(donation.createTime) }}</div>
                        <div v-if="donation.message" class="donation-message">{{ donation.message }}</div>
                      </div>
                    </div>
                    <div class="donation-amount">¥{{ formatAmount(donation.amount) }}</div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <!-- 捐赠对话框 -->
      <el-dialog v-model="showDonateDialog" title="爱心捐赠" width="500px">
        <div class="donate-form">
          <div class="project-summary">
            <h4>{{ project.title }}</h4>
            <p>目标金额：¥{{ formatAmount(project.targetAmount) }}</p>
          </div>

          <el-form :model="donateForm" label-width="80px">
            <el-form-item label="捐赠金额" required>
              <el-input-number v-model="donateForm.amount" :min="1" :max="1000000" placeholder="请输入捐赠金额"
                style="width: 100%" />
            </el-form-item>

            <el-form-item label="支付方式" required>
              <el-select v-model="donateForm.paymentMethod" placeholder="请选择支付方式" style="width: 100%">
                <el-option
                  v-for="method in paymentMethods"
                  :key="method.value"
                  :label="method.label"
                  :value="method.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="留言">
              <el-input v-model="donateForm.message" type="textarea" :rows="3" placeholder="留下您的爱心寄语（可选）" />
            </el-form-item>

            <el-form-item label="匿名捐赠">
              <el-switch v-model="donateForm.anonymous" />
            </el-form-item>
          </el-form>

          <div class="donate-actions">
            <el-button @click="showDonateDialog = false" :disabled="donating">取消</el-button>
            <el-button type="primary" @click="handleDonate" :loading="donating">确认捐赠</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { getProjectDetail } from '@/api/project'
  import { createDonation, getProjectDonations } from '@/api/donation'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Picture } from '@element-plus/icons-vue'
  import { useUserStore } from '@/stores/user'

  const route = useRoute()
  const router = useRouter()
  const userStore = useUserStore()

  // 响应式数据
  const loading = ref(false)
  const donating = ref(false)
  const project = ref({})
  const donations = ref([])
  const activeTab = ref('detail')
  const showDonateDialog = ref(false)

  // 捐赠表单
  const donateForm = reactive({
    amount: null,
    message: '',
    anonymous: false,
    paymentMethod: 'alipay'
  })

  // 支付方式选项
  const paymentMethods = [
    { value: 'alipay', label: '支付宝' },
    { value: 'wechat', label: '微信支付' },
    { value: 'bank', label: '银行转账' }
  ]

  // 加载项目详情
  const loadProjectDetail = async () => {
    try {
      loading.value = true
      const projectId = route.params.id
      const response = await getProjectDetail(projectId)
      project.value = response || {}

      // 加载捐赠记录
      await loadDonations()
    } catch (error) {
      console.error('加载项目详情失败:', error)
      ElMessage.error('加载项目详情失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 加载捐赠记录
  const loadDonations = async () => {
    try {
      const projectId = route.params.id
      const response = await getProjectDonations(projectId, {
        current: 1,
        size: 20 // 显示最近20条捐赠记录
      })

      console.log('捐赠记录响应:', response)

      if (response && response.records) {
        // 转换数据格式以适配前端显示
        donations.value = response.records.map(donation => ({
          id: donation.id,
          donorName: donation.isAnonymous ? '匿名用户' : (donation.realName || donation.username || '爱心人士'),
          amount: donation.amount,
          createTime: donation.donationTime,
          message: donation.message
        }))
      } else {
        donations.value = []
      }
    } catch (error) {
      console.error('加载捐赠记录失败:', error)
      donations.value = []
    }
  }

  // 处理捐赠
  const handleDonate = async () => {
    // 检查用户是否登录
    if (!userStore.isLoggedIn) {
      ElMessage.warning('请先登录后再进行捐赠')
      router.push('/login')
      return
    }

    // 表单验证
    if (!donateForm.amount || donateForm.amount <= 0) {
      ElMessage.warning('请输入有效的捐赠金额')
      return
    }

    if (!donateForm.paymentMethod) {
      ElMessage.warning('请选择支付方式')
      return
    }

    try {
      // 确认捐赠
      await ElMessageBox.confirm(
        `您确定要捐赠 ¥${donateForm.amount} 元吗？`,
        '确认捐赠',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )

      donating.value = true

      // 准备捐赠数据
      const donationData = {
        projectId: parseInt(route.params.id),
        amount: donateForm.amount,
        paymentMethod: donateForm.paymentMethod,
        message: donateForm.message || null,
        isAnonymous: donateForm.anonymous
      }

      console.log('提交捐赠数据:', donationData)

      // 调用捐赠API
      const response = await createDonation(donationData)
      console.log('捐赠响应:', response)

      ElMessage.success('捐赠提交成功！感谢您的爱心')
      showDonateDialog.value = false

      // 重置表单
      resetDonateForm()

      // 重新加载项目详情以更新筹款进度
      await loadProjectDetail()

      // 重新加载捐赠记录
      await loadDonations()

    } catch (error) {
      if (error === 'cancel') {
        // 用户取消了操作
        return
      }
      console.error('捐赠失败:', error)
      ElMessage.error('捐赠失败: ' + (error.response?.data?.message || error.message || '网络错误'))
    } finally {
      donating.value = false
    }
  }

  // 重置捐赠表单
  const resetDonateForm = () => {
    donateForm.amount = null
    donateForm.message = ''
    donateForm.anonymous = false
    donateForm.paymentMethod = 'alipay'
  }

  // 分享项目
  const shareProject = () => {
    if (navigator.share) {
      navigator.share({
        title: project.value.title,
        text: project.value.description,
        url: window.location.href
      })
    } else {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(window.location.href)
      ElMessage.success('项目链接已复制到剪贴板')
    }
  }

  // 工具函数
  const getProgressPercentage = (project) => {
    if (!project.targetAmount || project.targetAmount === 0) return 0
    return Math.min(Math.round((project.currentAmount / project.targetAmount) * 100), 100)
  }

  const formatAmount = (amount) => {
    if (!amount) return '0'
    return amount.toLocaleString()
  }

  const formatDate = (dateString) => {
    if (!dateString) return ''
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  const getStatusText = (status) => {
    const statusMap = {
      active: '进行中',
      completed: '已完成',
      paused: '暂停'
    }
    return statusMap[status] || '未知'
  }

  const getCategoryText = (category) => {
    const categoryMap = {
      medical: '医疗救助',
      education: '教育助学',
      disaster: '灾难救援',
      poverty: '扶贫助困',
      environment: '环境保护'
    }
    return categoryMap[category] || '其他'
  }

  const getCategoryType = (category) => {
    const typeMap = {
      medical: 'danger',
      education: 'primary',
      disaster: 'warning',
      poverty: 'success',
      environment: 'info'
    }
    return typeMap[category] || ''
  }

  // 获取项目图片数组
  const getProjectImages = () => {
    // 如果有images数组，返回所有图片
    if (project.value.images && project.value.images.length > 0) {
      return project.value.images
    }

    // 如果有imageUrl字段，包装成数组
    if (project.value.imageUrl) {
      return [project.value.imageUrl]
    }

    // 如果有coverImage字段，包装成数组
    if (project.value.coverImage) {
      return [project.value.coverImage]
    }

    // 返回空数组，让组件显示占位图
    return []
  }

  // 页面加载时获取数据
  onMounted(() => {
    loadProjectDetail()
  })
</script>

<style scoped>
  .project-detail-page {
    min-height: 100vh;
    background: #F7FAFC;
    padding: 32px 0;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }

  /* 项目头部 */
  .project-header {
    background: #FFFFFF;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    margin-bottom: 32px;
  }

  .project-header-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 32px;
    padding: 32px;
  }

  .project-image {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .project-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    display: block;
  }

  /* 轮播图样式 */
  .project-image :deep(.el-carousel) {
    border-radius: 12px;
    overflow: hidden;
  }

  .project-image :deep(.el-carousel__item) {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
  }

  .project-image :deep(.el-carousel__item img) {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .project-image :deep(.el-carousel__indicators) {
    bottom: 10px;
  }

  .project-image :deep(.el-carousel__indicator) {
    background: rgba(255, 255, 255, 0.3);
  }

  .project-image :deep(.el-carousel__indicator.is-active) {
    background: rgba(255, 255, 255, 0.8);
  }

  .project-image :deep(.el-carousel__arrow) {
    background: rgba(0, 0, 0, 0.5);
    border: none;
    color: white;
  }

  .project-image :deep(.el-carousel__arrow:hover) {
    background: rgba(0, 0, 0, 0.7);
  }

  /* 无图片占位图 */
  .no-image-placeholder {
    width: 100%;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f9f9f9;
    border: 2px dashed #ddd;
    border-radius: 12px;
  }

  .no-image-placeholder .empty-icon {
    font-size: 64px;
    color: #ccc;
  }

  .no-image-placeholder :deep(.el-empty) {
    padding: 40px 20px;
  }

  .no-image-placeholder :deep(.el-empty__description) {
    color: #999;
    font-size: 16px;
  }

  .project-status {
    position: absolute;
    top: 16px;
    right: 16px;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 500;
    color: white;
    backdrop-filter: blur(4px);
  }

  .project-status.active {
    background: rgba(56, 161, 105, 0.9);
  }

  .project-status.completed {
    background: rgba(113, 128, 150, 0.9);
  }

  .project-status.paused {
    background: rgba(214, 158, 46, 0.9);
  }

  /* 图片数量指示器 */
  .image-count {
    position: absolute;
    bottom: 16px;
    left: 16px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .image-count .el-icon {
    font-size: 14px;
  }

  .project-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .project-category {
    margin-bottom: 16px;
  }

  .project-title {
    font-size: 32px;
    font-weight: bold;
    color: #2D3748;
    margin-bottom: 16px;
    line-height: 1.3;
  }

  .project-description {
    font-size: 16px;
    color: #718096;
    line-height: 1.6;
    margin-bottom: 24px;
  }

  .project-meta {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;
  }

  .meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #718096;
    font-size: 14px;
  }

  .meta-item .el-icon {
    font-size: 16px;
    color: #E53E3E;
  }

  /* 项目进度区域 */
  .project-progress-section {
    margin-bottom: 32px;
  }

  .progress-card {
    background: #FFFFFF;
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 2px solid #FED7D7;
  }

  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
  }

  .progress-header h3 {
    font-size: 24px;
    font-weight: bold;
    color: #2D3748;
    margin: 0;
  }

  .progress-percentage {
    font-size: 32px;
    font-weight: bold;
    color: #E53E3E;
  }

  .progress-bar {
    margin-bottom: 24px;
  }

  .progress-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    margin-bottom: 32px;
  }

  .stat-item {
    text-align: center;
    padding: 16px;
    background: #FEF5F5;
    border-radius: 12px;
    border: 1px solid #FED7D7;
  }

  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #E53E3E;
    margin-bottom: 4px;
  }

  .stat-label {
    font-size: 14px;
    color: #718096;
  }

  .donate-actions {
    display: flex;
    gap: 16px;
  }

  .donate-actions .el-button {
    flex: 1;
  }

  /* 项目内容区域 */
  .project-content-section {
    background: #FFFFFF;
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    margin-bottom: 32px;
  }

  .project-detail-content {
    padding: 24px 0;
    line-height: 1.8;
    color: #2D3748;
  }

  /* 捐赠记录 */
  .donations-list {
    padding: 24px 0;
  }

  .donation-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #E2E8F0;
  }

  .donation-item:last-child {
    border-bottom: none;
  }

  .donor-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .donor-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #FED7D7;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #E53E3E;
  }

  .donor-name {
    font-weight: 500;
    color: #2D3748;
    margin-bottom: 4px;
  }

  .donation-time {
    font-size: 12px;
    color: #718096;
  }

  .donation-message {
    font-size: 12px;
    color: #4A5568;
    margin-top: 4px;
    font-style: italic;
  }

  .donation-amount {
    font-size: 18px;
    font-weight: bold;
    color: #E53E3E;
  }

  /* 空状态 */
  .empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
  }

  .empty-icon {
    font-size: 64px;
    color: #CBD5E0;
    margin-bottom: 16px;
  }

  /* 捐赠对话框 */
  .donate-form {
    padding: 16px 0;
  }

  .project-summary {
    background: #FEF5F5;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    border: 1px solid #FED7D7;
  }

  .project-summary h4 {
    color: #2D3748;
    margin-bottom: 8px;
  }

  .project-summary p {
    color: #718096;
    margin: 0;
  }

  .donate-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .project-header-content {
      grid-template-columns: 1fr;
      gap: 24px;
    }

    .project-title {
      font-size: 24px;
    }

    .progress-stats {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .donate-actions {
      flex-direction: column;
    }

    .meta-item {
      font-size: 12px;
    }
  }
</style>