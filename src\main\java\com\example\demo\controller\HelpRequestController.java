package com.example.demo.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.dto.ApiResponse;
import com.example.demo.dto.PageDTO;
import com.example.demo.dto.converter.DTOConverter;
import com.example.demo.dto.request.HelpRequestCreateDTO;
import com.example.demo.dto.request.HelpRequestReviewDTO;
import com.example.demo.dto.request.HelpRequestUpdateDTO;
import com.example.demo.dto.response.HelpRequestResponseDTO;
import com.example.demo.entity.HelpRequest;
import com.example.demo.entity.User;
import com.example.demo.service.HelpRequestService;
import com.example.demo.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 救助申请Controller
 * 提供救助申请提交、审核、查询等API
 */
@Slf4j
@RestController
@RequestMapping("/api/help-requests")
@Tag(name = "救助申请管理", description = "救助申请提交、审核、查询等API")
@Validated
public class HelpRequestController extends BaseController {

    @Autowired
    private HelpRequestService helpRequestService;

    @Autowired
    private UserService userService;

    @PostMapping
    @Operation(summary = "提交救助申请", description = "用户提交救助申请")
    public ApiResponse<HelpRequestResponseDTO> createHelpRequest(@Valid @RequestBody HelpRequestCreateDTO createDTO) {
        try {
            Long userId = getCurrentUserId();
            logOperation("提交救助申请", userId, createDTO.getTitle(), createDTO.getCategory());
            
            // 创建申请实体
            HelpRequest helpRequest = new HelpRequest();
            helpRequest.setUserId(userId);
            helpRequest.setTitle(createDTO.getTitle());
            helpRequest.setDescription(createDTO.getDescription());
            helpRequest.setCategory(createDTO.getCategory());
            helpRequest.setAmountNeeded(createDTO.getAmountNeeded());
            helpRequest.setContactPhone(createDTO.getContactPhone());
            helpRequest.setContactAddress(createDTO.getContactAddress());
            
            // 处理证明材料图片
            if (createDTO.getProofImages() != null && !createDTO.getProofImages().isEmpty()) {
                String imagesJson = "[\"" + String.join("\",\"", createDTO.getProofImages()) + "\"]";
                helpRequest.setProofImages(imagesJson);
            }
            
            // 创建申请
            HelpRequest createdRequest = helpRequestService.createHelpRequest(helpRequest);
            HelpRequestResponseDTO responseDTO = convertToHelpRequestResponseDTO(createdRequest);
            
            return success("申请提交成功", responseDTO);
        } catch (Exception e) {
            log.error("提交救助申请失败", e);
            return error("申请提交失败：" + e.getMessage());
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新救助申请", description = "用户更新自己的救助申请（仅限待审核状态）")
    public ApiResponse<HelpRequestResponseDTO> updateHelpRequest(
            @Parameter(description = "申请ID") @PathVariable @NotNull Long id,
            @Valid @RequestBody HelpRequestUpdateDTO updateDTO) {
        try {
            Long userId = getCurrentUserId();
            logOperation("更新救助申请", userId, id, updateDTO.getTitle());
            
            // 设置申请ID
            updateDTO.setId(id);
            
            // 创建更新实体
            HelpRequest helpRequest = new HelpRequest();
            helpRequest.setId(id);
            helpRequest.setTitle(updateDTO.getTitle());
            helpRequest.setDescription(updateDTO.getDescription());
            helpRequest.setAmountNeeded(updateDTO.getAmountNeeded());
            helpRequest.setContactPhone(updateDTO.getContactPhone());
            helpRequest.setContactAddress(updateDTO.getContactAddress());
            
            // 处理证明材料图片
            if (updateDTO.getProofImages() != null && !updateDTO.getProofImages().isEmpty()) {
                String imagesJson = "[\"" + String.join("\",\"", updateDTO.getProofImages()) + "\"]";
                helpRequest.setProofImages(imagesJson);
            }
            
            // 更新申请
            HelpRequest updatedRequest = helpRequestService.updateHelpRequest(helpRequest, userId);
            HelpRequestResponseDTO responseDTO = convertToHelpRequestResponseDTO(updatedRequest);
            
            return success("申请更新成功", responseDTO);
        } catch (Exception e) {
            log.error("更新救助申请失败", e);
            return error("申请更新失败：" + e.getMessage());
        }
    }

    @PostMapping("/{id}/review")
    @Operation(summary = "审核救助申请", description = "管理员审核救助申请")
    public ApiResponse<Void> reviewHelpRequest(@Valid @RequestBody HelpRequestReviewDTO reviewDTO) {
        try {
            Long adminId = getCurrentUserId();
            logOperation("审核救助申请", adminId, reviewDTO.getRequestId(), reviewDTO.getStatus());
            
            // 权限检查：只有管理员可以审核申请
            if (!isAdmin()) {
                return forbidden("无权限审核申请");
            }
            
            boolean success = helpRequestService.reviewHelpRequest(
                    reviewDTO.getRequestId(), reviewDTO.getStatus(), 
                    reviewDTO.getAdminResponse(), adminId);
            
            if (success) {
                return success("审核完成", null);
            } else {
                return error("审核失败");
            }
        } catch (Exception e) {
            log.error("审核救助申请失败", e);
            return error("审核失败：" + e.getMessage());
        }
    }

    @GetMapping("/my-requests")
    @Operation(summary = "获取我的救助申请", description = "获取当前用户的救助申请（个人中心专用）")
    public ApiResponse<PageDTO<HelpRequestResponseDTO>> getMyHelpRequests(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "申请分类") @RequestParam(required = false) String category,
            @Parameter(description = "申请状态") @RequestParam(required = false) String status,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword) {
        try {
            Long currentUserId = getCurrentUserId();

            log.info("个人中心查询救助申请 - 当前用户ID: {}, 查询参数: current={}, size={}, category={}, status={}, keyword={}",
                    currentUserId, current, size, category, status, keyword);

            // 个人中心只能查看自己的申请，无论是否为管理员
            logOperation("个人中心查询救助申请", current, size, currentUserId, category, status, keyword);

            Page<HelpRequest> page = new Page<>(current, size);
            var result = helpRequestService.findHelpRequestsWithConditions(
                    page, currentUserId, category, status, keyword);

            return convertPageResult(result, this::convertToHelpRequestResponseDTO);
        } catch (Exception e) {
            log.error("查询个人救助申请失败", e);
            return error("查询个人申请失败：" + e.getMessage());
        }
    }

    @GetMapping
    @Operation(summary = "分页查询救助申请", description = "分页查询救助申请列表，支持条件筛选")
    public ApiResponse<PageDTO<HelpRequestResponseDTO>> getHelpRequestList(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId,
            @Parameter(description = "申请分类") @RequestParam(required = false) String category,
            @Parameter(description = "申请状态") @RequestParam(required = false) String status,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword) {
        try {
            Long currentUserId = getCurrentUserId();
            
            // 如果不是管理员，只能查看自己的申请
            if (!isAdmin() && userId != null && !userId.equals(currentUserId)) {
                return forbidden("无权限查看其他用户的申请");
            }
            
            // 普通用户默认只查看自己的申请
            if (!isAdmin() && userId == null) {
                userId = currentUserId;
            }
            
            logOperation("分页查询救助申请", current, size, userId, category, status, keyword);
            
            Page<HelpRequest> page = new Page<>(current, size);
            var result = helpRequestService.findHelpRequestsWithConditions(
                    page, userId, category, status, keyword);
            
            return convertPageResult(result, this::convertToHelpRequestResponseDTO);
        } catch (Exception e) {
            log.error("查询救助申请列表失败", e);
            return error("查询申请列表失败：" + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取申请详情", description = "根据ID获取救助申请详情")
    public ApiResponse<HelpRequestResponseDTO> getHelpRequestById(
            @Parameter(description = "申请ID") @PathVariable @NotNull Long id) {
        try {
            Long userId = getCurrentUserId();
            logOperation("获取申请详情", userId, id);
            
            HelpRequest helpRequest = helpRequestService.findById(id);
            if (helpRequest == null) {
                return notFound("申请不存在");
            }
            
            // 权限检查：普通用户只能查看自己的申请
            if (!isAdmin() && !helpRequest.getUserId().equals(userId)) {
                return forbidden("无权限查看此申请");
            }
            
            HelpRequestResponseDTO responseDTO = convertToHelpRequestResponseDTO(helpRequest);
            return success(responseDTO);
        } catch (Exception e) {
            log.error("获取申请详情失败", e);
            return error("获取申请详情失败：" + e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除救助申请", description = "用户删除自己的救助申请（仅限待审核状态）")
    public ApiResponse<Void> deleteHelpRequest(
            @Parameter(description = "申请ID") @PathVariable @NotNull Long id) {
        try {
            Long userId = getCurrentUserId();
            logOperation("删除救助申请", userId, id);
            
            boolean success = helpRequestService.deleteHelpRequest(id, userId);
            if (success) {
                return success("申请删除成功", null);
            } else {
                return error("申请删除失败");
            }
        } catch (Exception e) {
            log.error("删除救助申请失败", e);
            return error("申请删除失败：" + e.getMessage());
        }
    }

    @GetMapping("/pending")
    @Operation(summary = "获取待审核申请", description = "管理员获取待审核的申请列表")
    public ApiResponse<List<HelpRequestResponseDTO>> getPendingRequests(
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        try {
            logOperation("获取待审核申请", limit);
            
            // 权限检查：只有管理员可以查看待审核申请
            if (!isAdmin()) {
                return forbidden("无权限查看待审核申请");
            }
            
            List<HelpRequest> requests = helpRequestService.findPendingRequests(limit);
            List<HelpRequestResponseDTO> responseList = convertList(requests, 
                    this::convertToHelpRequestResponseDTO);
            
            return success(responseList);
        } catch (Exception e) {
            log.error("获取待审核申请失败", e);
            return error("获取待审核申请失败：" + e.getMessage());
        }
    }

    @GetMapping("/recent-approved")
    @Operation(summary = "获取最近通过的申请", description = "获取最近通过审核的申请列表")
    public ApiResponse<List<HelpRequestResponseDTO>> getRecentApprovedRequests(
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        try {
            logOperation("获取最近通过的申请", limit);
            
            List<HelpRequest> requests = helpRequestService.findRecentApprovedRequests(limit);
            List<HelpRequestResponseDTO> responseList = convertList(requests, 
                    this::convertToHelpRequestResponseDTO);
            
            return success(responseList);
        } catch (Exception e) {
            log.error("获取最近通过的申请失败", e);
            return error("获取最近通过的申请失败：" + e.getMessage());
        }
    }

    @GetMapping("/stats")
    @Operation(summary = "获取申请统计信息", description = "获取救助申请相关的统计数据")
    public ApiResponse<Object> getHelpRequestStats() {
        try {
            logOperation("获取申请统计信息");
            
            Object stats = helpRequestService.getRequestOverviewStats();
            return success(stats);
        } catch (Exception e) {
            log.error("获取申请统计信息失败", e);
            return error("获取申请统计信息失败：" + e.getMessage());
        }
    }

    @GetMapping("/user/{userId}/stats")
    @Operation(summary = "获取用户申请统计", description = "获取指定用户的申请统计信息")
    public ApiResponse<Object> getUserRequestStats(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long userId) {
        try {
            Long currentUserId = getCurrentUserId();
            logOperation("获取用户申请统计", currentUserId, userId);
            
            // 权限检查：普通用户只能查看自己的统计
            if (!isAdmin() && !currentUserId.equals(userId)) {
                return forbidden("无权限查看其他用户的申请统计");
            }
            
            Object stats = helpRequestService.getUserRequestStats(userId);
            return success(stats);
        } catch (Exception e) {
            log.error("获取用户申请统计失败", e);
            return error("获取用户申请统计失败：" + e.getMessage());
        }
    }

    @GetMapping("/{id}/report")
    @Operation(summary = "生成申请报告", description = "管理员生成申请详细报告")
    public ApiResponse<Object> generateRequestReport(
            @Parameter(description = "申请ID") @PathVariable @NotNull Long id) {
        try {
            Long adminId = getCurrentUserId();
            logOperation("生成申请报告", adminId, id);
            
            // 权限检查：只有管理员可以生成报告
            if (!isAdmin()) {
                return forbidden("无权限生成报告");
            }
            
            Object report = helpRequestService.generateRequestReport(id);
            return success("报告生成成功", report);
        } catch (Exception e) {
            log.error("生成申请报告失败", e);
            return error("生成报告失败：" + e.getMessage());
        }
    }

    @PostMapping("/{id}/mark-urgent")
    @Operation(summary = "标记为紧急申请", description = "管理员标记申请为紧急处理")
    public ApiResponse<Void> markAsUrgent(
            @Parameter(description = "申请ID") @PathVariable @NotNull Long id) {
        try {
            Long adminId = getCurrentUserId();
            logOperation("标记为紧急申请", adminId, id);
            
            // 权限检查：只有管理员可以标记紧急
            if (!isAdmin()) {
                return forbidden("无权限标记紧急申请");
            }
            
            boolean success = helpRequestService.markAsUrgent(id, adminId);
            if (success) {
                return success("标记成功", null);
            } else {
                return error("标记失败");
            }
        } catch (Exception e) {
            log.error("标记紧急申请失败", e);
            return error("标记失败：" + e.getMessage());
        }
    }

    @GetMapping("/{id}/similar")
    @Operation(summary = "获取相似申请", description = "获取与指定申请相似的其他申请")
    public ApiResponse<List<HelpRequestResponseDTO>> getSimilarRequests(
            @Parameter(description = "申请ID") @PathVariable @NotNull Long id,
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "5") Integer limit) {
        try {
            logOperation("获取相似申请", id, limit);
            
            // 权限检查：只有管理员可以查看相似申请
            if (!isAdmin()) {
                return forbidden("无权限查看相似申请");
            }
            
            List<HelpRequest> requests = helpRequestService.findSimilarRequests(id, limit);
            List<HelpRequestResponseDTO> responseList = convertList(requests, 
                    this::convertToHelpRequestResponseDTO);
            
            return success(responseList);
        } catch (Exception e) {
            log.error("获取相似申请失败", e);
            return error("获取相似申请失败：" + e.getMessage());
        }
    }

    @GetMapping("/{id}/documents-check")
    @Operation(summary = "检查是否需要补充材料", description = "检查申请是否需要补充证明材料")
    public ApiResponse<Boolean> checkNeedsAdditionalDocuments(
            @Parameter(description = "申请ID") @PathVariable @NotNull Long id) {
        try {
            logOperation("检查是否需要补充材料", id);
            
            boolean needsDocuments = helpRequestService.needsAdditionalDocuments(id);
            return success(needsDocuments);
        } catch (Exception e) {
            log.error("检查补充材料失败", e);
            return error("检查失败：" + e.getMessage());
        }
    }

    /**
     * 转换HelpRequest实体为HelpRequestResponseDTO
     */
    private HelpRequestResponseDTO convertToHelpRequestResponseDTO(HelpRequest helpRequest) {
        HelpRequestResponseDTO dto = new HelpRequestResponseDTO();
        dto.setId(helpRequest.getId());
        dto.setUserId(helpRequest.getUserId());
        dto.setTitle(helpRequest.getTitle());
        dto.setDescription(helpRequest.getDescription());
        dto.setCategory(helpRequest.getCategory());
        dto.setCategoryDescription(DTOConverter.getHelpRequestCategoryDescription(helpRequest.getCategory()));
        dto.setAmountNeeded(helpRequest.getAmountNeeded());
        dto.setContactPhone(DTOConverter.maskPhone(helpRequest.getContactPhone()));
        dto.setContactAddress(helpRequest.getContactAddress());
        dto.setProofImages(DTOConverter.parseImageList(helpRequest.getProofImages()));
        dto.setStatus(helpRequest.getStatus());
        dto.setStatusDescription(DTOConverter.getHelpRequestStatusDescription(helpRequest.getStatus()));
        dto.setAdminResponse(helpRequest.getAdminResponse());
        dto.setAdminId(helpRequest.getAdminId());
        dto.setCreateTime(helpRequest.getCreateTime());
        dto.setUpdateTime(helpRequest.getUpdateTime());
        
        // 获取申请用户信息
        try {
            User user = userService.findById(helpRequest.getUserId());
            if (user != null) {
                dto.setUsername(user.getUsername());
                dto.setApplicantName(user.getRealName() != null ? user.getRealName() : user.getUsername());
            }
        } catch (Exception e) {
            log.warn("获取申请用户信息失败: {}", e.getMessage());
        }
        
        // 获取处理管理员信息
        if (helpRequest.getAdminId() != null) {
            try {
                User admin = userService.findById(helpRequest.getAdminId());
                if (admin != null) {
                    dto.setAdminName(admin.getRealName() != null ? admin.getRealName() : admin.getUsername());
                }
            } catch (Exception e) {
                log.warn("获取处理管理员信息失败: {}", e.getMessage());
            }
        }
        
        return dto;
    }
}
