package com.example.demo.exception;

/**
 * 用户不存在异常
 */
public class UserNotFoundException extends BusinessException {

    public UserNotFoundException() {
        super(ErrorCode.USER_NOT_FOUND);
    }

    public UserNotFoundException(String message) {
        super(ErrorCode.USER_NOT_FOUND, message);
    }

    public UserNotFoundException(Long userId) {
        super(ErrorCode.USER_NOT_FOUND, "用户不存在，ID: " + userId);
    }

    public UserNotFoundException(String field, Object value) {
        super(ErrorCode.USER_NOT_FOUND, String.format("用户不存在，%s: %s", field, value));
    }

    public UserNotFoundException(Throwable cause) {
        super(ErrorCode.USER_NOT_FOUND, cause);
    }
}
