package com.example.demo.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.entity.Conversation;
import com.example.demo.entity.Project;
import com.example.demo.mapper.ConversationMapper;
import com.example.demo.service.ConversationService;
import com.example.demo.service.ProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;

/**
 * AI对话服务实现类
 * 实现AI对话相关的业务逻辑
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class ConversationServiceImpl implements ConversationService {

    @Autowired
    private ConversationMapper conversationMapper;

    @Autowired
    private ProjectService projectService;

    @Override
    public Conversation createConversation(Conversation conversation) {
        log.info("创建对话记录: userId={}, sessionId={}, type={}", 
                conversation.getUserId(), conversation.getSessionId(), conversation.getConversationType());
        
        // 业务校验
        validateConversationForCreate(conversation);
        
        // 保存对话记录
        int result = conversationMapper.insert(conversation);
        if (result > 0) {
            log.info("对话记录创建成功: {}", conversation.getId());
            return conversation;
        } else {
            throw new RuntimeException("对话记录创建失败");
        }
    }

    @Override
    public String processAIConversation(Long userId, String sessionId, String message, String conversationType) {
        log.info("处理AI对话: userId={}, sessionId={}, type={}, message={}", 
                userId, sessionId, conversationType, message);
        
        if (!StringUtils.hasText(sessionId)) {
            throw new RuntimeException("会话ID不能为空");
        }
        
        if (!StringUtils.hasText(message)) {
            throw new RuntimeException("消息内容不能为空");
        }
        
        if (!StringUtils.hasText(conversationType)) {
            conversationType = Conversation.ConversationType.GENERAL.getCode();
        }
        
        // 生成AI回复
        String response = generateAIResponse(message, conversationType);
        
        // 保存对话记录
        Conversation conversation = new Conversation();
        conversation.setUserId(userId);
        conversation.setSessionId(sessionId);
        conversation.setMessage(message);
        conversation.setResponse(response);
        conversation.setConversationType(conversationType);
        
        createConversation(conversation);
        
        return response;
    }

    @Override
    @Transactional(readOnly = true)
    public Conversation findById(Long id) {
        if (id == null || id <= 0) {
            return null;
        }
        return conversationMapper.selectById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Conversation> findByUserId(Long userId) {
        if (userId == null || userId <= 0) {
            throw new RuntimeException("用户ID不能为空");
        }
        return conversationMapper.findByUserId(userId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Conversation> findBySessionId(String sessionId) {
        if (!StringUtils.hasText(sessionId)) {
            throw new RuntimeException("会话ID不能为空");
        }
        return conversationMapper.findBySessionId(sessionId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Conversation> findByConversationType(String conversationType, Integer limit) {
        if (!StringUtils.hasText(conversationType)) {
            throw new RuntimeException("对话类型不能为空");
        }
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        return conversationMapper.findByConversationType(conversationType, limit);
    }

    @Override
    @Transactional(readOnly = true)
    public IPage<Conversation> findConversationsWithConditions(Page<Conversation> page, Long userId, 
                                                              String conversationType, String keyword) {
        return conversationMapper.findConversationsWithConditions(page, userId, conversationType, keyword);
    }

    @Override
    @Transactional(readOnly = true)
    public List<String> findRecentSessionsByUser(Long userId, Integer limit) {
        if (userId == null || userId <= 0) {
            throw new RuntimeException("用户ID不能为空");
        }
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        return conversationMapper.findRecentSessionsByUser(userId, limit);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Object> getConversationTypeStats(LocalDateTime startTime, LocalDateTime endTime) {
        return conversationMapper.getConversationTypeStats(startTime, endTime);
    }

    @Override
    @Transactional(readOnly = true)
    public Long countConversations(String conversationType, LocalDateTime startTime, LocalDateTime endTime) {
        Long count = conversationMapper.countConversations(conversationType, startTime, endTime);
        return count != null ? count : 0L;
    }

    @Override
    @Transactional(readOnly = true)
    public Long countActiveUsers(LocalDateTime startTime, LocalDateTime endTime) {
        Long count = conversationMapper.countActiveUsers(startTime, endTime);
        return count != null ? count : 0L;
    }

    @Override
    @Transactional(readOnly = true)
    public List<Object> getPopularQuestions(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        return conversationMapper.getPopularQuestions(limit);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Object> getDailyConversationStats() {
        return conversationMapper.getDailyConversationStats();
    }

    @Override
    @Transactional(readOnly = true)
    public Object getConversationOverviewStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 总对话数
        Long totalConversations = countConversations(null, null, null);
        stats.put("totalConversations", totalConversations);
        
        // 今日对话数
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        LocalDateTime todayEnd = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
        Long todayConversations = countConversations(null, todayStart, todayEnd);
        stats.put("todayConversations", todayConversations);
        
        // 活跃用户数
        Long activeUsers = countActiveUsers(null, null);
        stats.put("activeUsers", activeUsers);
        
        // 今日活跃用户数
        Long todayActiveUsers = countActiveUsers(todayStart, todayEnd);
        stats.put("todayActiveUsers", todayActiveUsers);
        
        // 各类型对话统计
        List<Object> typeStats = getConversationTypeStats(null, null);
        stats.put("typeStats", typeStats);
        
        return stats;
    }

    @Override
    @Transactional(readOnly = true)
    public Object getUserConversationSummary(Long userId) {
        if (userId == null || userId <= 0) {
            throw new RuntimeException("用户ID不能为空");
        }
        return conversationMapper.getUserConversationSummary(userId);
    }

    @Override
    public String generateSessionId(Long userId) {
        String prefix = userId != null ? "USER_" + userId : "GUEST";
        return prefix + "_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    @Override
    public int deleteConversationsBefore(LocalDateTime beforeTime) {
        log.info("删除指定时间之前的对话记录: {}", beforeTime);
        
        if (beforeTime == null) {
            throw new RuntimeException("时间参数不能为空");
        }
        
        int deletedCount = conversationMapper.deleteConversationsBefore(beforeTime);
        log.info("删除对话记录完成，共删除{}条记录", deletedCount);
        return deletedCount;
    }

    @Override
    @Transactional(readOnly = true)
    public String getWelcomeMessage(String conversationType) {
        if (!StringUtils.hasText(conversationType)) {
            conversationType = Conversation.ConversationType.GENERAL.getCode();
        }
        
        switch (conversationType) {
            case "donation":
                return "您好！我是爱心助手小暖，我可以帮您了解捐赠流程、推荐合适的救助项目。请问您想要了解什么？";
            case "help_request":
                return "您好！我是爱心助手小暖，我可以帮您了解救助申请流程、所需材料等信息。请问您遇到了什么困难？";
            case "project_info":
                return "您好！我是爱心助手小暖，我可以为您介绍各类救助项目的详细信息。请问您想了解哪个项目？";
            default:
                return "您好！我是爱心助手小暖，我可以帮您了解救助项目、捐赠流程，或解答相关问题。请问需要什么帮助？";
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<String> getSmartReplySuggestions(String message, String conversationType) {
        List<String> suggestions = new ArrayList<>();
        
        if (!StringUtils.hasText(message)) {
            return suggestions;
        }
        
        String lowerMessage = message.toLowerCase();
        
        // 根据消息内容和对话类型提供智能回复建议
        if (lowerMessage.contains("捐赠") || lowerMessage.contains("捐款")) {
            suggestions.add("我想了解如何捐赠");
            suggestions.add("有哪些热门的救助项目？");
            suggestions.add("捐赠流程是什么？");
        } else if (lowerMessage.contains("申请") || lowerMessage.contains("救助")) {
            suggestions.add("我想申请救助");
            suggestions.add("申请救助需要什么材料？");
            suggestions.add("申请流程是什么？");
        } else if (lowerMessage.contains("项目") || lowerMessage.contains("帮助")) {
            suggestions.add("有哪些救助项目？");
            suggestions.add("项目进展如何？");
            suggestions.add("如何联系项目负责人？");
        } else {
            suggestions.add("我想了解救助项目");
            suggestions.add("我想进行捐赠");
            suggestions.add("我需要申请救助");
        }
        
        return suggestions;
    }

    @Override
    @Transactional(readOnly = true)
    public Object analyzeUserIntent(String message) {
        Map<String, Object> intent = new HashMap<>();
        
        if (!StringUtils.hasText(message)) {
            intent.put("type", "unknown");
            intent.put("confidence", 0.0);
            return intent;
        }
        
        String lowerMessage = message.toLowerCase();
        
        // 简单的意图识别逻辑
        if (lowerMessage.contains("捐赠") || lowerMessage.contains("捐款") || lowerMessage.contains("支持")) {
            intent.put("type", "donation");
            intent.put("confidence", 0.8);
            intent.put("description", "用户想要进行捐赠");
        } else if (lowerMessage.contains("申请") || lowerMessage.contains("救助") || lowerMessage.contains("帮助")) {
            intent.put("type", "help_request");
            intent.put("confidence", 0.8);
            intent.put("description", "用户想要申请救助");
        } else if (lowerMessage.contains("项目") || lowerMessage.contains("了解") || lowerMessage.contains("查看")) {
            intent.put("type", "project_info");
            intent.put("confidence", 0.7);
            intent.put("description", "用户想要了解项目信息");
        } else {
            intent.put("type", "general");
            intent.put("confidence", 0.5);
            intent.put("description", "通用咨询");
        }
        
        return intent;
    }

    @Override
    @Transactional(readOnly = true)
    public List<Object> getRelatedProjectRecommendations(String message, Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 3;
        }
        
        List<Object> recommendations = new ArrayList<>();
        
        if (!StringUtils.hasText(message)) {
            return recommendations;
        }
        
        String lowerMessage = message.toLowerCase();
        String category = null;
        
        // 根据消息内容推断用户感兴趣的项目类型
        if (lowerMessage.contains("医疗") || lowerMessage.contains("治病") || lowerMessage.contains("手术")) {
            category = Project.Category.MEDICAL.getCode();
        } else if (lowerMessage.contains("教育") || lowerMessage.contains("上学") || lowerMessage.contains("学费")) {
            category = Project.Category.EDUCATION.getCode();
        } else if (lowerMessage.contains("灾难") || lowerMessage.contains("洪水") || lowerMessage.contains("地震")) {
            category = Project.Category.DISASTER.getCode();
        } else if (lowerMessage.contains("贫困") || lowerMessage.contains("生活") || lowerMessage.contains("困难")) {
            category = Project.Category.POVERTY.getCode();
        }
        
        // 获取推荐项目
        List<Project> projects;
        if (category != null) {
            projects = projectService.findByCategoryAndStatus(category, Project.Status.ACTIVE.getCode());
        } else {
            projects = projectService.findHotProjects(limit);
        }
        
        // 转换为推荐格式
        for (int i = 0; i < Math.min(projects.size(), limit); i++) {
            Project project = projects.get(i);
            Map<String, Object> recommendation = new HashMap<>();
            recommendation.put("id", project.getId());
            recommendation.put("title", project.getTitle());
            recommendation.put("category", project.getCategory());
            recommendation.put("targetAmount", project.getTargetAmount());
            recommendation.put("currentAmount", project.getCurrentAmount());
            recommendations.add(recommendation);
        }
        
        return recommendations;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean needsHumanIntervention(Long conversationId) {
        if (conversationId == null || conversationId <= 0) {
            return false;
        }
        
        Conversation conversation = conversationMapper.selectById(conversationId);
        if (conversation == null) {
            return false;
        }
        
        String message = conversation.getMessage().toLowerCase();
        
        // 检查是否包含需要人工介入的关键词
        String[] interventionKeywords = {
            "投诉", "举报", "欺诈", "虚假", "不满", "退款", "纠纷", "法律", "起诉"
        };
        
        for (String keyword : interventionKeywords) {
            if (message.contains(keyword)) {
                return true;
            }
        }
        
        return false;
    }

    @Override
    public boolean markConversationResolved(Long conversationId, Long adminId) {
        log.info("标记对话为已解决: conversationId={}, adminId={}", conversationId, adminId);
        
        if (conversationId == null || conversationId <= 0) {
            throw new RuntimeException("对话ID不能为空");
        }
        
        if (adminId == null || adminId <= 0) {
            throw new RuntimeException("管理员ID不能为空");
        }
        
        // 这里可以添加标记逻辑，比如在对话记录中添加解决标记
        // 由于当前表结构没有相关字段，这里只是记录日志
        log.info("对话已标记为解决: conversationId={}", conversationId);
        return true;
    }

    @Override
    public boolean rateConversation(Long conversationId, Integer rating, String feedback) {
        log.info("对话评分: conversationId={}, rating={}, feedback={}", conversationId, rating, feedback);
        
        if (conversationId == null || conversationId <= 0) {
            throw new RuntimeException("对话ID不能为空");
        }
        
        if (rating == null || rating < 1 || rating > 5) {
            throw new RuntimeException("评分必须在1-5之间");
        }
        
        // 这里可以添加评分逻辑，比如在对话记录中添加评分字段
        // 由于当前表结构没有相关字段，这里只是记录日志
        log.info("对话评分成功: conversationId={}, rating={}", conversationId, rating);
        return true;
    }

    /**
     * 生成AI回复
     */
    private String generateAIResponse(String message, String conversationType) {
        // 这里实现AI回复逻辑，目前使用模拟回复
        String lowerMessage = message.toLowerCase();
        
        // 根据消息内容和对话类型生成回复
        if (lowerMessage.contains("你好") || lowerMessage.contains("hello")) {
            return getWelcomeMessage(conversationType);
        } else if (lowerMessage.contains("捐赠") || lowerMessage.contains("捐款")) {
            return "感谢您的爱心！捐赠流程很简单：1. 选择您想支持的项目 2. 填写捐赠金额 3. 选择支付方式 4. 完成支付。您可以通过支付宝、微信或银行转账进行捐赠。需要我为您推荐一些项目吗？";
        } else if (lowerMessage.contains("申请") || lowerMessage.contains("救助")) {
            return "我了解您的困难，申请救助需要以下步骤：1. 注册并登录账户 2. 填写救助申请表 3. 上传相关证明材料（如医疗证明、收入证明等）4. 等待管理员审核。审核通过后，我们会安排相应的帮助。请问您遇到的是什么类型的困难？";
        } else if (lowerMessage.contains("项目") || lowerMessage.contains("了解")) {
            return "我们平台有多种类型的救助项目：医疗救助、教育助学、灾难救援、扶贫助困等。所有项目都经过严格审核，资金使用透明公开。您想了解哪个类型的项目？我可以为您详细介绍。";
        } else if (lowerMessage.contains("流程") || lowerMessage.contains("怎么")) {
            return "具体流程如下：\n1. 浏览项目列表，选择您关心的项目\n2. 查看项目详情，了解资金用途\n3. 点击捐赠按钮，填写捐赠信息\n4. 选择支付方式完成支付\n5. 获得捐赠证书，可随时查看项目进展\n\n如果您需要申请救助，流程是：注册登录 → 填写申请 → 上传证明 → 等待审核。";
        } else if (lowerMessage.contains("安全") || lowerMessage.contains("可靠")) {
            return "请放心，我们是正规的爱心救助平台：\n✓ 所有项目都经过严格审核\n✓ 资金使用透明公开\n✓ 定期公布项目进展\n✓ 支持第三方监督\n✓ 提供捐赠证书和发票\n\n您的每一分善款都会用到真正需要帮助的人身上。";
        } else if (lowerMessage.contains("联系") || lowerMessage.contains("客服")) {
            return "如需人工客服，您可以：\n📞 客服电话：400-123-4567（工作时间：9:00-18:00）\n📧 邮箱：<EMAIL>\n💬 在线客服：点击右下角客服图标\n\n我也会尽力为您解答问题，请告诉我您需要什么帮助？";
        } else {
            return "感谢您的咨询！我是爱心助手小暖，我可以帮您：\n• 了解救助项目信息\n• 指导捐赠流程\n• 协助申请救助\n• 解答相关问题\n\n请告诉我您具体需要什么帮助，我会为您详细解答。";
        }
    }

    /**
     * 验证对话创建信息
     */
    private void validateConversationForCreate(Conversation conversation) {
        if (conversation == null) {
            throw new RuntimeException("对话信息不能为空");
        }
        
        if (!StringUtils.hasText(conversation.getSessionId())) {
            throw new RuntimeException("会话ID不能为空");
        }
        
        if (!StringUtils.hasText(conversation.getMessage())) {
            throw new RuntimeException("消息内容不能为空");
        }
        
        if (!StringUtils.hasText(conversation.getResponse())) {
            throw new RuntimeException("回复内容不能为空");
        }
        
        if (conversation.getMessage().length() > 1000) {
            throw new RuntimeException("消息内容长度不能超过1000字符");
        }
        
        if (conversation.getResponse().length() > 2000) {
            throw new RuntimeException("回复内容长度不能超过2000字符");
        }
        
        if (StringUtils.hasText(conversation.getConversationType()) && 
            !isValidConversationType(conversation.getConversationType())) {
            throw new RuntimeException("无效的对话类型");
        }
    }

    /**
     * 验证对话类型是否有效
     */
    private boolean isValidConversationType(String conversationType) {
        for (Conversation.ConversationType type : Conversation.ConversationType.values()) {
            if (type.getCode().equals(conversationType)) {
                return true;
            }
        }
        return false;
    }
}
