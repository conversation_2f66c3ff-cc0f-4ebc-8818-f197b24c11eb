package com.example.demo.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 救助申请响应DTO
 */
@Schema(description = "救助申请响应")
public class HelpRequestResponseDTO {

    @Schema(description = "申请ID", example = "1")
    private Long id;

    @Schema(description = "申请用户ID", example = "2")
    private Long userId;

    @Schema(description = "申请用户名", example = "testuser")
    private String username;

    @Schema(description = "申请人姓名", example = "张三")
    private String applicantName;

    @Schema(description = "申请标题", example = "父亲突发心脏病急需手术费")
    private String title;

    @Schema(description = "详细描述", example = "我父亲突发急性心肌梗塞，需要立即进行心脏搭桥手术...")
    private String description;

    @Schema(description = "申请分类", example = "medical")
    private String category;

    @Schema(description = "分类描述", example = "医疗救助")
    private String categoryDescription;

    @Schema(description = "所需金额", example = "150000.00")
    private BigDecimal amountNeeded;

    @Schema(description = "联系电话（脱敏）", example = "138****8001")
    private String contactPhone;

    @Schema(description = "联系地址", example = "北京市朝阳区某某街道")
    private String contactAddress;

    @Schema(description = "证明材料图片URL列表")
    private List<String> proofImages;

    @Schema(description = "申请状态", example = "pending")
    private String status;

    @Schema(description = "状态描述", example = "待审核")
    private String statusDescription;

    @Schema(description = "管理员回复", example = "经审核，情况属实，已安排志愿者跟进。")
    private String adminResponse;

    @Schema(description = "处理管理员ID", example = "1")
    private Long adminId;

    @Schema(description = "处理管理员姓名", example = "管理员")
    private String adminName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getApplicantName() {
        return applicantName;
    }

    public void setApplicantName(String applicantName) {
        this.applicantName = applicantName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCategoryDescription() {
        return categoryDescription;
    }

    public void setCategoryDescription(String categoryDescription) {
        this.categoryDescription = categoryDescription;
    }

    public BigDecimal getAmountNeeded() {
        return amountNeeded;
    }

    public void setAmountNeeded(BigDecimal amountNeeded) {
        this.amountNeeded = amountNeeded;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactAddress() {
        return contactAddress;
    }

    public void setContactAddress(String contactAddress) {
        this.contactAddress = contactAddress;
    }

    public List<String> getProofImages() {
        return proofImages;
    }

    public void setProofImages(List<String> proofImages) {
        this.proofImages = proofImages;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusDescription() {
        return statusDescription;
    }

    public void setStatusDescription(String statusDescription) {
        this.statusDescription = statusDescription;
    }

    public String getAdminResponse() {
        return adminResponse;
    }

    public void setAdminResponse(String adminResponse) {
        this.adminResponse = adminResponse;
    }

    public Long getAdminId() {
        return adminId;
    }

    public void setAdminId(Long adminId) {
        this.adminId = adminId;
    }

    public String getAdminName() {
        return adminName;
    }

    public void setAdminName(String adminName) {
        this.adminName = adminName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}
