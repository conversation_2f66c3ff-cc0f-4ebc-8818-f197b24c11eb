package com.example.demo.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * AI配置类
 * 配置火山引擎豆包AI相关参数
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ai.volcengine")
public class AiConfig {

    /**
     * API密钥
     */
    private String apiKey = "5b245916-7820-41d9-861e-65c89f34f100";

    /**
     * 基础URL
     */
    private String baseUrl = "https://ark.cn-beijing.volces.com/api/v3";

    /**
     * 模型名称
     */
    private String model = "doubao-seed-1-6-250615";

    /**
     * 连接池配置
     */
    private ConnectionPool connectionPool = new ConnectionPool();

    /**
     * 调度器配置
     */
    private Dispatcher dispatcher = new Dispatcher();

    /**
     * 默认系统提示词
     */
    private String systemPrompt = "你是一个专业的AI助手，专门为爱心救助平台提供服务。请用温暖、专业的语调回答用户问题，并提供有用的建议。";

    /**
     * 欢迎消息
     */
    private String welcomeMessage = "您好！我是爱心救助平台的AI助手，很高兴为您服务。我可以帮助您了解平台功能、解答疑问、提供救助建议等。请问有什么可以帮助您的吗？";

    /**
     * 热门问题列表
     */
    private String[] popularQuestions = {
        "如何申请救助？",
        "如何进行捐赠？",
        "救助申请需要什么材料？",
        "捐赠资金如何使用？",
        "如何查看救助进度？",
        "平台如何保证资金安全？"
    };

    /**
     * 最大对话历史记录数
     */
    private int maxHistorySize = 10;

    /**
     * 请求超时时间（秒）- 设置为0表示无超时限制
     */
    private int timeoutSeconds = 0;

    /**
     * 最大重试次数
     */
    private int maxRetries = 3;

    /**
     * 连接池配置类
     */
    @Data
    public static class ConnectionPool {
        /**
         * 最大空闲连接数
         */
        private int maxIdleConnections = 5;

        /**
         * 连接保持时间（分钟）
         */
        private long keepAliveDuration = 1;
    }

    /**
     * 调度器配置类
     */
    @Data
    public static class Dispatcher {
        /**
         * 最大请求数
         */
        private int maxRequests = 64;

        /**
         * 每个主机最大请求数
         */
        private int maxRequestsPerHost = 5;
    }
}
