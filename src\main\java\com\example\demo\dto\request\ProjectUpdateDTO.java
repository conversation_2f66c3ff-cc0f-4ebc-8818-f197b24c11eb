package com.example.demo.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 救助项目更新请求DTO
 */
@Schema(description = "救助项目更新请求")
public class ProjectUpdateDTO {

    @Schema(description = "项目ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "项目ID不能为空")
    private Long id;

    @Schema(description = "项目标题", example = "小明白血病治疗救助")
    @Size(max = 200, message = "项目标题长度不能超过200字符")
    private String title;

    @Schema(description = "项目描述", example = "小明今年8岁，患有急性白血病...")
    @Size(max = 2000, message = "项目描述长度不能超过2000字符")
    private String description;

    @Schema(description = "项目分类", example = "medical")
    private String category;

    @Schema(description = "目标金额", example = "300000.00")
    @DecimalMin(value = "0.01", message = "目标金额必须大于0")
    private BigDecimal targetAmount;

    @Schema(description = "项目图片URL列表", example = "[\"https://example.com/image1.jpg\"]")
    private List<String> images;

    @Schema(description = "联系信息", example = "联系人：小明妈妈，电话：138****1234")
    @Size(max = 500, message = "联系信息长度不能超过500字符")
    private String contactInfo;

    @Schema(description = "项目状态", example = "active")
    private String status;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getTargetAmount() {
        return targetAmount;
    }

    public void setTargetAmount(BigDecimal targetAmount) {
        this.targetAmount = targetAmount;
    }

    public List<String> getImages() {
        return images;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }

    public String getContactInfo() {
        return contactInfo;
    }

    public void setContactInfo(String contactInfo) {
        this.contactInfo = contactInfo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
}
