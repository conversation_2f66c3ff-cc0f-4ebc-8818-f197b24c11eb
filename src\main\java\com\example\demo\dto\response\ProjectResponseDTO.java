package com.example.demo.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 救助项目响应DTO
 */
@Schema(description = "救助项目响应")
public class ProjectResponseDTO {

    @Schema(description = "项目ID", example = "1")
    private Long id;

    @Schema(description = "项目标题", example = "小明白血病治疗救助")
    private String title;

    @Schema(description = "项目描述", example = "小明今年8岁，患有急性白血病...")
    private String description;

    @Schema(description = "项目分类", example = "medical")
    private String category;

    @Schema(description = "分类描述", example = "医疗救助")
    private String categoryDescription;

    @Schema(description = "目标金额", example = "300000.00")
    private BigDecimal targetAmount;

    @Schema(description = "当前筹集金额", example = "85000.00")
    private BigDecimal currentAmount;

    @Schema(description = "完成百分比", example = "28.33")
    private BigDecimal completionPercentage;

    @Schema(description = "项目图片URL列表")
    private List<String> images;

    @Schema(description = "联系信息", example = "联系人：小明妈妈，电话：138****1234")
    private String contactInfo;

    @Schema(description = "项目状态", example = "active")
    private String status;

    @Schema(description = "状态描述", example = "进行中")
    private String statusDescription;

    @Schema(description = "发布管理员ID", example = "1")
    private Long adminId;

    @Schema(description = "发布管理员姓名", example = "管理员")
    private String adminName;

    @Schema(description = "捐赠人数", example = "156")
    private Long donorCount;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCategoryDescription() {
        return categoryDescription;
    }

    public void setCategoryDescription(String categoryDescription) {
        this.categoryDescription = categoryDescription;
    }

    public BigDecimal getTargetAmount() {
        return targetAmount;
    }

    public void setTargetAmount(BigDecimal targetAmount) {
        this.targetAmount = targetAmount;
    }

    public BigDecimal getCurrentAmount() {
        return currentAmount;
    }

    public void setCurrentAmount(BigDecimal currentAmount) {
        this.currentAmount = currentAmount;
    }

    public BigDecimal getCompletionPercentage() {
        return completionPercentage;
    }

    public void setCompletionPercentage(BigDecimal completionPercentage) {
        this.completionPercentage = completionPercentage;
    }

    public List<String> getImages() {
        return images;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }

    public String getContactInfo() {
        return contactInfo;
    }

    public void setContactInfo(String contactInfo) {
        this.contactInfo = contactInfo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusDescription() {
        return statusDescription;
    }

    public void setStatusDescription(String statusDescription) {
        this.statusDescription = statusDescription;
    }

    public Long getAdminId() {
        return adminId;
    }

    public void setAdminId(Long adminId) {
        this.adminId = adminId;
    }

    public String getAdminName() {
        return adminName;
    }

    public void setAdminName(String adminName) {
        this.adminName = adminName;
    }

    public Long getDonorCount() {
        return donorCount;
    }

    public void setDonorCount(Long donorCount) {
        this.donorCount = donorCount;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}
