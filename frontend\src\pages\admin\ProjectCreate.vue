<template>
  <div class="admin-project-create">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button text @click="$router.back()">
          <el-icon>
            <ArrowLeft />
          </el-icon>
          返回
        </el-button>
        <div class="title-section">
          <h1 class="page-title">创建项目</h1>
          <p class="page-description">发布新的救助项目</p>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="creating" @click="handleCreate">
          创建项目
        </el-button>
      </div>
    </div>

    <!-- 创建表单 -->
    <div class="form-container">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px" size="large">
        <el-row :gutter="24">
          <!-- 左侧表单 -->
          <el-col :span="16">
            <el-card title="基本信息">
              <el-form-item label="项目标题" prop="title">
                <el-input v-model="formData.title" placeholder="请输入项目标题，如：小明白血病治疗救助" maxlength="200" show-word-limit />
              </el-form-item>

              <el-form-item label="项目分类" prop="category">
                <el-select v-model="formData.category" placeholder="请选择项目分类" style="width: 100%">
                  <el-option label="医疗救助" value="medical" />
                  <el-option label="教育助学" value="education" />
                  <el-option label="灾难救援" value="disaster" />
                  <el-option label="扶贫济困" value="poverty" />
                  <el-option label="其他" value="other" />
                </el-select>
              </el-form-item>

              <el-form-item label="目标金额" prop="targetAmount">
                <el-input-number v-model="formData.targetAmount" :min="1" :max="10000000" :step="100" :precision="2"
                  style="width: 100%" placeholder="请输入目标金额" />
                <div class="form-tip">
                  建议根据实际需求设置合理的目标金额
                </div>
              </el-form-item>

              <el-form-item label="项目描述" prop="description">
                <el-input v-model="formData.description" type="textarea" :rows="8"
                  placeholder="请详细描述项目情况、救助对象、资金用途等信息&#10;&#10;建议包含：&#10;1. 救助对象的基本情况&#10;2. 遇到的困难和问题&#10;3. 资金的具体用途&#10;4. 预期达到的效果"
                  maxlength="2000" show-word-limit />
              </el-form-item>

              <el-form-item label="联系信息" prop="contactInfo">
                <el-input v-model="formData.contactInfo" type="textarea" :rows="3"
                  placeholder="请输入联系人姓名、电话等联系方式&#10;如：联系人：张女士，电话：138****1234" maxlength="500" show-word-limit />
              </el-form-item>
            </el-card>

            <!-- 项目图片 -->
            <el-card title="项目图片" style="margin-top: 24px;">
              <div class="image-upload-section">
                <div class="current-images" v-if="imageList.length > 0">
                  <div class="image-grid">
                    <div v-for="(image, index) in imageList" :key="index" class="image-item">
                      <el-image :src="image" fit="cover" class="image-preview" :preview-src-list="imageList"
                        :initial-index="index" />
                      <div class="image-actions">
                        <el-button text type="danger" size="small" @click="removeImage(index)">
                          <el-icon>
                            <Delete />
                          </el-icon>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>

                <el-upload ref="uploadRef" :action="uploadAction" :headers="uploadHeaders" :data="uploadData"
                  :on-success="handleUploadSuccess" :on-error="handleUploadError" :before-upload="beforeUpload"
                  :show-file-list="false" accept="image/*" multiple>
                  <el-button type="primary">
                    <el-icon>
                      <Plus />
                    </el-icon>
                    上传图片
                  </el-button>
                  <template #tip>
                    <div class="upload-tip">
                      支持 JPG、PNG 格式，单个文件不超过 5MB，最多上传 5 张图片<br>
                      建议上传能够说明项目情况的相关图片
                    </div>
                  </template>
                </el-upload>
              </div>
            </el-card>
          </el-col>

          <!-- 右侧信息 -->
          <el-col :span="8">
            <el-card title="项目设置">
              <el-form-item label="项目状态" prop="status">
                <el-select v-model="formData.status" placeholder="请选择项目状态" style="width: 100%">
                  <el-option label="草稿" value="draft" />
                  <el-option label="进行中" value="active" />
                </el-select>
                <div class="form-tip">
                  草稿状态：项目不会公开显示<br>
                  进行中：项目立即公开，可接受捐赠
                </div>
              </el-form-item>

              <el-form-item label="初始金额">
                <el-input-number v-model="formData.currentAmount" :min="0" :max="formData.targetAmount" :step="100"
                  :precision="2" style="width: 100%" placeholder="可选，默认为0" />
                <div class="form-tip">
                  如果项目已有部分资金，可以设置初始金额
                </div>
              </el-form-item>
            </el-card>

            <!-- 创建提示 -->
            <el-card title="创建提示" style="margin-top: 24px;">
              <div class="tips-content">
                <div class="tip-item">
                  <el-icon class="tip-icon">
                    <InfoFilled />
                  </el-icon>
                  <span>请确保项目信息真实有效</span>
                </div>
                <div class="tip-item">
                  <el-icon class="tip-icon">
                    <InfoFilled />
                  </el-icon>
                  <span>项目图片有助于提高捐赠转化率</span>
                </div>
                <div class="tip-item">
                  <el-icon class="tip-icon">
                    <InfoFilled />
                  </el-icon>
                  <span>详细的描述能让捐赠者更好地了解项目</span>
                </div>
                <div class="tip-item">
                  <el-icon class="tip-icon">
                    <InfoFilled />
                  </el-icon>
                  <span>可以先保存为草稿，完善后再发布</span>
                </div>
              </div>
            </el-card>

            <!-- 预览效果 -->
            <el-card title="预览效果" style="margin-top: 24px;">
              <div class="preview-content">
                <div class="preview-progress">
                  <span class="preview-label">筹款进度</span>
                  <el-progress :percentage="getProgress()" :stroke-width="8" />
                  <span class="progress-text">{{ getProgress() }}%</span>
                </div>

                <div class="preview-stats">
                  <div class="stat-item">
                    <span class="stat-label">已筹金额</span>
                    <span class="stat-value">¥{{ formatAmount(formData.currentAmount) }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">目标金额</span>
                    <span class="stat-value">¥{{ formatAmount(formData.targetAmount) }}</span>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    ArrowLeft,
    Plus,
    Delete,
    InfoFilled
  } from '@element-plus/icons-vue'
  import { createProject } from '@/api/admin'
  import { useUserStore } from '@/stores/user'

  const router = useRouter()
  const userStore = useUserStore()

  // 响应式数据
  const creating = ref(false)
  const formRef = ref()

  // 表单数据
  const formData = reactive({
    title: '',
    category: '',
    description: '',
    contactInfo: '',
    targetAmount: 0,
    currentAmount: 0,
    status: 'draft'
  })

  // 图片相关
  const imageList = ref([])
  const uploadRef = ref()

  // 上传配置
  const uploadAction = computed(() => 'http://localhost:8080/api/files/upload/project')
  const uploadHeaders = computed(() => ({
    'Authorization': `Bearer ${userStore.token}`
  }))
  const uploadData = computed(() => ({
    category: 'project'
  }))

  // 表单验证规则
  const formRules = {
    title: [
      { required: true, message: '请输入项目标题', trigger: 'blur' },
      { min: 5, max: 200, message: '标题长度应在 5 到 200 个字符', trigger: 'blur' }
    ],
    category: [
      { required: true, message: '请选择项目分类', trigger: 'change' }
    ],
    description: [
      { required: true, message: '请输入项目描述', trigger: 'blur' },
      { min: 20, max: 2000, message: '描述长度应在 20 到 2000 个字符', trigger: 'blur' }
    ],
    contactInfo: [
      { required: true, message: '请输入联系信息', trigger: 'blur' },
      { min: 5, max: 500, message: '联系信息长度应在 5 到 500 个字符', trigger: 'blur' }
    ],
    targetAmount: [
      { required: true, message: '请输入目标金额', trigger: 'blur' },
      { type: 'number', min: 1, message: '目标金额必须大于 0', trigger: 'blur' }
    ],
    status: [
      { required: true, message: '请选择项目状态', trigger: 'change' }
    ]
  }

  // 创建项目
  const handleCreate = async () => {
    try {
      // 表单验证
      await formRef.value.validate()

      creating.value = true

      // 准备创建数据
      const createData = {
        title: formData.title,
        description: formData.description,
        category: formData.category,
        targetAmount: formData.targetAmount,
        contactInfo: formData.contactInfo,
        status: formData.status,
        images: imageList.value // 后端期望List<String>格式
      }

      // 如果设置了初始金额，也包含进去
      if (formData.currentAmount > 0) {
        createData.currentAmount = formData.currentAmount
      }

      await createProject(createData)

      ElMessage.success('项目创建成功')
      router.push('/admin/projects')

    } catch (error) {
      if (error.errors) {
        // 表单验证错误
        ElMessage.error('请检查表单填写是否正确')
      } else {
        console.error('创建项目失败:', error)
        // 显示后端返回的具体错误信息
        if (error.message) {
          ElMessage.error(error.message)
        }
      }
    } finally {
      creating.value = false
    }
  }

  // 取消创建
  const handleCancel = async () => {
    // 检查是否有未保存的内容
    const hasContent = formData.title || formData.description || formData.contactInfo || imageList.value.length > 0

    if (hasContent) {
      try {
        await ElMessageBox.confirm(
          '确定要取消创建吗？未保存的内容将会丢失。',
          '确认取消',
          {
            confirmButtonText: '确定',
            cancelButtonText: '继续编辑',
            type: 'warning'
          }
        )

        router.push('/admin/projects')

      } catch (error) {
        // 用户取消了取消操作，继续编辑
      }
    } else {
      router.push('/admin/projects')
    }
  }

  // 图片上传处理
  const beforeUpload = (file) => {
    // 检查文件类型
    const isImage = file.type.startsWith('image/')
    if (!isImage) {
      ElMessage.error('只能上传图片文件！')
      return false
    }

    // 检查文件大小
    const isLt5M = file.size / 1024 / 1024 < 5
    if (!isLt5M) {
      ElMessage.error('图片大小不能超过 5MB！')
      return false
    }

    // 检查图片数量
    if (imageList.value.length >= 5) {
      ElMessage.error('最多只能上传 5 张图片！')
      return false
    }

    return true
  }

  const handleUploadSuccess = (response) => {
    // 处理响应数据 - 可能是直接的数据或包装在data中
    let fileData = response
    if (response && response.data) {
      fileData = response.data
    }

    if (fileData && fileData.fileUrl) {
      imageList.value.push(fileData.fileUrl)
      ElMessage.success('图片上传成功')
    } else {
      ElMessage.error('图片上传失败：响应格式错误')
    }
  }

  const handleUploadError = (error) => {
    console.error('图片上传失败:', error)
    ElMessage.error('图片上传失败')
  }

  // 删除图片
  const removeImage = (index) => {
    imageList.value.splice(index, 1)
    ElMessage.success('图片已删除')
  }

  // 工具函数
  const formatAmount = (amount) => {
    if (!amount) return '0'
    return Number(amount).toLocaleString()
  }

  const getProgress = () => {
    if (!formData.targetAmount || formData.targetAmount === 0) return 0
    const progress = Math.round((formData.currentAmount / formData.targetAmount) * 100)
    return Math.min(progress, 100)
  }
</script>

<style scoped>
  .admin-project-create {
    max-width: 1400px;
    margin: 0 auto;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e8e8e8;
  }

  .header-left {
    display: flex;
    align-items: flex-start;
    gap: 16px;
  }

  .title-section {
    flex: 1;
  }

  .page-title {
    margin: 0 0 4px 0;
    color: #333;
    font-size: 24px;
    font-weight: 600;
  }

  .page-description {
    margin: 0;
    color: #666;
    font-size: 14px;
  }

  .header-right {
    display: flex;
    gap: 12px;
  }

  .form-container {
    margin-bottom: 24px;
  }

  .form-tip {
    color: #999;
    font-size: 12px;
    margin-top: 4px;
    line-height: 1.4;
  }

  .image-upload-section {
    space-y: 16px;
  }

  .image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 16px;
    margin-bottom: 16px;
  }

  .image-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e8e8e8;
  }

  .image-preview {
    width: 100%;
    height: 120px;
    cursor: pointer;
  }

  .image-actions {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 4px;
    padding: 4px;
  }

  .upload-tip {
    color: #999;
    font-size: 12px;
    margin-top: 8px;
    line-height: 1.4;
  }

  .tips-content {
    space-y: 12px;
  }

  .tip-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 8px 0;
    font-size: 14px;
    color: #666;
  }

  .tip-icon {
    color: #409eff;
    margin-top: 2px;
    flex-shrink: 0;
  }

  .preview-content {
    space-y: 16px;
  }

  .preview-progress {
    text-align: center;
  }

  .preview-label {
    display: block;
    color: #666;
    font-size: 14px;
    margin-bottom: 8px;
  }

  .progress-text {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
    display: block;
  }

  .preview-stats {
    space-y: 8px;
  }

  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .stat-item:last-child {
    border-bottom: none;
  }

  .stat-label {
    color: #666;
    font-size: 14px;
  }

  .stat-value {
    color: #333;
    font-size: 14px;
    font-weight: 500;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .page-header {
      flex-direction: column;
      gap: 16px;
    }

    .header-left {
      width: 100%;
    }

    .header-right {
      width: 100%;
      justify-content: flex-start;
    }

    .el-col {
      margin-bottom: 24px;
    }

    .image-grid {
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      gap: 12px;
    }

    .image-preview {
      height: 100px;
    }
  }
</style>