package com.example.demo.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.dto.ApiResponse;
import com.example.demo.dto.PageDTO;
import com.example.demo.dto.converter.DTOConverter;
import com.example.demo.dto.request.ProjectCreateDTO;
import com.example.demo.dto.request.ProjectQueryDTO;
import com.example.demo.dto.request.ProjectUpdateDTO;
import com.example.demo.dto.response.ProjectListDTO;
import com.example.demo.dto.response.ProjectResponseDTO;
import com.example.demo.entity.Project;
import com.example.demo.service.ProjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 救助项目管理Controller
 * 提供项目创建、查询、更新、删除等API
 */
@Slf4j
@RestController
@RequestMapping("/api/projects")
@Tag(name = "救助项目管理", description = "救助项目的创建、查询、更新、删除等API")
@Validated
public class ProjectController extends BaseController {

    @Autowired
    private ProjectService projectService;

    @PostMapping
    @Operation(summary = "创建救助项目", description = "管理员创建新的救助项目")
    public ApiResponse<ProjectResponseDTO> createProject(@Valid @RequestBody ProjectCreateDTO createDTO) {
        try {
            Long adminId = getCurrentUserId();
            logOperation("创建救助项目", adminId, createDTO.getTitle());
            
            // 权限检查：只有管理员可以创建项目
            if (!isAdmin()) {
                return forbidden("无权限创建项目");
            }
            
            // 创建项目实体
            Project project = new Project();
            project.setTitle(createDTO.getTitle());
            project.setDescription(createDTO.getDescription());
            project.setCategory(createDTO.getCategory());
            project.setTargetAmount(createDTO.getTargetAmount());
            project.setContactInfo(createDTO.getContactInfo());
            project.setAdminId(adminId);

            // 设置项目状态，默认为草稿
            project.setStatus(createDTO.getStatus() != null ? createDTO.getStatus() : "draft");

            // 设置当前金额，默认为0
            project.setCurrentAmount(createDTO.getCurrentAmount() != null ? createDTO.getCurrentAmount() : BigDecimal.ZERO);
            
            // 处理图片列表
            if (createDTO.getImages() != null && !createDTO.getImages().isEmpty()) {
                String imagesJson = "[\"" + String.join("\",\"", createDTO.getImages()) + "\"]";
                project.setImages(imagesJson);
            }
            
            // 创建项目
            Project createdProject = projectService.createProject(project, adminId);
            ProjectResponseDTO responseDTO = DTOConverter.convertToProjectResponseDTO(createdProject);
            
            return success("项目创建成功", responseDTO);
        } catch (Exception e) {
            log.error("创建救助项目失败", e);
            return error("创建项目失败：" + e.getMessage());
        }
    }

    @GetMapping
    @Operation(summary = "分页查询救助项目", description = "分页查询救助项目列表，支持条件筛选")
    public ApiResponse<PageDTO<ProjectListDTO>> getProjectList(@Valid ProjectQueryDTO queryDTO) {
        try {
            logOperation("分页查询救助项目", queryDTO.getCurrent(), queryDTO.getSize(), 
                        queryDTO.getCategory(), queryDTO.getStatus());
            
            Page<Project> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
            var result = projectService.findProjectsWithConditions(
                    page, queryDTO.getCategory(), queryDTO.getStatus(),
                    queryDTO.getKeyword());
            
            return convertPageResult(result, DTOConverter::convertToProjectListDTO);
        } catch (Exception e) {
            log.error("查询救助项目列表失败", e);
            return error("查询项目列表失败：" + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取项目详情", description = "根据ID获取救助项目的详细信息")
    public ApiResponse<ProjectResponseDTO> getProjectById(
            @Parameter(description = "项目ID") @PathVariable @NotNull Long id) {
        try {
            logOperation("获取项目详情", id);
            
            Project project = projectService.findById(id);
            if (project == null) {
                return notFound("项目不存在");
            }
            
            ProjectResponseDTO responseDTO = DTOConverter.convertToProjectResponseDTO(project);
            
            // 获取捐赠人数
            Long donorCount = projectService.getProjectDonorCount(id);
            responseDTO.setDonorCount(donorCount);
            
            return success(responseDTO);
        } catch (Exception e) {
            log.error("获取项目详情失败", e);
            return error("获取项目详情失败：" + e.getMessage());
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新救助项目", description = "管理员更新救助项目信息")
    public ApiResponse<ProjectResponseDTO> updateProject(
            @Parameter(description = "项目ID") @PathVariable @NotNull Long id,
            @Valid @RequestBody ProjectUpdateDTO updateDTO) {
        try {
            Long adminId = getCurrentUserId();
            logOperation("更新救助项目", adminId, id, updateDTO.getTitle());

            // 权限检查：只有管理员可以更新项目
            if (!isAdmin()) {
                return forbidden("无权限更新项目");
            }
            
            // 检查项目是否存在
            Project existingProject = projectService.findById(id);
            if (existingProject == null) {
                return notFound("项目不存在");
            }
            
            // 创建更新实体
            Project project = new Project();
            project.setId(id);
            project.setTitle(updateDTO.getTitle());
            project.setDescription(updateDTO.getDescription());
            project.setCategory(updateDTO.getCategory());
            project.setTargetAmount(updateDTO.getTargetAmount());
            project.setContactInfo(updateDTO.getContactInfo());
            project.setStatus(updateDTO.getStatus());
            
            // 处理图片列表
            if (updateDTO.getImages() != null && !updateDTO.getImages().isEmpty()) {
                String imagesJson = "[\"" + String.join("\",\"", updateDTO.getImages()) + "\"]";
                project.setImages(imagesJson);
            }
            
            // 更新项目
            Project updatedProject = projectService.updateProject(project, adminId);
            ProjectResponseDTO responseDTO = DTOConverter.convertToProjectResponseDTO(updatedProject);
            
            return success("项目更新成功", responseDTO);
        } catch (Exception e) {
            log.error("更新救助项目失败", e);
            return error("更新项目失败：" + e.getMessage());
        }
    }

    @PutMapping("/{id}/current-amount")
    @Operation(summary = "修改项目当前金额", description = "管理员修改项目当前筹集金额")
    public ApiResponse<Void> updateProjectCurrentAmount(
            @Parameter(description = "项目ID") @PathVariable @NotNull Long id,
            @Parameter(description = "新的当前金额") @RequestParam @NotNull BigDecimal amount) {
        try {
            Long adminId = getCurrentUserId();
            logOperation("修改项目当前金额", adminId, id, amount);

            // 权限检查：只有管理员可以修改金额
            if (!isAdmin()) {
                return forbidden("无权限修改项目金额");
            }

            // 检查项目是否存在
            Project existingProject = projectService.findById(id);
            if (existingProject == null) {
                return notFound("项目不存在");
            }

            // 验证金额
            if (amount.compareTo(BigDecimal.ZERO) < 0) {
                return badRequest("金额不能为负数");
            }

            if (amount.compareTo(existingProject.getTargetAmount()) > 0) {
                return badRequest("当前金额不能超过目标金额");
            }

            boolean success = projectService.setCurrentAmount(id, amount);
            if (success) {
                return success("项目金额修改成功", null);
            } else {
                return error("项目金额修改失败");
            }
        } catch (Exception e) {
            log.error("修改项目金额失败", e);
            return error("修改项目金额失败：" + e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除救助项目", description = "管理员删除救助项目")
    public ApiResponse<Void> deleteProject(
            @Parameter(description = "项目ID") @PathVariable @NotNull Long id) {
        try {
            Long adminId = getCurrentUserId();
            logOperation("删除救助项目", adminId, id);
            
            // 权限检查：只有管理员可以删除项目
            if (!isAdmin()) {
                return forbidden("无权限删除项目");
            }
            
            boolean success = projectService.deleteProject(id, adminId);
            if (success) {
                return success("项目删除成功", null);
            } else {
                return error("项目删除失败");
            }
        } catch (Exception e) {
            log.error("删除救助项目失败", e);
            return error("删除项目失败：" + e.getMessage());
        }
    }

    @PutMapping("/{id}/status")
    @Operation(summary = "更新项目状态", description = "管理员更新项目状态")
    public ApiResponse<Void> updateProjectStatus(
            @Parameter(description = "项目ID") @PathVariable @NotNull Long id,
            @Parameter(description = "新状态") @RequestParam @NotNull String status) {
        try {
            Long adminId = getCurrentUserId();
            logOperation("更新项目状态", adminId, id, status);
            
            // 权限检查：只有管理员可以更新项目状态
            if (!isAdmin()) {
                return forbidden("无权限更新项目状态");
            }
            
            boolean success = projectService.updateProjectStatus(id, status, adminId);
            if (success) {
                return success("项目状态更新成功", null);
            } else {
                return error("项目状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新项目状态失败", e);
            return error("更新项目状态失败：" + e.getMessage());
        }
    }

    @GetMapping("/hot")
    @Operation(summary = "获取热门项目", description = "获取热门救助项目列表")
    public ApiResponse<List<ProjectListDTO>> getHotProjects(
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        try {
            logOperation("获取热门项目", limit);
            
            List<Project> projects = projectService.findHotProjects(limit);
            List<ProjectListDTO> responseList = convertList(projects, DTOConverter::convertToProjectListDTO);
            
            return success(responseList);
        } catch (Exception e) {
            log.error("获取热门项目失败", e);
            return error("获取热门项目失败：" + e.getMessage());
        }
    }

    @GetMapping("/recommended")
    @Operation(summary = "获取推荐项目", description = "获取推荐救助项目列表")
    public ApiResponse<List<ProjectListDTO>> getRecommendedProjects(
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        try {
            logOperation("获取推荐项目", limit);
            
            List<Project> projects = projectService.getRecommendedProjects(limit);
            List<ProjectListDTO> responseList = convertList(projects, DTOConverter::convertToProjectListDTO);
            
            return success(responseList);
        } catch (Exception e) {
            log.error("获取推荐项目失败", e);
            return error("获取推荐项目失败：" + e.getMessage());
        }
    }

    @GetMapping("/categories")
    @Operation(summary = "获取项目分类统计", description = "获取各分类的项目数量统计")
    public ApiResponse<List<Object>> getProjectCategoryStats() {
        try {
            logOperation("获取项目分类统计");
            
            List<Object> stats = projectService.getProjectStatsByCategory();
            return success(stats);
        } catch (Exception e) {
            log.error("获取项目分类统计失败", e);
            return error("获取项目分类统计失败：" + e.getMessage());
        }
    }

    @GetMapping("/stats")
    @Operation(summary = "获取项目统计信息", description = "获取项目相关的统计数据")
    public ApiResponse<Object> getProjectStats() {
        try {
            logOperation("获取项目统计信息");
            
            Object stats = projectService.getProjectOverviewStats();
            return success(stats);
        } catch (Exception e) {
            log.error("获取项目统计信息失败", e);
            return error("获取项目统计信息失败：" + e.getMessage());
        }
    }

    @GetMapping("/{id}/stats")
    @Operation(summary = "获取项目详细统计", description = "获取指定项目的详细统计信息")
    public ApiResponse<Object> getProjectDetailStats(
            @Parameter(description = "项目ID") @PathVariable @NotNull Long id) {
        try {
            logOperation("获取项目详细统计", id);
            
            Object stats = projectService.getProjectDetailStats(id);
            return success(stats);
        } catch (Exception e) {
            log.error("获取项目详细统计失败", e);
            return error("获取项目详细统计失败：" + e.getMessage());
        }
    }
}
