# 🌟 爱心救助平台 - 温暖之家

> 一个基于 Spring Boot 3 + Vue 3 的现代化爱心救助平台，集成 AI 智能助手，为需要帮助的人们搭建爱心桥梁。

## 📋 项目概述

**爱心救助平台**是一个全栈 Web 应用程序，旨在为需要帮助的个人和家庭提供一个透明、高效的救助申请和捐赠平台。平台集成了先进的 AI 技术，为用户提供 24/7 智能咨询服务。

### 🎯 核心功能

- **💝 救助项目管理** - 发布、管理和展示各类救助项目
- **💰 在线捐赠系统** - 安全便捷的多渠道捐赠功能
- **📝 救助申请** - 用户可在线提交救助申请
- **🤖 AI 智能助手** - 基于火山引擎豆包 AI 的 24/7 咨询服务
- **👥 用户管理** - 完整的用户注册、登录和权限管理
- **📊 数据统计** - 实时的捐赠和项目统计分析
- **🔐 安全保障** - JWT 认证 + Spring Security 安全框架

## 🏗️ 技术架构

### 后端技术栈
- **框架**: Spring Boot 3.2.1
- **Java 版本**: JDK 21
- **数据库**: MySQL 8.0
- **ORM**: MyBatis Plus 3.5.3.2
- **安全**: Spring Security + JWT
- **API 文档**: Swagger 3 (OpenAPI)
- **AI 集成**: 火山引擎豆包 AI
- **构建工具**: Maven

### 前端技术栈
- **框架**: Vue 3.5.17
- **构建工具**: Vite 4.5.14
- **UI 组件**: Element Plus 2.10.2
- **状态管理**: Pinia 3.0.3
- **路由**: Vue Router 4.5.1
- **HTTP 客户端**: Axios 1.10.0
- **样式**: SCSS

### 数据库设计
- **核心表**: 5个主要业务表
- **用户表** (users) - 用户信息和权限管理
- **项目表** (projects) - 救助项目信息
- **捐赠表** (donations) - 捐赠记录和交易信息
- **对话表** (conversations) - AI 对话记录
- **申请表** (help_requests) - 救助申请信息

## 🚀 快速开始

### 环境要求
- JDK 21+
- Node.js 18+
- MySQL 8.0+
- Maven 3.6+

### 后端启动

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd charity-platform
   ```

2. **配置数据库**
   ```bash
   # 创建数据库
   mysql -u root -p
   CREATE DATABASE charity_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   
   # 导入数据库结构
   mysql -u root -p charity_platform < database/schema.sql
   mysql -u root -p charity_platform < database/init_data.sql
   ```

3. **修改配置文件**
   ```yaml
   # src/main/resources/application.yml
   spring:
     datasource:
       url: ********************************************
       username: root
       password: [你的密码]
   ```

4. **启动后端服务**
   ```bash
   mvn spring-boot:run
   ```

### 前端启动

1. **安装依赖**
   ```bash
   cd frontend
   npm install
   ```

2. **启动开发服务器**
   ```bash
   npm run dev
   ```

### 访问地址
- **前端应用**: http://localhost:3000
- **后端 API**: http://localhost:8080/api
- **API 文档**: http://localhost:8080/swagger-ui/index.html
- **健康检查**: http://localhost:8080/api/test/health

## 📱 功能模块

### 🏠 首页
- 平台介绍和统计数据展示
- 热门救助项目推荐
- AI 助手快速入口
- 用户登录/注册入口

### 💝 救助项目
- 项目列表浏览和搜索
- 项目详情查看
- 在线捐赠功能
- 项目进度跟踪

### 🤖 AI 智能助手
- 24/7 在线咨询服务
- 智能问答和建议
- 热门问题快速回复
- 对话历史记录

### 👤 用户中心
- 个人信息管理
- 捐赠历史查看
- 救助申请管理
- 密码修改

### 🔧 管理后台
- 用户管理
- 项目管理
- 捐赠记录管理
- 救助申请审核
- 数据统计分析

## 🔐 默认账户

### 管理员账户
- **用户名**: admin
- **密码**: admin123
- **权限**: 完整管理权限

### 测试用户
- **用户名**: testuser
- **密码**: admin123
- **权限**: 普通用户权限

## 📊 API 接口

### 核心接口
- `GET /api/test/hello` - 系统状态检查
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `GET /api/projects` - 获取项目列表
- `POST /api/donations` - 创建捐赠
- `POST /api/chat/send` - AI 对话
- `GET /api/admin/dashboard-stats` - 管理员统计

详细 API 文档请访问: http://localhost:8080/swagger-ui/index.html

## 🎨 界面预览

### 主要页面
- **首页** - 温馨的红色主题设计
- **项目列表** - 卡片式项目展示
- **项目详情** - 详细的项目信息和捐赠入口
- **AI 聊天** - 现代化的对话界面
- **管理后台** - 专业的数据管理界面

## 🔧 开发指南

### 项目结构
```
charity-platform/
├── src/main/java/com/example/demo/    # 后端源码
│   ├── controller/                    # 控制器层
│   ├── service/                       # 服务层
│   ├── mapper/                        # 数据访问层
│   ├── entity/                        # 实体类
│   ├── dto/                          # 数据传输对象
│   └── config/                       # 配置类
├── frontend/                         # 前端源码
│   ├── src/
│   │   ├── pages/                    # 页面组件
│   │   ├── components/               # 公共组件
│   │   ├── api/                      # API 接口
│   │   ├── stores/                   # 状态管理
│   │   └── router/                   # 路由配置
│   └── public/                       # 静态资源
├── database/                         # 数据库脚本
└── docs/                            # 项目文档
```

### 开发规范
- 后端遵循 RESTful API 设计规范
- 前端采用 Vue 3 Composition API
- 代码注释完整，便于维护
- 统一的错误处理和日志记录

## 🤝 贡献指南

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🌟 特色亮点

### AI 智能助手
- **技术**: 集成火山引擎豆包 AI
- **功能**: 24/7 智能咨询、问题解答、救助建议
- **特点**: 温暖专业的语调，针对救助平台优化

### 现代化架构
- **前后端分离**: 独立开发和部署
- **响应式设计**: 支持多设备访问
- **安全可靠**: JWT + Spring Security 双重保障
- **高性能**: 数据库优化和缓存机制

### 用户体验
- **直观界面**: Element Plus 组件库，美观易用
- **快速响应**: Vite 构建工具，开发体验优秀
- **实时更新**: 数据实时同步，状态及时反馈

## 🔍 详细功能说明

### 救助项目管理
- **项目发布**: 管理员可发布各类救助项目
- **分类管理**: 医疗、教育、灾难、扶贫四大类别
- **进度跟踪**: 实时显示筹款进度和捐赠人数
- **状态管理**: 进行中、已完成、已关闭状态控制

### 捐赠系统
- **多种支付**: 支持支付宝、微信、银行转账
- **匿名捐赠**: 保护捐赠者隐私
- **留言功能**: 捐赠时可留下鼓励话语
- **收据管理**: 自动生成捐赠凭证

### 救助申请
- **在线申请**: 用户可在线提交救助申请
- **材料上传**: 支持证明材料图片上传
- **审核流程**: 管理员审核，状态实时更新
- **进度查询**: 申请者可随时查看审核进度

## 🛠️ 部署指南

### 生产环境部署

1. **后端部署**
   ```bash
   # 打包应用
   mvn clean package -DskipTests

   # 运行 JAR 文件
   java -jar target/charity-platform-0.0.1-SNAPSHOT.jar
   ```

2. **前端部署**
   ```bash
   # 构建生产版本
   npm run build

   # 部署到 Web 服务器
   cp -r dist/* /var/www/html/
   ```

3. **数据库配置**
   ```sql
   -- 生产环境建议配置
   SET GLOBAL max_connections = 1000;
   SET GLOBAL innodb_buffer_pool_size = 1G;
   ```

### Docker 部署 (可选)
```dockerfile
# Dockerfile 示例
FROM openjdk:21-jdk-slim
COPY target/charity-platform-0.0.1-SNAPSHOT.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java","-jar","/app.jar"]
```

## 🧪 测试指南

### 后端测试
```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=UserServiceTest
```

### 前端测试
```bash
# 运行单元测试
npm run test

# 运行 E2E 测试
npm run test:e2e
```

## 📈 性能优化

### 数据库优化
- 合理的索引设计
- 查询语句优化
- 连接池配置

### 前端优化
- 组件懒加载
- 图片压缩和懒加载
- 代码分割和缓存

### 后端优化
- Redis 缓存集成
- 异步处理
- 数据库连接池优化

## 🔒 安全措施

### 数据安全
- 密码 BCrypt 加密
- SQL 注入防护
- XSS 攻击防护
- CSRF 令牌验证

### 接口安全
- JWT 令牌认证
- 接口访问频率限制
- 权限分级控制
- 敏感数据脱敏

## 📚 学习资源

### 相关技术文档
- [Spring Boot 官方文档](https://spring.io/projects/spring-boot)
- [Vue 3 官方文档](https://vuejs.org/)
- [Element Plus 组件库](https://element-plus.org/)
- [MyBatis Plus 文档](https://baomidou.com/)

### 项目扩展建议
- 集成支付网关 (支付宝、微信支付)
- 添加短信通知功能
- 实现实时消息推送
- 增加数据可视化图表
- 添加移动端 APP

## 📞 联系我们

- **项目类型**: 学生课程设计 / 开源项目
- **技术支持**: 欢迎提交 Issue 或 Pull Request
- **项目地址**: [GitHub 仓库地址]

## 🙏 致谢

感谢以下开源项目和技术社区的支持：
- Spring Boot 团队
- Vue.js 团队
- Element Plus 团队
- MyBatis Plus 团队
- 火山引擎豆包 AI

---

💝 **让爱心传递，让温暖延续** - 爱心救助平台团队

*本项目旨在通过技术手段促进社会公益事业发展，为需要帮助的人们搭建爱心桥梁。*
