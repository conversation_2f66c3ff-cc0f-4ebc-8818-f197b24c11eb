package com.example.demo.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * AI对话请求DTO
 */
@Schema(description = "AI对话请求")
public class ConversationRequestDTO {

    @Schema(description = "会话ID", example = "session_001")
    private String sessionId;

    @Schema(description = "用户消息", example = "你好，我想了解如何申请救助", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "用户消息不能为空")
    @Size(max = 1000, message = "消息长度不能超过1000字符")
    private String message;

    @Schema(description = "对话类型", example = "help_request")
    private String conversationType;

    // Getters and Setters
    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getConversationType() {
        return conversationType;
    }

    public void setConversationType(String conversationType) {
        this.conversationType = conversationType;
    }
}
