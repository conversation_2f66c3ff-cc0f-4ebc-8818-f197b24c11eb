<template>
  <div class="help-request-create">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <el-button @click="goBack" class="back-btn">
            <el-icon>
              <ArrowLeft />
            </el-icon>
            返回
          </el-button>
          <div class="title-section">
            <h1>申请救助</h1>
            <p>请详细填写您的救助申请信息，我们会尽快审核处理</p>
          </div>
        </div>
      </div>

      <!-- 申请表单 -->
      <div class="form-section">
        <el-card>
          <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" v-loading="submitting">
            <!-- 基本信息 -->
            <div class="form-group">
              <h3>基本信息</h3>

              <el-form-item label="申请标题" prop="title">
                <el-input v-model="form.title" placeholder="请简要描述您的救助需求" maxlength="100" show-word-limit />
              </el-form-item>

              <el-form-item label="救助分类" prop="category">
                <el-select v-model="form.category" placeholder="请选择救助分类" style="width: 100%">
                  <el-option label="医疗救助" value="medical" />
                  <el-option label="教育助学" value="education" />
                  <el-option label="灾难救援" value="disaster" />
                  <el-option label="扶贫助困" value="poverty" />
                  <el-option label="其他" value="other" />
                </el-select>
              </el-form-item>

              <el-form-item label="申请金额" prop="requestAmount">
                <el-input-number v-model="form.requestAmount" :min="1" :max="1000000" :precision="2"
                  placeholder="请输入申请金额" style="width: 100%">
                  <template #prepend>¥</template>
                </el-input-number>
              </el-form-item>

              <el-form-item label="紧急程度" prop="urgency">
                <el-radio-group v-model="form.urgency">
                  <el-radio label="low">一般</el-radio>
                  <el-radio label="medium">紧急</el-radio>
                  <el-radio label="high">非常紧急</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>

            <!-- 详细描述 -->
            <div class="form-group">
              <h3>详细描述</h3>

              <el-form-item label="详细说明" prop="description">
                <el-input v-model="form.description" type="textarea" :rows="6" placeholder="请详细描述您的困难情况、救助原因、资金用途等信息"
                  maxlength="2000" show-word-limit />
              </el-form-item>

              <el-form-item label="资金用途" prop="fundUsage">
                <el-input v-model="form.fundUsage" type="textarea" :rows="3" placeholder="请说明申请资金的具体用途和预算分配"
                  maxlength="500" show-word-limit />
              </el-form-item>
            </div>

            <!-- 联系信息 -->
            <div class="form-group">
              <h3>联系信息</h3>

              <el-form-item label="联系人姓名" prop="contactName">
                <el-input v-model="form.contactName" placeholder="请输入联系人姓名" maxlength="50" />
              </el-form-item>

              <el-form-item label="联系电话" prop="contactPhone">
                <el-input v-model="form.contactPhone" placeholder="请输入联系电话" maxlength="20" />
              </el-form-item>

              <el-form-item label="联系地址" prop="contactAddress">
                <el-input v-model="form.contactAddress" placeholder="请输入详细地址" maxlength="200" />
              </el-form-item>
            </div>

            <!-- 证明材料 -->
            <div class="form-group">
              <h3>证明材料</h3>

              <el-form-item label="上传材料">
                <div class="upload-section">
                  <el-upload ref="uploadRef" :file-list="fileList" :on-change="handleFileChange"
                    :on-remove="handleFileRemove" :before-upload="beforeUpload" :auto-upload="false" multiple drag
                    accept=".jpg,.jpeg,.png,.pdf,.doc,.docx">
                    <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                    <div class="el-upload__text">
                      将文件拖到此处，或<em>点击上传</em>
                    </div>
                    <template #tip>
                      <div class="el-upload__tip">
                        支持 JPG、PNG、PDF、DOC 格式，单个文件不超过 10MB
                      </div>
                    </template>
                  </el-upload>

                  <div class="upload-tips">
                    <h4>建议上传以下材料：</h4>
                    <ul>
                      <li>身份证明（身份证、户口本等）</li>
                      <li>困难证明（医院诊断书、贫困证明等）</li>
                      <li>收入证明（工资单、银行流水等）</li>
                      <li>其他相关证明材料</li>
                    </ul>
                  </div>
                </div>
              </el-form-item>
            </div>

            <!-- 提交按钮 -->
            <div class="form-actions">
              <el-button @click="handleReset">重置</el-button>
              <el-button @click="handleSaveDraft">保存草稿</el-button>
              <el-button type="primary" @click="handleSubmit" :loading="submitting">
                提交申请
              </el-button>
            </div>
          </el-form>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { ArrowLeft, UploadFilled } from '@element-plus/icons-vue'
  import { createHelpRequest } from '@/api/helpRequest'
  import { uploadRequestDocument } from '@/api/file'

  const router = useRouter()

  // 响应式数据
  const formRef = ref()
  const uploadRef = ref()
  const submitting = ref(false)
  const fileList = ref([])

  // 表单数据
  const form = reactive({
    title: '',
    category: '',
    requestAmount: null,
    urgency: 'medium',
    description: '',
    fundUsage: '',
    contactName: '',
    contactPhone: '',
    contactAddress: '',
    documents: []
  })

  // 表单验证规则
  const rules = {
    title: [
      { required: true, message: '请输入申请标题', trigger: 'blur' },
      { min: 5, max: 100, message: '标题长度应在 5-100 字符之间', trigger: 'blur' }
    ],
    category: [
      { required: true, message: '请选择救助分类', trigger: 'change' }
    ],
    requestAmount: [
      { required: true, message: '请输入申请金额', trigger: 'blur' },
      { type: 'number', min: 1, message: '申请金额必须大于 0', trigger: 'blur' }
    ],
    description: [
      { required: true, message: '请输入详细说明', trigger: 'blur' },
      { min: 20, max: 2000, message: '详细说明应在 20-2000 字符之间', trigger: 'blur' }
    ],
    fundUsage: [
      { required: true, message: '请说明资金用途', trigger: 'blur' },
      { min: 10, max: 500, message: '资金用途说明应在 10-500 字符之间', trigger: 'blur' }
    ],
    contactName: [
      { required: true, message: '请输入联系人姓名', trigger: 'blur' }
    ],
    contactPhone: [
      { required: true, message: '请输入联系电话', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    contactAddress: [
      { required: true, message: '请输入联系地址', trigger: 'blur' }
    ]
  }

  // 返回上一页
  const goBack = () => {
    router.go(-1)
  }

  // 文件上传前验证
  const beforeUpload = (file) => {
    const allowedTypes = [
      'image/jpeg', 'image/jpg', 'image/png',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ]

    if (!allowedTypes.includes(file.type)) {
      ElMessage.error('只支持 JPG、PNG、PDF、DOC、DOCX 格式的文件')
      return false
    }

    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      ElMessage.error('文件大小不能超过 10MB')
      return false
    }

    return false // 阻止自动上传
  }

  // 文件变化处理
  const handleFileChange = (file, files) => {
    fileList.value = files
  }

  // 文件移除处理
  const handleFileRemove = (file, files) => {
    fileList.value = files
  }

  // 上传文件
  const uploadFiles = async () => {
    const uploadedFiles = []

    for (const file of fileList.value) {
      if (file.raw) {
        try {
          const formData = new FormData()
          formData.append('file', file.raw)

          const response = await uploadRequestDocument(formData)
          if (response && response.filePath) {
            uploadedFiles.push(response.filePath)
          }
        } catch (error) {
          console.error('文件上传失败:', error)
          throw new Error(`文件 ${file.name} 上传失败`)
        }
      }
    }

    return uploadedFiles
  }

  // 提交申请
  const handleSubmit = async () => {
    try {
      await formRef.value.validate()

      await ElMessageBox.confirm(
        '确定要提交救助申请吗？提交后将进入审核流程。',
        '确认提交',
        {
          confirmButtonText: '确定提交',
          cancelButtonText: '取消',
          type: 'info'
        }
      )

      submitting.value = true

      // 上传文件
      let documents = []
      if (fileList.value.length > 0) {
        documents = await uploadFiles()
      }

      // 提交申请
      const requestData = {
        title: form.title,
        description: form.description,
        category: form.category,
        amountNeeded: form.requestAmount, // 字段名映射
        contactPhone: form.contactPhone,
        contactAddress: form.contactAddress,
        proofImages: documents,
        status: 'pending'
      }

      await createHelpRequest(requestData)

      ElMessage.success('救助申请提交成功，请等待审核')
      router.push('/profile?tab=requests')

    } catch (error) {
      if (error !== 'cancel') {
        console.error('提交申请失败:', error)
        ElMessage.error('提交失败：' + (error.message || '请稍后重试'))
      }
    } finally {
      submitting.value = false
    }
  }

  // 保存草稿
  const handleSaveDraft = async () => {
    try {
      submitting.value = true

      const requestData = {
        title: form.title,
        description: form.description,
        category: form.category,
        amountNeeded: form.requestAmount, // 字段名映射
        contactPhone: form.contactPhone,
        contactAddress: form.contactAddress,
        proofImages: [], // 草稿不包含文件
        status: 'draft'
      }

      await createHelpRequest(requestData)
      ElMessage.success('草稿保存成功')

    } catch (error) {
      console.error('保存草稿失败:', error)
      ElMessage.error('保存失败：' + (error.message || '请稍后重试'))
    } finally {
      submitting.value = false
    }
  }

  // 重置表单
  const handleReset = () => {
    formRef.value.resetFields()
    fileList.value = []
  }
</script>

<style scoped>
  .help-request-create {
    min-height: 100vh;
    background: #f5f5f5;
    padding: 24px 0;
  }

  .container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 24px;
  }

  /* 页面头部 */
  .page-header {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .header-content {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .back-btn {
    color: #718096;
  }

  .title-section h1 {
    font-size: 24px;
    font-weight: bold;
    color: #2D3748;
    margin: 0 0 8px 0;
  }

  .title-section p {
    color: #718096;
    margin: 0;
    font-size: 14px;
  }

  /* 表单区域 */
  .form-section .el-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .form-group {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #E2E8F0;
  }

  .form-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }

  .form-group h3 {
    font-size: 18px;
    font-weight: bold;
    color: #2D3748;
    margin: 0 0 20px 0;
    padding-left: 12px;
    border-left: 4px solid #4299E1;
  }

  /* 上传区域 */
  .upload-section {
    width: 100%;
  }

  .upload-tips {
    margin-top: 16px;
    padding: 16px;
    background: #F7FAFC;
    border-radius: 8px;
    border-left: 4px solid #4299E1;
  }

  .upload-tips h4 {
    font-size: 14px;
    font-weight: bold;
    color: #2D3748;
    margin: 0 0 8px 0;
  }

  .upload-tips ul {
    margin: 0;
    padding-left: 20px;
    color: #718096;
    font-size: 13px;
  }

  .upload-tips li {
    margin-bottom: 4px;
  }

  /* 表单操作 */
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    padding-top: 24px;
    border-top: 1px solid #E2E8F0;
    margin-top: 32px;
  }

  /* 表单项样式 */
  .el-form-item {
    margin-bottom: 24px;
  }

  .el-input-number {
    width: 100%;
  }

  .el-radio-group {
    display: flex;
    gap: 24px;
  }

  /* 上传组件样式 */
  :deep(.el-upload-dragger) {
    width: 100%;
    height: 120px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    background: #fafafa;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: border-color 0.3s;
  }

  :deep(.el-upload-dragger:hover) {
    border-color: #4299E1;
  }

  :deep(.el-upload-dragger .el-icon--upload) {
    font-size: 32px;
    color: #c0c4cc;
    margin-bottom: 8px;
  }

  :deep(.el-upload__text) {
    color: #606266;
    font-size: 14px;
  }

  :deep(.el-upload__text em) {
    color: #4299E1;
    font-style: normal;
  }

  :deep(.el-upload__tip) {
    color: #909399;
    font-size: 12px;
    margin-top: 8px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .container {
      padding: 0 16px;
    }

    .page-header {
      padding: 16px;
    }

    .header-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    .form-actions {
      flex-direction: column;
    }

    .form-actions .el-button {
      width: 100%;
    }

    .el-radio-group {
      flex-direction: column;
      gap: 12px;
    }
  }
</style>