package com.example.demo.exception;

/**
 * JWT认证异常
 */
public class JwtAuthenticationException extends BusinessException {

    public JwtAuthenticationException() {
        super(ErrorCode.TOKEN_INVALID);
    }

    public JwtAuthenticationException(String message) {
        super(ErrorCode.TOKEN_INVALID, message);
    }

    public JwtAuthenticationException(ErrorCode errorCode) {
        super(errorCode);
    }

    public JwtAuthenticationException(ErrorCode errorCode, String message) {
        super(errorCode, message);
    }

    public JwtAuthenticationException(Throwable cause) {
        super(ErrorCode.TOKEN_INVALID, cause);
    }

    /**
     * 令牌过期异常
     */
    public static JwtAuthenticationException expired() {
        return new JwtAuthenticationException(ErrorCode.TOKEN_EXPIRED);
    }

    /**
     * 令牌无效异常
     */
    public static JwtAuthenticationException invalid(String reason) {
        return new JwtAuthenticationException(ErrorCode.TOKEN_INVALID, "令牌无效：" + reason);
    }

    /**
     * 令牌缺失异常
     */
    public static JwtAuthenticationException missing() {
        return new JwtAuthenticationException(ErrorCode.UNAUTHORIZED, "缺少访问令牌");
    }

    /**
     * 令牌格式错误异常
     */
    public static JwtAuthenticationException malformed() {
        return new JwtAuthenticationException(ErrorCode.TOKEN_INVALID, "令牌格式错误");
    }
}
