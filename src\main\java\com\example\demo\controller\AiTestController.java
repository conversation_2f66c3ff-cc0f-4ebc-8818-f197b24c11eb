package com.example.demo.controller;

import com.example.demo.config.AiConfig;
import com.example.demo.dto.ApiResponse;
import com.example.demo.service.AiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI测试Controller
 * 用于测试火山引擎豆包AI服务
 */
@Slf4j
@RestController
@RequestMapping("/api/test/ai")
@Tag(name = "AI测试", description = "火山引擎豆包AI服务测试API")
public class AiTestController extends BaseController {

    @Autowired
    private AiService aiService;

    @Autowired
    private AiConfig aiConfig;

    @GetMapping("/status")
    @Operation(summary = "检查AI服务状态", description = "检查AI服务是否可用")
    public ApiResponse<Map<String, Object>> checkStatus() {
        Map<String, Object> status = new HashMap<>();
        
        boolean isAvailable = aiService.isServiceAvailable();
        status.put("available", isAvailable);
        status.put("model", aiConfig.getModel());
        status.put("baseUrl", aiConfig.getBaseUrl());
        status.put("hasApiKey", aiConfig.getApiKey() != null && !aiConfig.getApiKey().isEmpty());
        status.put("apiKeyMasked", maskApiKey(aiConfig.getApiKey()));
        
        if (isAvailable) {
            return success("AI服务可用", status);
        } else {
            return error(60000001,"AI服务不可用，请检查配置");
        }
    }

    @PostMapping("/chat")
    @Operation(summary = "测试AI对话", description = "发送消息测试AI对话功能")
    public ApiResponse<Map<String, Object>> testChat(
            @Parameter(description = "测试消息") @RequestParam String message,
            @Parameter(description = "对话ID") @RequestParam(required = false) Long conversationId) {
        
        try {
            logOperation("测试AI对话", message, conversationId);
            
            if (!aiService.isServiceAvailable()) {
                return error("AI服务不可用，请检查配置");
            }
            
            long startTime = System.currentTimeMillis();
            String response = aiService.chat(message, conversationId);
            long endTime = System.currentTimeMillis();
            
            Map<String, Object> result = new HashMap<>();
            result.put("userMessage", message);
            result.put("aiResponse", response);
            result.put("conversationId", conversationId);
            result.put("responseTime", endTime - startTime);
            result.put("model", aiConfig.getModel());
            
            return success("AI对话测试成功", result);
        } catch (Exception e) {
            log.error("AI对话测试失败", e);
            return error("AI对话测试失败：" + e.getMessage());
        }
    }

    @GetMapping("/welcome")
    @Operation(summary = "获取欢迎消息", description = "获取AI欢迎消息")
    public ApiResponse<String> getWelcome() {
        try {
            String welcomeMessage = aiService.getWelcomeMessage();
            return success("获取欢迎消息成功", welcomeMessage);
        } catch (Exception e) {
            log.error("获取欢迎消息失败", e);
            return error("获取欢迎消息失败：" + e.getMessage());
        }
    }

    @GetMapping("/popular-questions")
    @Operation(summary = "获取热门问题", description = "获取预设的热门问题列表")
    public ApiResponse<List<String>> getPopularQuestions() {
        try {
            List<String> questions = aiService.getPopularQuestions();
            return success("获取热门问题成功", questions);
        } catch (Exception e) {
            log.error("获取热门问题失败", e);
            return error("获取热门问题失败：" + e.getMessage());
        }
    }

    @GetMapping("/config")
    @Operation(summary = "获取AI配置信息", description = "获取当前AI服务配置信息")
    public ApiResponse<Map<String, Object>> getConfig() {
        Map<String, Object> config = new HashMap<>();
        
        config.put("model", aiConfig.getModel());
        config.put("baseUrl", aiConfig.getBaseUrl());
        config.put("systemPrompt", aiConfig.getSystemPrompt());
        config.put("maxHistorySize", aiConfig.getMaxHistorySize());
        config.put("timeoutSeconds", aiConfig.getTimeoutSeconds());
        config.put("maxRetries", aiConfig.getMaxRetries());
        config.put("apiKeyMasked", maskApiKey(aiConfig.getApiKey()));
        
        return success("获取配置信息成功", config);
    }

    @PostMapping("/clear-history")
    @Operation(summary = "清除对话历史", description = "清除指定对话的历史记录")
    public ApiResponse<Void> clearHistory(
            @Parameter(description = "对话ID") @RequestParam Long conversationId) {
        
        try {
            aiService.clearConversationHistory(conversationId);
            return success("对话历史已清除", null);
        } catch (Exception e) {
            log.error("清除对话历史失败", e);
            return error("清除对话历史失败：" + e.getMessage());
        }
    }

    @GetMapping("/history")
    @Operation(summary = "获取对话历史", description = "获取指定对话的历史记录")
    public ApiResponse<List<String>> getHistory(
            @Parameter(description = "对话ID") @RequestParam Long conversationId) {
        
        try {
            List<String> history = aiService.getConversationHistory(conversationId);
            return success("获取对话历史成功", history);
        } catch (Exception e) {
            log.error("获取对话历史失败", e);
            return error("获取对话历史失败：" + e.getMessage());
        }
    }

    @PostMapping("/batch-test")
    @Operation(summary = "批量测试", description = "批量测试AI对话功能")
    public ApiResponse<Map<String, Object>> batchTest() {
        try {
            String[] testMessages = {
                "你好",
                "如何申请救助？",
                "如何进行捐赠？",
                "平台如何保证资金安全？"
            };
            
            Map<String, Object> results = new HashMap<>();
            long totalTime = 0;
            int successCount = 0;
            
            for (int i = 0; i < testMessages.length; i++) {
                try {
                    long startTime = System.currentTimeMillis();
                    String response = aiService.chat(testMessages[i], (long) i);
                    long endTime = System.currentTimeMillis();
                    
                    totalTime += (endTime - startTime);
                    successCount++;
                    
                    results.put("test" + (i + 1), Map.of(
                        "message", testMessages[i],
                        "response", response,
                        "responseTime", endTime - startTime
                    ));
                } catch (Exception e) {
                    results.put("test" + (i + 1), Map.of(
                        "message", testMessages[i],
                        "error", e.getMessage()
                    ));
                }
            }
            
            results.put("summary", Map.of(
                "totalTests", testMessages.length,
                "successCount", successCount,
                "failureCount", testMessages.length - successCount,
                "averageResponseTime", successCount > 0 ? totalTime / successCount : 0
            ));
            
            return success("批量测试完成", results);
        } catch (Exception e) {
            log.error("批量测试失败", e);
            return error("批量测试失败：" + e.getMessage());
        }
    }

    /**
     * 掩码API密钥
     */
    private String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() < 8) {
            return "未配置";
        }
        return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
    }
}
