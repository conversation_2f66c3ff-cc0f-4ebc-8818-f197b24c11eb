package com.example.demo.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.dto.ApiResponse;
import com.example.demo.dto.PageDTO;
import com.example.demo.dto.converter.DTOConverter;
import com.example.demo.dto.request.DonationCreateDTO;
import com.example.demo.dto.request.DonationQueryDTO;
import com.example.demo.dto.response.DonationListDTO;
import com.example.demo.dto.response.DonationResponseDTO;
import com.example.demo.entity.Donation;
import com.example.demo.entity.Project;
import com.example.demo.entity.User;
import com.example.demo.service.DonationService;
import com.example.demo.service.ProjectService;
import com.example.demo.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import org.springframework.util.StringUtils;

/**
 * 捐赠管理Controller
 * 提供捐赠创建、查询、支付处理等API
 */
@Slf4j
@RestController
@RequestMapping("/api/donations")
@Tag(name = "捐赠管理", description = "捐赠创建、查询、支付处理等API")
@Validated
public class DonationController extends BaseController {

    @Autowired
    private DonationService donationService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private UserService userService;

    @PostMapping
    @Operation(summary = "创建捐赠", description = "用户创建捐赠记录")
    public ApiResponse<DonationResponseDTO> createDonation(@Valid @RequestBody DonationCreateDTO createDTO) {
        try {
            Long userId = getCurrentUserId();
            logOperation("创建捐赠", userId, createDTO.getProjectId(), createDTO.getAmount());
            
            // 检查项目是否存在
            Project project = projectService.findById(createDTO.getProjectId());
            if (project == null) {
                return notFound("项目不存在");
            }
            
            // 创建捐赠实体
            Donation donation = new Donation();
            donation.setUserId(userId);
            donation.setProjectId(createDTO.getProjectId());
            donation.setAmount(createDTO.getAmount());
            donation.setPaymentMethod(createDTO.getPaymentMethod());
            donation.setMessage(createDTO.getMessage());
            donation.setIsAnonymous(createDTO.getIsAnonymous() ? 
                    Donation.Anonymous.YES.getCode() : Donation.Anonymous.NO.getCode());
            
            // 创建捐赠
            Donation createdDonation = donationService.createDonation(donation);
            DonationResponseDTO responseDTO = convertToDonationResponseDTO(createdDonation);
            
            return success("捐赠创建成功", responseDTO);
        } catch (Exception e) {
            log.error("创建捐赠失败", e);
            return error("创建捐赠失败：" + e.getMessage());
        }
    }

    @PostMapping("/{id}/pay")
    @Operation(summary = "处理捐赠支付", description = "处理捐赠支付，更新支付状态")
    public ApiResponse<DonationResponseDTO> processDonationPayment(
            @Parameter(description = "捐赠ID") @PathVariable @NotNull Long id,
            @Parameter(description = "交易流水号") @RequestParam @NotNull String transactionId) {
        try {
            Long userId = getCurrentUserId();
            logOperation("处理捐赠支付", userId, id, transactionId);
            
            // 检查捐赠是否存在且属于当前用户
            Donation donation = donationService.findById(id);
            if (donation == null) {
                return notFound("捐赠记录不存在");
            }
            
            if (!donation.getUserId().equals(userId)) {
                return forbidden("无权限操作此捐赠");
            }
            
            // 处理支付
            boolean success = donationService.processDonationPayment(id, transactionId);
            if (success) {
                Donation updatedDonation = donationService.findById(id);
                DonationResponseDTO responseDTO = convertToDonationResponseDTO(updatedDonation);
                return success("支付处理成功", responseDTO);
            } else {
                return error("支付处理失败");
            }
        } catch (Exception e) {
            log.error("处理捐赠支付失败", e);
            return error("支付处理失败：" + e.getMessage());
        }
    }

    @PostMapping("/{id}/cancel")
    @Operation(summary = "取消捐赠", description = "用户取消待支付的捐赠")
    public ApiResponse<Void> cancelDonation(
            @Parameter(description = "捐赠ID") @PathVariable @NotNull Long id) {
        try {
            Long userId = getCurrentUserId();
            logOperation("取消捐赠", userId, id);

            boolean success = donationService.cancelDonation(id, userId);
            if (success) {
                return success("捐赠取消成功", null);
            } else {
                return error("捐赠取消失败");
            }
        } catch (Exception e) {
            log.error("取消捐赠失败", e);
            return error("取消捐赠失败：" + e.getMessage());
        }
    }

    @PutMapping("/{id}/status")
    @Operation(summary = "更新捐赠状态", description = "管理员更新捐赠状态")
    public ApiResponse<DonationResponseDTO> updateDonationStatus(
            @Parameter(description = "捐赠ID") @PathVariable @NotNull Long id,
            @Parameter(description = "新状态") @RequestParam @NotNull String status) {
        try {
            Long adminId = getCurrentUserId();
            logOperation("更新捐赠状态", adminId, id, status);

            // 权限检查：只有管理员可以更新捐赠状态
            if (!isAdmin()) {
                return forbidden("无权限更新捐赠状态");
            }

            // 验证状态值
            if (!isValidDonationStatus(status)) {
                return badRequest("无效的捐赠状态");
            }

            boolean success = donationService.updateDonationStatus(id, status);
            if (success) {
                // 返回更新后的捐赠信息
                Donation donation = donationService.findById(id);
                if (donation != null) {
                    DonationResponseDTO responseDTO = convertToDonationResponseDTO(donation);
                    return success("捐赠状态更新成功", responseDTO);
                } else {
                    return success("捐赠状态更新成功", null);
                }
            } else {
                return error("捐赠状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新捐赠状态失败", e);
            return error("更新捐赠状态失败：" + e.getMessage());
        }
    }

    /**
     * 验证捐赠状态是否有效
     */
    private boolean isValidDonationStatus(String status) {
        return "pending".equals(status) || "success".equals(status) || "failed".equals(status);
    }

    @GetMapping
    @Operation(summary = "分页查询捐赠记录", description = "分页查询捐赠记录，支持条件筛选")
    public ApiResponse<PageDTO<DonationListDTO>> getDonationList(@Valid DonationQueryDTO queryDTO) {
        try {
            Long currentUserId = getCurrentUserId();
            boolean isAdminUser = isAdmin();

            log.info("捐赠记录查询 - 当前用户ID: {}, 是否管理员: {}, 查询参数: {}",
                    currentUserId, isAdminUser, queryDTO);

            // 如果不是管理员，只能查看自己的捐赠记录
            if (!isAdminUser && queryDTO.getUserId() != null && !queryDTO.getUserId().equals(currentUserId)) {
                log.warn("普通用户尝试查看其他用户捐赠记录 - 当前用户: {}, 请求查看用户: {}",
                        currentUserId, queryDTO.getUserId());
                return forbidden("无权限查看其他用户的捐赠记录");
            }

            // 普通用户默认只查看自己的记录
            if (!isAdminUser && queryDTO.getUserId() == null) {
                log.info("普通用户查询，自动设置用户ID为: {}", currentUserId);
                queryDTO.setUserId(currentUserId);
            }
            
            logOperation("分页查询捐赠记录", queryDTO.getCurrent(), queryDTO.getSize(), 
                        queryDTO.getUserId(), queryDTO.getProjectId());
            
            // 转换时间字符串为LocalDateTime
            LocalDateTime startTime = parseDateTime(queryDTO.getStartTime());
            LocalDateTime endTime = parseDateTime(queryDTO.getEndTime());

            Page<Donation> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
            var result = donationService.findDonationsWithConditions(
                    page, queryDTO.getUserId(), queryDTO.getProjectId(),
                    queryDTO.getStatus(), queryDTO.getPaymentMethod(),
                    startTime, endTime);

            return convertPageResult(result, this::convertToDonationListDTO);
        } catch (Exception e) {
            log.error("查询捐赠记录失败", e);
            return error("查询捐赠记录失败：" + e.getMessage());
        }
    }

    @GetMapping("/my-donations")
    @Operation(summary = "获取我的捐赠记录", description = "获取当前用户的捐赠记录（个人中心专用）")
    public ApiResponse<PageDTO<DonationListDTO>> getMyDonations(@Valid DonationQueryDTO queryDTO) {
        try {
            Long currentUserId = getCurrentUserId();

            log.info("个人中心查询捐赠记录 - 当前用户ID: {}, 查询参数: {}", currentUserId, queryDTO);

            // 个人中心只能查看自己的记录，无论是否为管理员
            queryDTO.setUserId(currentUserId);

            logOperation("个人中心查询捐赠记录", queryDTO.getCurrent(), queryDTO.getSize(), currentUserId);

            // 转换时间字符串为LocalDateTime
            LocalDateTime startTime = parseDateTime(queryDTO.getStartTime());
            LocalDateTime endTime = parseDateTime(queryDTO.getEndTime());

            Page<Donation> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
            var result = donationService.findDonationsWithConditions(
                    page, queryDTO.getUserId(), queryDTO.getProjectId(),
                    queryDTO.getStatus(), queryDTO.getPaymentMethod(),
                    startTime, endTime);

            return convertPageResult(result, this::convertToDonationListDTO);
        } catch (Exception e) {
            log.error("查询个人捐赠记录失败", e);
            return error("查询个人捐赠记录失败：" + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取捐赠详情", description = "根据ID获取捐赠记录详情")
    public ApiResponse<DonationResponseDTO> getDonationById(
            @Parameter(description = "捐赠ID") @PathVariable @NotNull Long id) {
        try {
            Long userId = getCurrentUserId();
            logOperation("获取捐赠详情", userId, id);
            
            Donation donation = donationService.findById(id);
            if (donation == null) {
                return notFound("捐赠记录不存在");
            }
            
            // 权限检查：普通用户只能查看自己的捐赠记录
            if (!isAdmin() && !donation.getUserId().equals(userId)) {
                return forbidden("无权限查看此捐赠记录");
            }
            
            DonationResponseDTO responseDTO = convertToDonationResponseDTO(donation);
            return success(responseDTO);
        } catch (Exception e) {
            log.error("获取捐赠详情失败", e);
            return error("获取捐赠详情失败：" + e.getMessage());
        }
    }

    @GetMapping("/recent")
    @Operation(summary = "获取最近捐赠", description = "获取最近的捐赠记录")
    public ApiResponse<List<DonationListDTO>> getRecentDonations(
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "10") Integer limit,
            @Parameter(description = "是否只显示非匿名") @RequestParam(defaultValue = "true") Boolean nonAnonymousOnly) {
        try {
            logOperation("获取最近捐赠", limit, nonAnonymousOnly);
            
            List<Donation> donations = donationService.findRecentDonations(limit, !nonAnonymousOnly);
            List<DonationListDTO> responseList = convertList(donations, this::convertToDonationListDTO);
            
            return success(responseList);
        } catch (Exception e) {
            log.error("获取最近捐赠失败", e);
            return error("获取最近捐赠失败：" + e.getMessage());
        }
    }

    @GetMapping("/large")
    @Operation(summary = "获取大额捐赠", description = "获取大额捐赠记录")
    public ApiResponse<List<DonationListDTO>> getLargeDonations(
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        try {
            logOperation("获取大额捐赠", limit);
            
            List<Donation> donations = donationService.findLargeDonations(limit);
            List<DonationListDTO> responseList = convertList(donations, this::convertToDonationListDTO);
            
            return success(responseList);
        } catch (Exception e) {
            log.error("获取大额捐赠失败", e);
            return error("获取大额捐赠失败：" + e.getMessage());
        }
    }

    @GetMapping("/{id}/certificate")
    @Operation(summary = "生成捐赠证书", description = "为成功的捐赠生成证书")
    public ApiResponse<Object> generateDonationCertificate(
            @Parameter(description = "捐赠ID") @PathVariable @NotNull Long id) {
        try {
            Long userId = getCurrentUserId();
            logOperation("生成捐赠证书", userId, id);
            
            // 检查权限
            if (!donationService.hasDonationPermission(id, userId)) {
                return forbidden("无权限生成此捐赠证书");
            }
            
            Object certificate = donationService.generateDonationCertificate(id);
            return success("证书生成成功", certificate);
        } catch (Exception e) {
            log.error("生成捐赠证书失败", e);
            return error("生成捐赠证书失败：" + e.getMessage());
        }
    }

    @GetMapping("/stats")
    @Operation(summary = "获取捐赠统计信息", description = "获取捐赠相关的统计数据")
    public ApiResponse<Object> getDonationStats() {
        try {
            logOperation("获取捐赠统计信息");
            
            Object stats = donationService.getDonationOverviewStats();
            return success(stats);
        } catch (Exception e) {
            log.error("获取捐赠统计信息失败", e);
            return error("获取捐赠统计信息失败：" + e.getMessage());
        }
    }

    @GetMapping("/user/{userId}/stats")
    @Operation(summary = "获取用户捐赠统计", description = "获取指定用户的捐赠统计信息")
    public ApiResponse<Object> getUserDonationStats(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long userId) {
        try {
            Long currentUserId = getCurrentUserId();
            logOperation("获取用户捐赠统计", currentUserId, userId);

            // 权限检查：普通用户只能查看自己的统计
            if (!isAdmin() && !currentUserId.equals(userId)) {
                return forbidden("无权限查看其他用户的捐赠统计");
            }

            Object stats = donationService.getUserDonationStats(userId);
            return success(stats);
        } catch (Exception e) {
            log.error("获取用户捐赠统计失败", e);
            return error("获取用户捐赠统计失败：" + e.getMessage());
        }
    }

    @PostMapping("/{id}/refund")
    @Operation(summary = "处理捐赠退款", description = "管理员处理捐赠退款")
    public ApiResponse<Void> refundDonation(
            @Parameter(description = "捐赠ID") @PathVariable @NotNull Long id,
            @Parameter(description = "退款原因") @RequestParam @NotNull String reason) {
        try {
            Long adminId = getCurrentUserId();
            logOperation("处理捐赠退款", adminId, id, reason);
            
            // 权限检查：只有管理员可以处理退款
            if (!isAdmin()) {
                return forbidden("无权限处理退款");
            }
            
            boolean success = donationService.refundDonation(id, reason, adminId);
            if (success) {
                return success("退款处理成功", null);
            } else {
                return error("退款处理失败");
            }
        } catch (Exception e) {
            log.error("处理捐赠退款失败", e);
            return error("退款处理失败：" + e.getMessage());
        }
    }

    /**
     * 转换Donation实体为DonationResponseDTO
     */
    private DonationResponseDTO convertToDonationResponseDTO(Donation donation) {
        DonationResponseDTO dto = new DonationResponseDTO();
        dto.setId(donation.getId());
        dto.setUserId(donation.getUserId());
        dto.setProjectId(donation.getProjectId());
        dto.setAmount(donation.getAmount());
        dto.setPaymentMethod(donation.getPaymentMethod());
        dto.setPaymentMethodDescription(DTOConverter.getPaymentMethodDescription(donation.getPaymentMethod()));
        dto.setTransactionId(donation.getTransactionId());
        dto.setMessage(donation.getMessage());
        dto.setIsAnonymous(donation.getIsAnonymous() == Donation.Anonymous.YES.getCode());
        dto.setStatus(donation.getStatus());
        dto.setStatusDescription(DTOConverter.getDonationStatusDescription(donation.getStatus()));
        dto.setDonationTime(donation.getDonationTime());
        
        // 获取用户信息
        try {
            User user = userService.findById(donation.getUserId());
            if (user != null) {
                dto.setUsername(user.getUsername());
                if (dto.getIsAnonymous()) {
                    dto.setDonorName("匿名捐赠者");
                } else {
                    dto.setDonorName(user.getRealName() != null ? user.getRealName() : user.getUsername());
                }
            }
        } catch (Exception e) {
            log.warn("获取捐赠用户信息失败: {}", e.getMessage());
        }
        
        // 获取项目信息
        try {
            Project project = projectService.findById(donation.getProjectId());
            if (project != null) {
                dto.setProjectTitle(project.getTitle());
            }
        } catch (Exception e) {
            log.warn("获取捐赠项目信息失败: {}", e.getMessage());
        }
        
        return dto;
    }

    /**
     * 转换Donation实体为DonationListDTO
     */
    private DonationListDTO convertToDonationListDTO(Donation donation) {
        DonationListDTO dto = new DonationListDTO();
        dto.setId(donation.getId());
        dto.setUserId(donation.getUserId());
        dto.setProjectId(donation.getProjectId());
        dto.setAmount(donation.getAmount());
        dto.setPaymentMethod(donation.getPaymentMethod());
        dto.setPaymentMethodDescription(DTOConverter.getPaymentMethodDescription(donation.getPaymentMethod()));
        dto.setTransactionId(donation.getTransactionId());
        dto.setMessage(donation.getMessage());
        dto.setIsAnonymous(donation.getIsAnonymous() == Donation.Anonymous.YES.getCode());
        dto.setStatus(donation.getStatus());
        dto.setStatusDescription(DTOConverter.getDonationStatusDescription(donation.getStatus()));
        dto.setDonationTime(donation.getDonationTime());
        
        // 获取捐赠者姓名
        try {
            if (dto.getIsAnonymous()) {
                dto.setDonorName("匿名捐赠者");
            } else {
                User user = userService.findById(donation.getUserId());
                if (user != null) {
                    dto.setDonorName(user.getRealName() != null ? user.getRealName() : user.getUsername());
                }
            }
        } catch (Exception e) {
            dto.setDonorName("未知用户");
        }
        
        // 获取项目标题
        try {
            Project project = projectService.findById(donation.getProjectId());
            if (project != null) {
                dto.setProjectTitle(project.getTitle());
            }
        } catch (Exception e) {
            dto.setProjectTitle("未知项目");
        }
        
        return dto;
    }

    /**
     * 解析时间字符串为LocalDateTime
     * 支持ISO 8601格式：2025-06-28T16:00:00.000Z
     */
    private LocalDateTime parseDateTime(String dateTimeStr) {
        if (!StringUtils.hasText(dateTimeStr)) {
            return null;
        }

        try {
            // 处理ISO 8601格式，移除Z后缀并解析
            if (dateTimeStr.endsWith("Z")) {
                dateTimeStr = dateTimeStr.substring(0, dateTimeStr.length() - 1);
            }

            // 支持多种格式
            if (dateTimeStr.contains("T")) {
                // ISO格式：2025-06-28T16:00:00.000 或 2025-06-28T16:00:00
                if (dateTimeStr.contains(".")) {
                    return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"));
                } else {
                    return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));
                }
            } else {
                // 简单日期格式：2025-06-28
                return LocalDateTime.parse(dateTimeStr + "T00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));
            }
        } catch (DateTimeParseException e) {
            log.warn("时间格式解析失败: {}, 错误: {}", dateTimeStr, e.getMessage());
            return null;
        }
    }
}
