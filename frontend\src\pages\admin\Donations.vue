<template>
  <div class="admin-donations">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">捐赠管理</h1>
        <p class="page-description">管理和监控平台的捐赠记录</p>
      </div>
      <div class="header-right">
        <el-button @click="handleRefresh">
          <el-icon>
            <Refresh />
          </el-icon>
          刷新
        </el-button>
        <el-button type="primary" @click="handleExport">
          <el-icon>
            <Download />
          </el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="24">
        <el-col :span="6">
          <el-card class="stats-card total-amount">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon>
                  <Money />
                </el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">¥{{ formatAmount(stats.totalAmount) }}</div>
                <div class="stats-label">总捐赠金额</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card total-count">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon>
                  <Document />
                </el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ stats.totalCount || 0 }}</div>
                <div class="stats-label">总捐赠笔数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card today-amount">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon>
                  <Calendar />
                </el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">¥{{ formatAmount(stats.todayAmount) }}</div>
                <div class="stats-label">今日捐赠</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card avg-amount">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon>
                  <TrendCharts />
                </el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">¥{{ formatAmount(stats.avgAmount) }}</div>
                <div class="stats-label">平均金额</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <el-card>
        <el-form :model="filters" inline>
          <el-form-item label="搜索捐赠">
            <el-input v-model="filters.keyword" placeholder="输入捐赠人姓名或项目名称" style="width: 250px" clearable
              @keyup.enter="handleSearch">
              <template #prefix>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="项目筛选">
            <el-select v-model="filters.projectId" placeholder="选择项目" style="width: 200px" clearable filterable>
              <el-option v-for="project in projectOptions" :key="project.id" :label="project.title"
                :value="project.id" />
            </el-select>
          </el-form-item>

          <el-form-item label="支付方式">
            <el-select v-model="filters.paymentMethod" placeholder="选择支付方式" style="width: 150px" clearable>
              <el-option label="支付宝" value="alipay" />
              <el-option label="微信支付" value="wechat" />
              <el-option label="银行转账" value="bank" />
            </el-select>
          </el-form-item>

          <el-form-item label="捐赠状态">
            <el-select v-model="filters.status" placeholder="选择状态" style="width: 120px" clearable>
              <el-option label="待支付" value="pending" />
              <el-option label="已完成" value="completed" />
              <el-option label="已取消" value="cancelled" />
              <el-option label="已退款" value="refunded" />
            </el-select>
          </el-form-item>

          <el-form-item label="时间范围">
            <el-date-picker v-model="filters.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" style="width: 240px" @change="handleDateChange" />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon>
                <Search />
              </el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 捐赠列表 -->
    <div class="table-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>捐赠列表</span>
            <div class="header-actions">
              <span class="total-count">共 {{ pagination.total }} 笔捐赠</span>
            </div>
          </div>
        </template>

        <el-table :data="donations" v-loading="loading" stripe style="width: 100%">
          <el-table-column prop="id" label="ID" width="80" />

          <el-table-column label="捐赠信息" min-width="280">
            <template #default="{ row }">
              <div class="donation-info">
                <div class="donation-amount">¥{{ formatAmount(row.amount) }}</div>
                <div class="donation-meta">
                  <span class="donor-name">{{ row.isAnonymous ? '匿名用户' : (row.donorName || '未知用户') }}</span>
                  <span class="separator">•</span>
                  <span class="project-name">{{ row.projectTitle || '未知项目' }}</span>
                </div>
                <div v-if="row.message" class="donation-message">{{ row.message }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="支付方式" width="120">
            <template #default="{ row }">
              <el-tag :type="getPaymentType(row.paymentMethod)" size="small">
                {{ row.paymentMethodDescription || getPaymentText(row.paymentMethod) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ row.statusDescription || getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="捐赠时间" width="160">
            <template #default="{ row }">
              {{ formatTime(row.donationTime) }}
            </template>
          </el-table-column>

          <el-table-column label="交易流水" width="140">
            <template #default="{ row }">
              <span v-if="row.transactionId" class="transaction-id">
                {{ row.transactionId }}
              </span>
              <span v-else class="no-transaction">-</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="220" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button text type="primary" size="small" @click="handleView(row)">
                  <el-icon>
                    <View />
                  </el-icon>
                  查看
                </el-button>

                <el-dropdown trigger="click" @command="(command) => handleStatusChange(command, row)">
                  <el-button text type="warning" size="small">
                    <el-icon>
                      <Edit />
                    </el-icon>
                    状态
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="pending" :disabled="row.status === 'pending'">
                        设为待处理
                      </el-dropdown-item>
                      <el-dropdown-item command="success" :disabled="row.status === 'success'">
                        设为成功
                      </el-dropdown-item>
                      <el-dropdown-item command="failed" :disabled="row.status === 'failed'">
                        设为失败
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>

                <el-button v-if="row.status === 'success'" text type="danger" size="small"
                  @click="handleRefund(row)">
                  <el-icon>
                    <RefreshLeft />
                  </el-icon>
                  退款
                </el-button>

                <el-dropdown @command="(command) => handleDropdownCommand(command, row)" trigger="click">
                  <el-button text type="primary" size="small" class="more-btn">
                    <el-icon>
                      <MoreFilled />
                    </el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="user">
                        <el-icon>
                          <User />
                        </el-icon>
                        查看捐赠人
                      </el-dropdown-item>
                      <el-dropdown-item command="project" divided>
                        <el-icon>
                          <Document />
                        </el-icon>
                        查看项目
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination v-model:current-page="pagination.current" v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50, 100]" :total="pagination.total" layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange" @current-change="handlePageChange" />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    Search,
    Refresh,
    Download,
    Money,
    Document,
    Calendar,
    TrendCharts,
    View,
    RefreshLeft,
    MoreFilled,
    User,
    Edit
  } from '@element-plus/icons-vue'
  import {
    getDonationList,
    getDonationStats,
    getProjectList,
    refundDonation,
    updateDonationStatus
  } from '@/api/admin'

  const router = useRouter()

  // 响应式数据
  const loading = ref(false)
  const donations = ref([])
  const projectOptions = ref([])
  const stats = ref({
    totalAmount: 0,
    totalCount: 0,
    todayAmount: 0,
    avgAmount: 0
  })

  // 筛选条件
  const filters = reactive({
    keyword: '',
    projectId: '',
    paymentMethod: '',
    status: '',
    dateRange: null,
    startTime: '',
    endTime: ''
  })

  // 分页信息
  const pagination = reactive({
    current: 1,
    size: 20,
    total: 0
  })

  // 加载捐赠列表
  const loadDonations = async () => {
    try {
      loading.value = true

      const params = {
        current: pagination.current,
        size: pagination.size,
        keyword: filters.keyword || undefined,
        projectId: filters.projectId || undefined,
        paymentMethod: filters.paymentMethod || undefined,
        status: filters.status || undefined,
        startTime: filters.startTime || undefined,
        endTime: filters.endTime || undefined
      }

      const response = await getDonationList(params)

      if (response && response.records) {
        donations.value = response.records
        pagination.total = response.total || 0
      }

    } catch (error) {
      console.error('加载捐赠列表失败:', error)
      ElMessage.error('加载捐赠列表失败')
    } finally {
      loading.value = false
    }
  }

  // 加载统计数据
  const loadStats = async () => {
    try {
      const response = await getDonationStats()
      if (response) {
        stats.value = {
          totalAmount: response.totalAmount || 0,
          totalCount: response.totalCount || 0,
          todayAmount: response.todayAmount || 0,
          avgAmount: response.avgAmount || 0
        }
      }
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  }

  // 加载项目选项
  const loadProjectOptions = async () => {
    try {
      const response = await getProjectList({ current: 1, size: 100 })
      if (response && response.records) {
        projectOptions.value = response.records
      }
    } catch (error) {
      console.error('加载项目选项失败:', error)
    }
  }

  // 搜索处理
  const handleSearch = () => {
    pagination.current = 1
    loadDonations()
  }

  // 重置筛选
  const handleReset = () => {
    filters.keyword = ''
    filters.projectId = ''
    filters.paymentMethod = ''
    filters.status = ''
    filters.dateRange = null
    filters.startTime = ''
    filters.endTime = ''
    pagination.current = 1
    loadDonations()
  }

  // 刷新
  const handleRefresh = () => {
    loadDonations()
    loadStats()
  }

  // 日期范围变化
  const handleDateChange = (dates) => {
    if (dates && dates.length === 2) {
      // 开始时间设为当天00:00:00
      const startDate = new Date(dates[0])
      startDate.setHours(0, 0, 0, 0)
      filters.startTime = startDate.toISOString()

      // 结束时间设为当天23:59:59
      const endDate = new Date(dates[1])
      endDate.setHours(23, 59, 59, 999)
      filters.endTime = endDate.toISOString()
    } else {
      filters.startTime = ''
      filters.endTime = ''
    }
  }

  // 分页处理
  const handlePageChange = (page) => {
    pagination.current = page
    loadDonations()
  }

  const handleSizeChange = (size) => {
    pagination.size = size
    pagination.current = 1
    loadDonations()
  }

  // 查看捐赠详情
  const handleView = (row) => {
    router.push(`/admin/donations/${row.id}`)
  }

  // 处理状态修改
  const handleStatusChange = async (newStatus, row) => {
    try {
      const statusText = {
        'pending': '待处理',
        'success': '成功',
        'failed': '失败'
      }

      await ElMessageBox.confirm(
        `确定要将捐赠"${formatAmount(row.amount)}元"的状态修改为"${statusText[newStatus]}"吗？`,
        '确认修改状态',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )

      const response = await updateDonationStatus(row.id, newStatus)
      ElMessage.success('捐赠状态修改成功')

      // 刷新列表
      await loadDonations()

    } catch (error) {
      if (error === 'cancel') {
        return
      }
      console.error('修改捐赠状态失败:', error)
      ElMessage.error('修改捐赠状态失败: ' + (error.response?.data?.message || error.message))
    }
  }

  // 处理退款
  const handleRefund = async (row) => {
    try {
      const { value: reason } = await ElMessageBox.prompt(
        `确定要为捐赠"${formatAmount(row.amount)}元"办理退款吗？请输入退款原因：`,
        '确认退款',
        {
          confirmButtonText: '确定退款',
          cancelButtonText: '取消',
          inputPlaceholder: '请输入退款原因',
          inputValidator: (value) => {
            if (!value || value.trim().length === 0) {
              return '请输入退款原因'
            }
            return true
          }
        }
      )

      await refundDonation(row.id, reason)
      ElMessage.success('退款处理成功')
      loadDonations() // 重新加载列表

    } catch (error) {
      if (error !== 'cancel') {
        console.error('处理退款失败:', error)
        if (error.message) {
          ElMessage.error(error.message)
        } else {
          ElMessage.error('退款处理失败')
        }
      }
    }
  }

  // 导出数据
  const handleExport = () => {
    ElMessage.info('导出功能开发中...')
  }

  // 下拉菜单操作
  const handleDropdownCommand = (command, row) => {
    console.log('下拉菜单操作:', command, row)
    switch (command) {
      case 'user':
        console.log('查看用户:', row.userId, row.isAnonymous)
        if (row.userId && !row.isAnonymous) {
          console.log('跳转到用户详情:', `/admin/users/${row.userId}`)
          router.push(`/admin/users/${row.userId}`)
        } else {
          ElMessage.warning('匿名用户无法查看详情')
        }
        break
      case 'project':
        console.log('查看项目:', row.projectId)
        if (row.projectId) {
          console.log('跳转到项目详情:', `/admin/projects/${row.projectId}`)
          router.push(`/admin/projects/${row.projectId}`)
        } else {
          ElMessage.warning('项目信息不存在')
        }
        break
    }
  }

  // 工具函数
  const getPaymentType = (paymentMethod) => {
    const typeMap = {
      alipay: 'primary',
      wechat: 'success',
      bank: 'warning'
    }
    return typeMap[paymentMethod] || ''
  }

  const getPaymentText = (paymentMethod) => {
    const textMap = {
      alipay: '支付宝',
      wechat: '微信支付',
      bank: '银行转账'
    }
    return textMap[paymentMethod] || paymentMethod
  }

  const getStatusType = (status) => {
    const typeMap = {
      pending: 'warning',
      success: 'success',
      completed: 'success',
      cancelled: 'info',
      refunded: 'danger'
    }
    return typeMap[status] || ''
  }

  const getStatusText = (status) => {
    const textMap = {
      pending: '待支付',
      success: '已完成',
      completed: '已完成',
      cancelled: '已取消',
      refunded: '已退款'
    }
    return textMap[status] || status
  }

  const formatTime = (timeString) => {
    if (!timeString) return '未知'
    return new Date(timeString).toLocaleString('zh-CN')
  }

  const formatAmount = (amount) => {
    if (!amount) return '0'
    return Number(amount).toLocaleString()
  }

  // 页面初始化
  onMounted(() => {
    // 检查URL参数，预设筛选条件
    const route = useRoute()
    if (route.query.projectId) {
      filters.projectId = route.query.projectId
    }

    loadDonations()
    loadStats()
    loadProjectOptions()
  })
</script>

<style scoped>
  .admin-donations {
    padding: 24px;
    background: #f5f5f5;
    min-height: 100vh;
  }

  /* 页面头部 */
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .page-title {
    font-size: 24px;
    font-weight: bold;
    color: #2D3748;
    margin: 0 0 8px 0;
  }

  .page-description {
    color: #718096;
    margin: 0;
    font-size: 14px;
  }

  .header-right {
    display: flex;
    gap: 12px;
  }

  /* 统计区域 */
  .stats-section {
    margin-bottom: 24px;
  }

  .stats-card {
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
  }

  .stats-card:hover {
    transform: translateY(-2px);
  }

  .stats-content {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
  }

  .stats-card.total-amount .stats-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .stats-card.total-count .stats-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  .stats-card.today-amount .stats-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .stats-card.avg-amount .stats-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }

  .stats-info {
    flex: 1;
  }

  .stats-value {
    font-size: 24px;
    font-weight: bold;
    color: #2D3748;
    margin-bottom: 4px;
  }

  .stats-label {
    font-size: 14px;
    color: #718096;
  }

  /* 筛选区域 */
  .filter-section {
    margin-bottom: 24px;
  }

  .filter-section .el-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  /* 表格区域 */
  .table-section .el-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .total-count {
    color: #718096;
    font-size: 14px;
  }

  /* 捐赠信息 */
  .donation-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .donation-amount {
    font-size: 16px;
    font-weight: bold;
    color: #E53E3E;
  }

  .donation-meta {
    font-size: 12px;
    color: #718096;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .separator {
    color: #CBD5E0;
  }

  .project-name {
    color: #4299E1;
  }

  .donation-message {
    font-size: 12px;
    color: #2D3748;
    background: #F7FAFC;
    padding: 4px 8px;
    border-radius: 4px;
    border-left: 3px solid #4299E1;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 交易流水 */
  .transaction-id {
    font-family: monospace;
    font-size: 12px;
    color: #2D3748;
    background: #F7FAFC;
    padding: 2px 6px;
    border-radius: 4px;
  }

  .no-transaction {
    color: #A0AEC0;
    font-style: italic;
  }

  /* 操作按钮 */
  .action-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
  }

  .more-btn {
    padding: 4px 8px;
  }

  /* 分页 */
  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .admin-donations {
      padding: 16px;
    }

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .header-right {
      width: 100%;
      justify-content: flex-end;
    }

    .stats-section .el-col {
      margin-bottom: 16px;
    }

    .filter-section .el-form {
      flex-direction: column;
      align-items: stretch;
    }

    .filter-section .el-form-item {
      margin-right: 0;
      margin-bottom: 16px;
    }

    .action-buttons {
      flex-direction: column;
      gap: 4px;
    }

    .donation-message {
      max-width: 150px;
    }
  }
</style>