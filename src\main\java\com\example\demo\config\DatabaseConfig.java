package com.example.demo.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 数据库配置类
 * 用于管理数据库相关配置
 */
@Configuration
public class DatabaseConfig {

    /**
     * MyBatis-Plus分页插件配置
     * 必须配置此插件，否则分页查询不会生效
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 添加分页插件
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor(DbType.MYSQL);

        // 设置分页参数
        paginationInterceptor.setMaxLimit(500L); // 单页最大限制数量，默认500条，-1不受限制
        paginationInterceptor.setOverflow(false); // 溢出总页数后是否进行处理，默认false
        paginationInterceptor.setOptimizeJoin(true); // 是否优化JOIN查询，默认true

        interceptor.addInnerInterceptor(paginationInterceptor);

        return interceptor;
    }

}
