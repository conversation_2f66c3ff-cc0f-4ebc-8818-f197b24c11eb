package com.example.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 用户Mapper接口
 * 继承MyBatis Plus的BaseMapper，提供基础的CRUD操作
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名查询用户
     * @param username 用户名
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE username = #{username}")
    User findByUsername(@Param("username") String username);

    /**
     * 根据邮箱查询用户
     * @param email 邮箱
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE email = #{email}")
    User findByEmail(@Param("email") String email);

    /**
     * 根据角色查询用户列表
     * @param role 角色
     * @return 用户列表
     */
    @Select("SELECT * FROM users WHERE role = #{role} AND status = 1 ORDER BY create_time DESC")
    List<User> findByRole(@Param("role") String role);

    /**
     * 分页查询用户列表
     * @param page 分页参数
     * @param role 角色（可选）
     * @param status 状态（可选）
     * @param keyword 关键词搜索（用户名或真实姓名）
     * @return 分页用户列表
     */
    @Select("<script>" +
            "SELECT * FROM users WHERE 1=1 " +
            "<if test='role != null and role != \"\"'> AND role = #{role} </if>" +
            "<if test='status != null'> AND status = #{status} </if>" +
            "<if test='keyword != null and keyword != \"\"'> " +
            "AND (username LIKE CONCAT('%', #{keyword}, '%') OR real_name LIKE CONCAT('%', #{keyword}, '%')) " +
            "</if>" +
            "ORDER BY create_time DESC" +
            "</script>")
    IPage<User> findUsersWithConditions(Page<User> page, 
                                       @Param("role") String role, 
                                       @Param("status") Integer status, 
                                       @Param("keyword") String keyword);

    /**
     * 统计用户数量
     * @param role 角色（可选）
     * @param status 状态（可选）
     * @return 用户数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM users WHERE 1=1 " +
            "<if test='role != null and role != \"\"'> AND role = #{role} </if>" +
            "<if test='status != null'> AND status = #{status} </if>" +
            "</script>")
    Long countUsers(@Param("role") String role, @Param("status") Integer status);

    /**
     * 检查用户名是否存在
     * @param username 用户名
     * @param excludeId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM users WHERE username = #{username} " +
            "<if test='excludeId != null'> AND id != #{excludeId} </if>" +
            "</script>")
    Long checkUsernameExists(@Param("username") String username, @Param("excludeId") Long excludeId);

    /**
     * 检查邮箱是否存在
     * @param email 邮箱
     * @param excludeId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM users WHERE email = #{email} " +
            "<if test='excludeId != null'> AND id != #{excludeId} </if>" +
            "</script>")
    Long checkEmailExists(@Param("email") String email, @Param("excludeId") Long excludeId);

    /**
     * 更新用户状态
     * @param id 用户ID
     * @param status 新状态
     * @return 影响行数
     */
    @Update("UPDATE users SET status = #{status}, update_time = NOW() WHERE id = #{id}")
    int updateUserStatus(@Param("id") Long id, @Param("status") Integer status);
}
