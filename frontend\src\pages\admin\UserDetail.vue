<template>
  <div class="user-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" class="back-btn">
          <el-icon>
            <ArrowLeft />
          </el-icon>
          返回用户列表
        </el-button>
        <div class="title-section">
          <h1 class="page-title">用户详情</h1>
          <p class="page-description">查看用户详细信息和统计数据</p>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="handleRefresh">
          <el-icon>
            <Refresh />
          </el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <div v-loading="loading" class="content-wrapper">
      <!-- 用户基本信息 -->
      <div class="info-section">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
              <div class="header-actions">
                <el-tag :type="getStatusType(userInfo.status)" size="large">
                  {{ getStatusText(userInfo.status) }}
                </el-tag>
              </div>
            </div>
          </template>

          <div class="user-profile">
            <div class="avatar-section">
              <el-avatar :src="userInfo.avatar" :size="80" class="user-avatar">
                <el-icon>
                  <User />
                </el-icon>
              </el-avatar>
              <div class="avatar-info">
                <h2 class="username">{{ userInfo.username }}</h2>
                <el-tag :type="getRoleType(userInfo.role)" size="small">
                  {{ getRoleText(userInfo.role) }}
                </el-tag>
              </div>
            </div>

            <div class="info-grid">
              <div class="info-item">
                <label>用户ID</label>
                <span>{{ userInfo.id }}</span>
              </div>
              <div class="info-item">
                <label>真实姓名</label>
                <span>{{ userInfo.realName || '未设置' }}</span>
              </div>
              <div class="info-item">
                <label>邮箱地址</label>
                <span>{{ userInfo.email }}</span>
              </div>
              <div class="info-item">
                <label>手机号码</label>
                <span>{{ userInfo.phone || '未设置' }}</span>
              </div>
              <div class="info-item">
                <label>注册时间</label>
                <span>{{ formatTime(userInfo.createTime) }}</span>
              </div>
              <div class="info-item">
                <label>最后登录</label>
                <span>{{ formatTime(userInfo.lastLoginTime) }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-card class="stats-card">
              <div class="stats-item">
                <div class="stats-icon donation">
                  <el-icon>
                    <Money />
                  </el-icon>
                </div>
                <div class="stats-content">
                  <div class="stats-value">{{ userStats.donationCount || 0 }}</div>
                  <div class="stats-label">捐赠次数</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="stats-card">
              <div class="stats-item">
                <div class="stats-icon amount">
                  <el-icon>
                    <Coin />
                  </el-icon>
                </div>
                <div class="stats-content">
                  <div class="stats-value">¥{{ formatAmount(userStats.totalDonation) }}</div>
                  <div class="stats-label">捐赠总额</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="stats-card">
              <div class="stats-item">
                <div class="stats-icon request">
                  <el-icon>
                    <Document />
                  </el-icon>
                </div>
                <div class="stats-content">
                  <div class="stats-value">{{ userStats.helpRequestCount || 0 }}</div>
                  <div class="stats-label">申请次数</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 操作区域 -->
      <div class="actions-section">
        <el-card>
          <template #header>
            <span>管理操作</span>
          </template>

          <div class="action-buttons">
            <el-button :type="userInfo.status === 1 ? 'danger' : 'success'" @click="handleToggleStatus">
              <el-icon>
                <Switch v-if="userInfo.status === 1" />
                <VideoPlay v-else />
              </el-icon>
              {{ userInfo.status === 1 ? '禁用用户' : '启用用户' }}
            </el-button>

            <el-button type="warning" @click="handleResetPassword">
              <el-icon>
                <Key />
              </el-icon>
              重置密码
            </el-button>

            <el-button @click="handleViewDonations">
              <el-icon>
                <Money />
              </el-icon>
              查看捐赠记录
            </el-button>

            <el-button @click="handleViewRequests">
              <el-icon>
                <Document />
              </el-icon>
              查看申请记录
            </el-button>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    ArrowLeft,
    Refresh,
    User,
    Money,
    Coin,
    Document,
    Switch,
    VideoPlay,
    Key
  } from '@element-plus/icons-vue'
  import {
    getUserById,
    updateUserStatus,
    resetUserPassword,
    getUserDonations,
    getUserHelpRequests
  } from '@/api/admin'

  const route = useRoute()
  const router = useRouter()

  // 响应式数据
  const loading = ref(false)
  const userInfo = ref({})
  const userStats = ref({})

  // 获取用户ID
  const userId = route.params.id

  // 加载用户详情
  const loadUserDetail = async () => {
    try {
      loading.value = true

      const response = await getUserById(userId)
      if (response) {
        userInfo.value = response

        // 模拟统计数据 - 实际项目中应该从API获取
        userStats.value = {
          donationCount: Math.floor(Math.random() * 20),
          totalDonation: Math.floor(Math.random() * 10000),
          helpRequestCount: Math.floor(Math.random() * 5)
        }
      }

    } catch (error) {
      console.error('加载用户详情失败:', error)
      ElMessage.error('加载用户详情失败')
    } finally {
      loading.value = false
    }
  }

  // 返回用户列表
  const goBack = () => {
    router.push('/admin/users')
  }

  // 刷新
  const handleRefresh = () => {
    loadUserDetail()
  }

  // 切换用户状态
  const handleToggleStatus = async () => {
    const action = userInfo.value.status === 1 ? '禁用' : '启用'
    const newStatus = userInfo.value.status === 1 ? 0 : 1

    try {
      await ElMessageBox.confirm(
        `确定要${action}用户"${userInfo.value.username}"吗？`,
        `确认${action}`,
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await updateUserStatus(userInfo.value.id, newStatus)
      ElMessage.success(`用户${action}成功`)
      loadUserDetail()

    } catch (error) {
      if (error !== 'cancel') {
        console.error(`${action}用户失败:`, error)
        if (error.message) {
          ElMessage.error(error.message)
        }
      }
    }
  }

  // 重置密码
  const handleResetPassword = async () => {
    try {
      await ElMessageBox.confirm(
        `确定要重置用户"${userInfo.value.username}"的密码吗？\n密码将重置为：12345678`,
        '确认重置密码',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await resetUserPassword(userInfo.value.id)
      ElMessage.success('密码重置成功，新密码为：12345678')

    } catch (error) {
      if (error !== 'cancel') {
        console.error('重置密码失败:', error)
        if (error.message) {
          ElMessage.error(error.message)
        }
      }
    }
  }

  // 查看捐赠记录
  const handleViewDonations = async () => {
    try {
      loading.value = true
      const response = await getUserDonations(userInfo.value.id, {
        current: 1,
        size: 10
      })

      console.log('捐赠记录API响应:', response)

      // 处理不同的响应格式
      let records = []
      let totalCount = 0

      if (response) {
        if (response.records) {
          // 分页格式
          records = response.records
          totalCount = response.total || records.length
        } else if (Array.isArray(response)) {
          // 直接数组格式
          records = response
          totalCount = records.length
        }
      }

      if (records && records.length > 0) {
        // 显示捐赠记录列表
        const donationList = records.map(donation =>
          `项目：${donation.projectTitle || '未知项目'} - 金额：¥${donation.amount} - 时间：${new Date(donation.donationTime).toLocaleString()}`
        ).join('\n')

        await ElMessageBox.alert(
          donationList,
          `${userInfo.value.username} 的捐赠记录 (共${totalCount}条)`,
          {
            confirmButtonText: '确定',
            type: 'info'
          }
        )
      } else {
        ElMessage.info('该用户暂无捐赠记录')
      }
    } catch (error) {
      console.error('获取捐赠记录失败:', error)
      ElMessage.error('获取捐赠记录失败')
    } finally {
      loading.value = false
    }
  }

  // 查看申请记录
  const handleViewRequests = async () => {
    try {
      loading.value = true
      const response = await getUserHelpRequests(userInfo.value.id, {
        current: 1,
        size: 10
      })

      console.log('申请记录API响应:', response)

      // 处理不同的响应格式
      let records = []
      let totalCount = 0

      if (response) {
        if (response.records) {
          // 分页格式
          records = response.records
          totalCount = response.total || records.length
        } else if (Array.isArray(response)) {
          // 直接数组格式
          records = response
          totalCount = records.length
        }
      }

      if (records && records.length > 0) {
        // 显示申请记录列表
        const requestList = records.map(request =>
          `标题：${request.title} - 状态：${getRequestStatusText(request.status)} - 时间：${new Date(request.createTime).toLocaleString()}`
        ).join('\n')

        await ElMessageBox.alert(
          requestList,
          `${userInfo.value.username} 的申请记录 (共${totalCount}条)`,
          {
            confirmButtonText: '确定',
            type: 'info'
          }
        )
      } else {
        ElMessage.info('该用户暂无申请记录')
      }
    } catch (error) {
      console.error('获取申请记录失败:', error)
      ElMessage.error('获取申请记录失败')
    } finally {
      loading.value = false
    }
  }

  // 工具函数
  const getRoleType = (role) => {
    const typeMap = {
      admin: 'danger',
      user: 'primary'
    }
    return typeMap[role] || ''
  }

  const getRoleText = (role) => {
    const textMap = {
      admin: '管理员',
      user: '普通用户'
    }
    return textMap[role] || role
  }

  const getStatusType = (status) => {
    return status === 1 ? 'success' : 'danger'
  }

  const getStatusText = (status) => {
    return status === 1 ? '正常' : '禁用'
  }

  const formatTime = (timeString) => {
    if (!timeString) return '未知'
    return new Date(timeString).toLocaleString('zh-CN')
  }

  const formatAmount = (amount) => {
    if (!amount) return '0'
    return amount.toLocaleString()
  }

  const getRequestStatusText = (status) => {
    const statusMap = {
      pending: '待审核',
      approved: '已通过',
      rejected: '已拒绝',
      completed: '已完成'
    }
    return statusMap[status] || status
  }

  // 页面初始化
  onMounted(() => {
    loadUserDetail()
  })
</script>

<style scoped>
  .user-detail {
    padding: 24px;
    background: #f5f5f5;
    min-height: 100vh;
  }

  /* 页面头部 */
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .back-btn {
    color: #718096;
  }

  .title-section {
    flex: 1;
  }

  .page-title {
    font-size: 24px;
    font-weight: bold;
    color: #2D3748;
    margin: 0 0 8px 0;
  }

  .page-description {
    color: #718096;
    margin: 0;
    font-size: 14px;
  }

  /* 内容区域 */
  .content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  /* 信息区域 */
  .info-section .el-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .user-profile {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .avatar-section {
    display: flex;
    align-items: center;
    gap: 16px;
    padding-bottom: 24px;
    border-bottom: 1px solid #E2E8F0;
  }

  .avatar-info {
    flex: 1;
  }

  .username {
    font-size: 20px;
    font-weight: bold;
    color: #2D3748;
    margin: 0 0 8px 0;
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .info-item label {
    font-size: 12px;
    color: #718096;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .info-item span {
    font-size: 14px;
    color: #2D3748;
    font-weight: 500;
  }

  /* 统计区域 */
  .stats-section {
    margin-bottom: 24px;
  }

  .stats-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: none;
  }

  .stats-item {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
  }

  .stats-icon.donation {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .stats-icon.amount {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  .stats-icon.request {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .stats-content {
    flex: 1;
  }

  .stats-value {
    font-size: 24px;
    font-weight: bold;
    color: #2D3748;
    margin-bottom: 4px;
  }

  .stats-label {
    font-size: 14px;
    color: #718096;
  }

  /* 操作区域 */
  .actions-section .el-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .action-buttons {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
  }

  .action-buttons .el-button {
    flex: 1;
    min-width: 140px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .user-detail {
      padding: 16px;
    }

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .header-left {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    .avatar-section {
      flex-direction: column;
      text-align: center;
    }

    .info-grid {
      grid-template-columns: 1fr;
    }

    .action-buttons {
      flex-direction: column;
    }

    .action-buttons .el-button {
      flex: none;
      width: 100%;
    }
  }
</style>