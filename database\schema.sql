-- 爱心救助平台数据库设计
-- 数据库名称: charity_platform
-- 字符集: utf8mb4
-- 排序规则: utf8mb4_unicode_ci

-- 创建数据库
CREATE DATABASE IF NOT EXISTS charity_platform 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE charity_platform;

-- 1. 用户表 (users)
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    real_name VARCHAR(50) COMMENT '真实姓名',
    avatar VARCHAR(255) COMMENT '头像URL',
    role ENUM('user', 'admin') DEFAULT 'user' COMMENT '角色: user-普通用户, admin-管理员',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-正常',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role)
) COMMENT '用户表';

-- 2. 救助项目表 (projects)
CREATE TABLE projects (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '项目ID',
    title VARCHAR(200) NOT NULL COMMENT '项目标题',
    description TEXT COMMENT '项目描述',
    category ENUM('medical', 'education', 'disaster', 'poverty') NOT NULL COMMENT '项目分类: medical-医疗, education-教育, disaster-灾难, poverty-扶贫',
    target_amount DECIMAL(12,2) NOT NULL COMMENT '目标金额',
    current_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT '当前筹集金额',
    images TEXT COMMENT '项目图片(JSON格式存储多张图片URL)',
    contact_info VARCHAR(500) COMMENT '联系信息',
    status ENUM('active', 'completed', 'closed') DEFAULT 'active' COMMENT '状态: active-进行中, completed-已完成, closed-已关闭',
    admin_id BIGINT NOT NULL COMMENT '发布管理员ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) COMMENT '救助项目表';

-- 3. 捐赠记录表 (donations)
CREATE TABLE donations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '捐赠ID',
    user_id BIGINT NOT NULL COMMENT '捐赠用户ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    amount DECIMAL(10,2) NOT NULL COMMENT '捐赠金额',
    payment_method ENUM('alipay', 'wechat', 'bank') DEFAULT 'alipay' COMMENT '支付方式: alipay-支付宝, wechat-微信, bank-银行转账',
    transaction_id VARCHAR(100) COMMENT '交易流水号',
    message VARCHAR(500) COMMENT '捐赠留言',
    is_anonymous TINYINT DEFAULT 0 COMMENT '是否匿名: 0-否, 1-是',
    status ENUM('pending', 'success', 'failed') DEFAULT 'pending' COMMENT '状态: pending-待支付, success-成功, failed-失败',
    donation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '捐赠时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_project_id (project_id),
    INDEX idx_donation_time (donation_time),
    INDEX idx_status (status)
) COMMENT '捐赠记录表';

-- 4. AI对话记录表 (conversations)
CREATE TABLE conversations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '对话ID',
    user_id BIGINT COMMENT '用户ID(可为空，支持游客对话)',
    session_id VARCHAR(100) NOT NULL COMMENT '会话ID',
    message TEXT NOT NULL COMMENT '用户消息',
    response TEXT NOT NULL COMMENT 'AI回复',
    conversation_type ENUM('general', 'donation', 'help_request', 'project_info') DEFAULT 'general' COMMENT '对话类型: general-通用, donation-捐赠咨询, help_request-求助咨询, project_info-项目信息',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_conversation_type (conversation_type),
    INDEX idx_create_time (create_time)
) COMMENT 'AI对话记录表';

-- 5. 救助申请表 (help_requests)
CREATE TABLE help_requests (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '申请ID',
    user_id BIGINT NOT NULL COMMENT '申请用户ID',
    title VARCHAR(200) NOT NULL COMMENT '申请标题',
    description TEXT NOT NULL COMMENT '详细描述',
    category ENUM('medical', 'education', 'disaster', 'poverty') NOT NULL COMMENT '申请分类',
    amount_needed DECIMAL(10,2) COMMENT '所需金额',
    contact_phone VARCHAR(20) NOT NULL COMMENT '联系电话',
    contact_address VARCHAR(500) COMMENT '联系地址',
    proof_images TEXT COMMENT '证明材料图片(JSON格式)',
    status ENUM('pending', 'approved', 'rejected', 'processing') DEFAULT 'pending' COMMENT '状态: pending-待审核, approved-已通过, rejected-已拒绝, processing-处理中',
    admin_response TEXT COMMENT '管理员回复',
    admin_id BIGINT COMMENT '处理管理员ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) COMMENT '救助申请表';

-- 创建触发器：更新项目当前金额
DELIMITER //
CREATE TRIGGER update_project_amount 
AFTER INSERT ON donations 
FOR EACH ROW
BEGIN
    IF NEW.status = 'success' THEN
        UPDATE projects 
        SET current_amount = current_amount + NEW.amount 
        WHERE id = NEW.project_id;
    END IF;
END//
DELIMITER ;
