package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户实体类
 * 对应数据库表: users
 */
@Data
@TableName("users")
@Schema(description = "用户实体")
public class User {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "用户ID", example = "1")
    private Long id;

    @TableField("username")
    @Schema(description = "用户名", example = "testuser")
    private String username;

    @TableField("password")
    @Schema(description = "密码(加密)", example = "$2a$10$...")
    private String password;

    @TableField("email")
    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @TableField("phone")
    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    @TableField("real_name")
    @Schema(description = "真实姓名", example = "张三")
    private String realName;

    @TableField("avatar")
    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    @TableField("role")
    @Schema(description = "角色: user-普通用户, admin-管理员", example = "user")
    private String role;

    @TableField("status")
    @Schema(description = "状态: 0-禁用, 1-正常", example = "1")
    private Integer status;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 用户角色枚举
     */
    public enum Role {
        USER("user", "普通用户"),
        ADMIN("admin", "管理员");

        private final String code;
        private final String description;

        Role(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 用户状态枚举
     */
    public enum Status {
        DISABLED(0, "禁用"),
        NORMAL(1, "正常");

        private final Integer code;
        private final String description;

        Status(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
