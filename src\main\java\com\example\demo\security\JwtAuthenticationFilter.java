package com.example.demo.security;

import com.example.demo.config.JwtProperties;
import com.example.demo.exception.JwtAuthenticationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * JWT认证过滤器
 * 拦截请求并验证JWT令牌
 */
@Slf4j
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Autowired
    private CustomUserDetailsService userDetailsService;

    @Autowired
    private JwtProperties jwtProperties;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        try {
            String token = extractTokenFromRequest(request);

            if (StringUtils.hasText(token) && jwtTokenProvider.validateToken(token)) {
                // 确保是访问令牌
                if (!jwtTokenProvider.isAccessToken(token)) {
                    log.warn("使用了非访问令牌进行认证: {}", request.getRequestURI());
                    filterChain.doFilter(request, response);
                    return;
                }
                
                Long userId = jwtTokenProvider.getUserIdFromToken(token);
                
                // 加载用户详情
                UserDetails userDetails = userDetailsService.loadUserById(userId);
                
                // 创建认证对象
                UsernamePasswordAuthenticationToken authentication = 
                    new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                
                // 设置到安全上下文
                SecurityContextHolder.getContext().setAuthentication(authentication);
                
                log.debug("用户认证成功: userId={}, username={}", userId, userDetails.getUsername());
            }
        } catch (JwtAuthenticationException e) {
            log.warn("JWT认证失败: {}, URI: {}", e.getMessage(), request.getRequestURI());
            // 清除安全上下文
            SecurityContextHolder.clearContext();
        } catch (Exception e) {
            log.error("JWT认证过程中发生异常: {}, URI: {}", e.getMessage(), request.getRequestURI());
            // 清除安全上下文
            SecurityContextHolder.clearContext();
        }
        
        filterChain.doFilter(request, response);
    }

    /**
     * 从请求中提取JWT令牌
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        // 从请求头中获取令牌
        String authHeader = request.getHeader(jwtProperties.getHeader());
        if (StringUtils.hasText(authHeader)) {
            return jwtTokenProvider.extractTokenFromHeader(authHeader);
        }
        
        // 从请求参数中获取令牌（用于某些特殊场景，如WebSocket）
        String tokenParam = request.getParameter("token");
        if (StringUtils.hasText(tokenParam)) {
            return tokenParam;
        }
        
        return null;
    }

    /**
     * 判断是否跳过JWT验证
     */
    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        String path = request.getRequestURI();
        
        // 公开接口不需要JWT验证
        return isPublicPath(path);
    }

    /**
     * 判断是否为公开路径
     */
    private boolean isPublicPath(String path) {
        // 公开的API路径
        String[] publicPaths = {
            "/api/users/register",
            "/api/users/login",
            "/api/auth/",
            "/api/conversations/chat",
            "/api/conversations/welcome",
            "/api/conversations/popular-questions",
            "/api/projects/hot",
            "/api/projects/recommended",
            "/api/projects/categories",
            "/api/projects/stats",
            "/api/donations/recent",
            "/api/donations/large",
            "/api/donations/stats",
            "/api/help-requests/recent-approved",
            "/api/help-requests/stats",
            "/api/test/",
            "/swagger-ui/",
            "/swagger-ui.html",
            "/api/swagger-ui/",
            "/api/swagger-ui.html",
            "/v3/api-docs",
            "/swagger-resources/",
            "/webjars/",
            "/favicon.ico",
            "/error",
            "/actuator/"
        };

        for (String publicPath : publicPaths) {
            if (path.startsWith(publicPath)) {
                return true;
            }
        }
        
        return false;
    }
}
