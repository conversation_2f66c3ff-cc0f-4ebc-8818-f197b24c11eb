package com.example.demo.controller;

import com.example.demo.dto.ApiResponse;
import com.example.demo.service.UserService;
import com.example.demo.service.ProjectService;
import com.example.demo.service.DonationService;
import com.example.demo.service.HelpRequestService;
import com.example.demo.service.ConversationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 管理员Controller
 * 提供管理员专用的统计和管理功能
 */
@Slf4j
@RestController
@RequestMapping("/api/admin")
@Tag(name = "管理员管理", description = "管理员专用的统计和管理功能API")
public class AdminController extends BaseController {

    @Autowired
    private UserService userService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private DonationService donationService;

    @Autowired
    private HelpRequestService helpRequestService;

    @Autowired
    private ConversationService conversationService;

    @GetMapping("/dashboard-stats")
    @Operation(summary = "获取管理员仪表板统计", description = "获取管理员仪表板所需的综合统计数据")
    public ApiResponse<Map<String, Object>> getDashboardStats() {
        try {
            // 权限检查：只有管理员可以访问
            if (!isAdmin()) {
                return forbidden("无权限访问管理员统计数据");
            }

            logOperation("获取管理员仪表板统计");

            Map<String, Object> dashboardStats = new HashMap<>();

            // 获取用户统计
            Object userStats = userService.getUserStatistics();
            dashboardStats.put("userStats", userStats);

            // 获取项目统计
            Object projectStats = projectService.getProjectOverviewStats();
            dashboardStats.put("projectStats", projectStats);

            // 获取捐赠统计
            Object donationStats = donationService.getDonationOverviewStats();
            dashboardStats.put("donationStats", donationStats);

            // 获取救助申请统计
            Object helpRequestStats = helpRequestService.getHelpRequestOverviewStats();
            dashboardStats.put("helpRequestStats", helpRequestStats);

            // 获取AI助手统计
            Object conversationStats = conversationService.getConversationOverviewStats();
            dashboardStats.put("conversationStats", conversationStats);

            // 获取待处理事项统计
            Map<String, Object> pendingStats = new HashMap<>();
            
            // 待审核申请数量
            try {
                Long pendingRequestCount = helpRequestService.countPendingRequests();
                pendingStats.put("pendingRequests", pendingRequestCount);
            } catch (Exception e) {
                log.warn("获取待审核申请数量失败", e);
                pendingStats.put("pendingRequests", 0L);
            }

            // 今日新增用户数
            try {
                Long todayNewUsers = userService.countTodayNewUsers();
                pendingStats.put("todayNewUsers", todayNewUsers);
            } catch (Exception e) {
                log.warn("获取今日新增用户数失败", e);
                pendingStats.put("todayNewUsers", 0L);
            }

            // 今日新增项目数
            try {
                Long todayNewProjects = projectService.countTodayNewProjects();
                pendingStats.put("todayNewProjects", todayNewProjects);
            } catch (Exception e) {
                log.warn("获取今日新增项目数失败", e);
                pendingStats.put("todayNewProjects", 0L);
            }

            // 今日捐赠金额
            try {
                Object todayDonations = donationService.getTodayDonationStats();
                pendingStats.put("todayDonations", todayDonations);
            } catch (Exception e) {
                log.warn("获取今日捐赠统计失败", e);
                pendingStats.put("todayDonations", null);
            }

            dashboardStats.put("pendingStats", pendingStats);

            return success("获取仪表板统计成功", dashboardStats);

        } catch (Exception e) {
            log.error("获取管理员仪表板统计失败", e);
            return error("获取仪表板统计失败：" + e.getMessage());
        }
    }

    @GetMapping("/system-info")
    @Operation(summary = "获取系统信息", description = "获取系统运行状态和基本信息")
    public ApiResponse<Map<String, Object>> getSystemInfo() {
        try {
            // 权限检查：只有管理员可以访问
            if (!isAdmin()) {
                return forbidden("无权限访问系统信息");
            }

            logOperation("获取系统信息");

            Map<String, Object> systemInfo = new HashMap<>();

            // 系统基本信息
            systemInfo.put("javaVersion", System.getProperty("java.version"));
            systemInfo.put("osName", System.getProperty("os.name"));
            systemInfo.put("osVersion", System.getProperty("os.version"));

            // 内存信息
            Runtime runtime = Runtime.getRuntime();
            Map<String, Object> memoryInfo = new HashMap<>();
            memoryInfo.put("totalMemory", runtime.totalMemory());
            memoryInfo.put("freeMemory", runtime.freeMemory());
            memoryInfo.put("maxMemory", runtime.maxMemory());
            memoryInfo.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());
            systemInfo.put("memory", memoryInfo);

            // 系统时间
            systemInfo.put("serverTime", System.currentTimeMillis());

            return success("获取系统信息成功", systemInfo);

        } catch (Exception e) {
            log.error("获取系统信息失败", e);
            return error("获取系统信息失败：" + e.getMessage());
        }
    }

    @GetMapping("/public-stats")
    @Operation(summary = "获取公开统计数据", description = "获取首页展示的公开统计数据，无需登录")
    public ApiResponse<Map<String, Object>> getPublicStats() {
        try {
            logOperation("获取公开统计数据");

            Map<String, Object> publicStats = new HashMap<>();

            // 获取捐赠统计
            try {
                Object donationStats = donationService.getDonationOverviewStats();
                publicStats.put("donationStats", donationStats);
            } catch (Exception e) {
                log.warn("获取捐赠统计失败", e);
                publicStats.put("donationStats", null);
            }

            // 获取项目统计
            try {
                Object projectStats = projectService.getProjectOverviewStats();
                publicStats.put("projectStats", projectStats);
            } catch (Exception e) {
                log.warn("获取项目统计失败", e);
                publicStats.put("projectStats", null);
            }

            // 获取用户统计（公开部分）
            try {
                Object userStats = userService.getUserStatistics();
                publicStats.put("userStats", userStats);
            } catch (Exception e) {
                log.warn("获取用户统计失败", e);
                publicStats.put("userStats", null);
            }

            // 获取救助申请统计（公开部分）
            try {
                Object helpRequestStats = helpRequestService.getHelpRequestOverviewStats();
                publicStats.put("helpRequestStats", helpRequestStats);
            } catch (Exception e) {
                log.warn("获取救助申请统计失败", e);
                publicStats.put("helpRequestStats", null);
            }

            return success("获取公开统计数据成功", publicStats);

        } catch (Exception e) {
            log.error("获取公开统计数据失败", e);
            return error("获取公开统计数据失败：" + e.getMessage());
        }
    }
}
