<template>
  <div class="admin-users">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">用户管理</h1>
        <p class="page-description">管理平台用户信息和状态</p>
      </div>
      <div class="header-right">
        <el-button @click="handleRefresh">
          <el-icon>
            <Refresh />
          </el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <el-card>
        <el-form :model="filters" inline>
          <el-form-item label="搜索用户">
            <el-input v-model="filters.keyword" placeholder="输入用户名、邮箱或真实姓名" style="width: 250px" clearable
              @keyup.enter="handleSearch">
              <template #prefix>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="用户角色">
            <el-select v-model="filters.role" placeholder="选择角色" style="width: 120px" clearable>
              <el-option label="普通用户" value="user" />
              <el-option label="管理员" value="admin" />
            </el-select>
          </el-form-item>

          <el-form-item label="用户状态">
            <el-select v-model="filters.status" placeholder="选择状态" style="width: 120px" clearable>
              <el-option label="正常" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon>
                <Search />
              </el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 用户列表 -->
    <div class="table-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>用户列表</span>
            <div class="header-actions">
              <span class="total-count">共 {{ pagination.total }} 个用户</span>
            </div>
          </div>
        </template>

        <el-table :data="users" v-loading="loading" stripe style="width: 100%">
          <el-table-column prop="id" label="ID" width="80" />

          <el-table-column label="用户信息" min-width="200">
            <template #default="{ row }">
              <div class="user-info">
                <el-avatar :src="row.avatar" :size="40" class="user-avatar">
                  <el-icon>
                    <User />
                  </el-icon>
                </el-avatar>
                <div class="user-details">
                  <div class="username">{{ row.username }}</div>
                  <div class="real-name">{{ row.realName || '未设置' }}</div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="email" label="邮箱" min-width="180" />

          <el-table-column prop="phone" label="手机号" width="120">
            <template #default="{ row }">
              {{ row.phone || '未设置' }}
            </template>
          </el-table-column>

          <el-table-column label="角色" width="100">
            <template #default="{ row }">
              <el-tag :type="getRoleType(row.role)" size="small">
                {{ getRoleText(row.role) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="注册时间" width="160">
            <template #default="{ row }">
              {{ formatTime(row.createTime) }}
            </template>
          </el-table-column>

          <el-table-column label="最后登录" width="160">
            <template #default="{ row }">
              {{ formatTime(row.lastLoginTime) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button text type="primary" size="small" @click="handleView(row)">
                  <el-icon>
                    <View />
                  </el-icon>
                  查看
                </el-button>

                <el-dropdown @command="(command) => handleDropdownCommand(command, row)" trigger="click">
                  <el-button text type="primary" size="small" class="more-btn">
                    <el-icon>
                      <MoreFilled />
                    </el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="toggleStatus">
                        <el-icon>
                          <Switch v-if="row.status === 1" />
                          <VideoPlay v-else />
                        </el-icon>
                        {{ row.status === 1 ? '禁用用户' : '启用用户' }}
                      </el-dropdown-item>
                      <el-dropdown-item command="resetPassword" divided>
                        <el-icon>
                          <Key />
                        </el-icon>
                        重置密码
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination v-model:current-page="pagination.current" v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50, 100]" :total="pagination.total" layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange" @current-change="handlePageChange" />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    Search,
    Refresh,
    User,
    View,
    MoreFilled,
    Switch,
    VideoPlay,
    Key
  } from '@element-plus/icons-vue'
  import {
    getUserList,
    updateUserStatus,
    resetUserPassword
  } from '@/api/admin'

  const router = useRouter()

  // 响应式数据
  const loading = ref(false)
  const users = ref([])

  // 筛选条件
  const filters = reactive({
    keyword: '',
    role: '',
    status: ''
  })

  // 分页信息
  const pagination = reactive({
    current: 1,
    size: 20,
    total: 0
  })

  // 加载用户列表
  const loadUsers = async () => {
    try {
      loading.value = true

      const params = {
        current: pagination.current,
        size: pagination.size,
        keyword: filters.keyword || undefined,
        role: filters.role || undefined,
        status: filters.status !== '' ? filters.status : undefined
      }

      const response = await getUserList(params)

      if (response && response.records) {
        users.value = response.records
        pagination.total = response.total || 0
      }

    } catch (error) {
      console.error('加载用户列表失败:', error)
      ElMessage.error('加载用户列表失败')
    } finally {
      loading.value = false
    }
  }

  // 搜索处理
  const handleSearch = () => {
    pagination.current = 1
    loadUsers()
  }

  // 重置筛选
  const handleReset = () => {
    filters.keyword = ''
    filters.role = ''
    filters.status = ''
    pagination.current = 1
    loadUsers()
  }

  // 刷新
  const handleRefresh = () => {
    loadUsers()
  }

  // 分页处理
  const handlePageChange = (page) => {
    pagination.current = page
    loadUsers()
  }

  const handleSizeChange = (size) => {
    pagination.size = size
    pagination.current = 1
    loadUsers()
  }

  // 查看用户详情
  const handleView = (row) => {
    router.push(`/admin/users/${row.id}`)
  }

  // 下拉菜单操作
  const handleDropdownCommand = async (command, row) => {
    switch (command) {
      case 'toggleStatus':
        await handleToggleStatus(row)
        break
      case 'resetPassword':
        await handleResetPassword(row)
        break
    }
  }

  // 切换用户状态
  const handleToggleStatus = async (row) => {
    const action = row.status === 1 ? '禁用' : '启用'
    const newStatus = row.status === 1 ? 0 : 1

    try {
      await ElMessageBox.confirm(
        `确定要${action}用户"${row.username}"吗？`,
        `确认${action}`,
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await updateUserStatus(row.id, newStatus)
      ElMessage.success(`用户${action}成功`)
      loadUsers()

    } catch (error) {
      if (error !== 'cancel') {
        console.error(`${action}用户失败:`, error)
        if (error.message) {
          ElMessage.error(error.message)
        }
      }
    }
  }

  // 重置密码
  const handleResetPassword = async (row) => {
    try {
      await ElMessageBox.confirm(
        `确定要重置用户"${row.username}"的密码吗？\n密码将重置为：12345678`,
        '确认重置密码',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await resetUserPassword(row.id)
      ElMessage.success('密码重置成功，新密码为：12345678')

    } catch (error) {
      if (error !== 'cancel') {
        console.error('重置密码失败:', error)
        if (error.message) {
          ElMessage.error(error.message)
        }
      }
    }
  }

  // 工具函数
  const getRoleType = (role) => {
    const typeMap = {
      admin: 'danger',
      user: 'primary'
    }
    return typeMap[role] || ''
  }

  const getRoleText = (role) => {
    const textMap = {
      admin: '管理员',
      user: '普通用户'
    }
    return textMap[role] || role
  }

  const getStatusType = (status) => {
    return status === 1 ? 'success' : 'danger'
  }

  const getStatusText = (status) => {
    return status === 1 ? '正常' : '禁用'
  }

  const formatTime = (timeString) => {
    if (!timeString) return '未知'
    return new Date(timeString).toLocaleString('zh-CN')
  }

  // 页面初始化
  onMounted(() => {
    loadUsers()
  })
</script>

<style scoped>
  .admin-users {
    padding: 24px;
    background: #f5f5f5;
    min-height: 100vh;
  }

  /* 页面头部 */
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .page-title {
    font-size: 24px;
    font-weight: bold;
    color: #2D3748;
    margin: 0 0 8px 0;
  }

  .page-description {
    color: #718096;
    margin: 0;
    font-size: 14px;
  }

  /* 筛选区域 */
  .filter-section {
    margin-bottom: 24px;
  }

  .filter-section .el-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  /* 表格区域 */
  .table-section .el-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .total-count {
    color: #718096;
    font-size: 14px;
  }

  /* 用户信息 */
  .user-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .user-avatar {
    flex-shrink: 0;
  }

  .user-details {
    flex: 1;
    min-width: 0;
  }

  .username {
    font-weight: 500;
    color: #2D3748;
    margin-bottom: 4px;
  }

  .real-name {
    font-size: 12px;
    color: #718096;
  }

  /* 操作按钮 */
  .action-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .more-btn {
    padding: 4px 8px;
  }

  /* 分页 */
  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .admin-users {
      padding: 16px;
    }

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .filter-section .el-form {
      flex-direction: column;
      align-items: stretch;
    }

    .filter-section .el-form-item {
      margin-right: 0;
      margin-bottom: 16px;
    }

    .action-buttons {
      flex-direction: column;
      gap: 4px;
    }
  }
</style>