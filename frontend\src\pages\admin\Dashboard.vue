<template>
  <div class="dashboard" v-loading="loading">
    <div class="page-header">
      <h1 class="page-title">仪表板</h1>
      <div class="header-actions">
        <el-button @click="loadDashboardData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
    
    <!-- 统计卡片 -->
    <el-row :gutter="24" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon user-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ dashboardData.userStats?.totalUsers || 0 }}</div>
              <div class="stat-label">总用户数</div>
              <div class="stat-change">
                今日新增: {{ dashboardData.pendingStats?.todayNewUsers || 0 }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon project-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ dashboardData.projectStats?.totalProjects || 0 }}</div>
              <div class="stat-label">救助项目</div>
              <div class="stat-change">
                今日新增: {{ dashboardData.pendingStats?.todayNewProjects || 0 }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon donation-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">¥{{ formatAmount(dashboardData.donationStats?.totalAmount) }}</div>
              <div class="stat-label">捐赠总额</div>
              <div class="stat-change">
                今日: ¥{{ formatAmount(dashboardData.pendingStats?.todayDonations?.todayAmount) }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon request-icon">
              <el-icon><ChatDotRound /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ dashboardData.helpRequestStats?.totalRequests || 0 }}</div>
              <div class="stat-label">救助申请</div>
              <div class="stat-change">
                待审核: {{ dashboardData.pendingStats?.pendingRequests || 0 }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作和最近活动 -->
    <el-row :gutter="24" class="dashboard-content">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>快速操作</span>
            </div>
          </template>
          <div class="quick-actions">
            <el-button type="primary" @click="$router.push('/admin/projects/create')">
              <el-icon><Plus /></el-icon>
              创建项目
            </el-button>
            <el-button type="success" @click="$router.push('/admin/help-requests')">
              <el-icon><View /></el-icon>
              审核申请
            </el-button>
            <el-button type="info" @click="$router.push('/admin/users')">
              <el-icon><User /></el-icon>
              管理用户
            </el-button>
            <el-button type="warning" @click="$router.push('/admin/donations')">
              <el-icon><Money /></el-icon>
              查看捐赠
            </el-button>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>系统信息</span>
              <el-button text @click="refreshSystemInfo">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          <div class="system-info" v-if="systemInfo">
            <div class="info-item">
              <span class="info-label">Java版本:</span>
              <span class="info-value">{{ systemInfo.javaVersion }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">操作系统:</span>
              <span class="info-value">{{ systemInfo.osName }} {{ systemInfo.osVersion }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">内存使用:</span>
              <span class="info-value">{{ formatMemory(systemInfo.memory?.usedMemory) }} / {{ formatMemory(systemInfo.memory?.totalMemory) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">服务器时间:</span>
              <span class="info-value">{{ formatTime(systemInfo.serverTime) }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getDashboardStats, getSystemInfo } from '@/api/admin'
import { ElMessage } from 'element-plus'
import {
  User,
  Document,
  Money,
  ChatDotRound,
  Plus,
  View,
  Refresh
} from '@element-plus/icons-vue'

const dashboardData = ref({})
const systemInfo = ref(null)
const loading = ref(false)

// 加载仪表板数据
const loadDashboardData = async () => {
  try {
    loading.value = true
    const response = await getDashboardStats()
    dashboardData.value = response || {}
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
    ElMessage.error('加载仪表板数据失败')
  } finally {
    loading.value = false
  }
}

// 加载系统信息
const loadSystemInfo = async () => {
  try {
    const response = await getSystemInfo()
    systemInfo.value = response || {}
  } catch (error) {
    console.error('加载系统信息失败:', error)
    ElMessage.error('加载系统信息失败')
  }
}

// 刷新系统信息
const refreshSystemInfo = () => {
  loadSystemInfo()
  ElMessage.success('系统信息已刷新')
}

// 格式化金额
const formatAmount = (amount) => {
  if (!amount) return '0'
  return Number(amount).toLocaleString()
}

// 格式化内存
const formatMemory = (bytes) => {
  if (!bytes) return '0 MB'
  const mb = bytes / (1024 * 1024)
  return `${mb.toFixed(1)} MB`
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString()
}

onMounted(() => {
  loadDashboardData()
  loadSystemInfo()
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.user-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.project-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.donation-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.request-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  color: #999;
}

.dashboard-content {
  margin-top: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.quick-actions .el-button {
  height: 60px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.system-info {
  space-y: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  color: #666;
  font-size: 14px;
}

.info-value {
  color: #333;
  font-size: 14px;
  font-weight: 500;
}
</style>
