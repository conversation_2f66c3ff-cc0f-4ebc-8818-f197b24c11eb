<template>
  <div class="admin-projects">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">项目管理</h1>
        <p class="page-description">管理所有救助项目，包括创建、编辑、状态管理等</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="$router.push('/admin/projects/create')">
          <el-icon>
            <Plus />
          </el-icon>
          创建项目
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="项目标题">
          <el-input v-model="searchForm.keyword" placeholder="请输入项目标题" clearable style="width: 200px"
            @keyup.enter="handleSearch" />
        </el-form-item>

        <el-form-item label="项目分类">
          <el-select v-model="searchForm.category" placeholder="请选择分类" clearable style="width: 150px">
            <el-option label="医疗救助" value="medical" />
            <el-option label="教育助学" value="education" />
            <el-option label="灾难救援" value="disaster" />
            <el-option label="扶贫济困" value="poverty" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="项目状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 150px">
            <el-option label="进行中" value="active" />
            <el-option label="已完成" value="completed" />
            <el-option label="已关闭" value="closed" />
            <el-option label="草稿" value="draft" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon>
              <Search />
            </el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon>
              <Refresh />
            </el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 项目列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>项目列表</span>
          <div class="header-actions">
            <el-button text @click="loadProjects">
              <el-icon>
                <Refresh />
              </el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table v-loading="loading" :data="projects" style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />

        <el-table-column prop="id" label="ID" width="80" />

        <el-table-column prop="title" label="项目标题" min-width="200">
          <template #default="{ row }">
            <div class="project-title">
              <span>{{ row.title }}</span>
              <el-tag v-if="row.urgent" type="danger" size="small" style="margin-left: 8px">
                紧急
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="category" label="分类" width="100">
          <template #default="{ row }">
            <el-tag :type="getCategoryType(row.category)" size="small">
              {{ getCategoryLabel(row.category) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="targetAmount" label="目标金额" width="120">
          <template #default="{ row }">
            ¥{{ formatAmount(row.targetAmount) }}
          </template>
        </el-table-column>

        <el-table-column prop="currentAmount" label="已筹金额" width="120">
          <template #default="{ row }">
            ¥{{ formatAmount(row.currentAmount) }}
          </template>
        </el-table-column>

        <el-table-column label="进度" width="120">
          <template #default="{ row }">
            <el-progress :percentage="getProgress(row)" :stroke-width="8" :show-text="false" />
            <div class="progress-text">{{ getProgress(row) }}%</div>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.createTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button text type="primary" size="small" @click="handleView(row)">
                <el-icon>
                  <View />
                </el-icon>
                查看
              </el-button>

              <el-button text type="primary" size="small" @click="handleEdit(row)">
                <el-icon>
                  <Edit />
                </el-icon>
                编辑
              </el-button>

              <el-dropdown @command="(command) => handleDropdownCommand(command, row)" trigger="click">
                <el-button text type="primary" size="small" class="more-btn">
                  <el-icon>
                    <MoreFilled />
                  </el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="toggleStatus">
                      <el-icon>
                        <Switch v-if="row.status === 'active'" />
                        <VideoPlay v-else />
                      </el-icon>
                      {{ row.status === 'active' ? '关闭项目' : '激活项目' }}
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" divided>
                      <el-icon>
                        <Delete />
                      </el-icon>
                      删除项目
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination v-model:current-page="pagination.current" v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]" :total="pagination.total" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </el-card>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    Plus,
    Search,
    Refresh,
    View,
    Edit,
    MoreFilled,
    Switch,
    VideoPlay,
    Delete
  } from '@element-plus/icons-vue'
  import {
    getProjectList,
    updateProjectStatus,
    deleteProject
  } from '@/api/admin'

  const router = useRouter()

  // 响应式数据
  const loading = ref(false)
  const projects = ref([])
  const selectedProjects = ref([])

  // 搜索表单
  const searchForm = reactive({
    keyword: '',
    category: '',
    status: ''
  })

  // 分页数据
  const pagination = reactive({
    current: 1,
    size: 10,
    total: 0
  })

  // 加载项目列表
  const loadProjects = async () => {
    try {
      loading.value = true

      const params = {
        current: pagination.current,
        size: pagination.size,
        ...searchForm
      }

      // 过滤空值
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key]
        }
      })

      const response = await getProjectList(params)

      if (response && response.records) {
        projects.value = response.records
        pagination.total = response.total
      } else {
        projects.value = response || []
        pagination.total = projects.value.length
      }

    } catch (error) {
      console.error('加载项目列表失败:', error)
      ElMessage.error('加载项目列表失败')
    } finally {
      loading.value = false
    }
  }

  // 搜索
  const handleSearch = () => {
    pagination.current = 1
    loadProjects()
  }

  // 重置搜索
  const handleReset = () => {
    Object.assign(searchForm, {
      keyword: '',
      category: '',
      status: ''
    })
    pagination.current = 1
    loadProjects()
  }

  // 分页处理
  const handleSizeChange = (size) => {
    pagination.size = size
    pagination.current = 1
    loadProjects()
  }

  const handleCurrentChange = (current) => {
    pagination.current = current
    loadProjects()
  }

  // 选择处理
  const handleSelectionChange = (selection) => {
    selectedProjects.value = selection
  }

  // 操作处理
  const handleView = (row) => {
    // 跳转到项目详情页面（普通用户视角）
    window.open(`/projects/${row.id}`, '_blank')
  }

  const handleEdit = (row) => {
    router.push(`/admin/projects/edit/${row.id}`)
  }

  const handleDropdownCommand = async (command, row) => {
    switch (command) {
      case 'toggleStatus':
        await handleToggleStatus(row)
        break
      case 'delete':
        await handleDelete(row)
        break
    }
  }

  // 切换项目状态
  const handleToggleStatus = async (row) => {
    const newStatus = row.status === 'active' ? 'closed' : 'active'
    const action = newStatus === 'active' ? '激活' : '关闭'

    try {
      await ElMessageBox.confirm(
        `确定要${action}项目"${row.title}"吗？`,
        '确认操作',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await updateProjectStatus(row.id, newStatus)
      ElMessage.success(`项目${action}成功`)
      loadProjects()

    } catch (error) {
      if (error !== 'cancel') {
        console.error(`${action}项目失败:`, error)
        // 显示后端返回的具体错误信息，不显示通用错误
        if (error.message) {
          ElMessage.error(error.message)
        }
      }
    }
  }

  // 删除项目
  const handleDelete = async (row) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除项目"${row.title}"吗？\n\n注意：如果项目已有捐赠记录，将无法删除，只能关闭项目。`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'error'
        }
      )

      await deleteProject(row.id)
      ElMessage.success('项目删除成功')
      loadProjects()

    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除项目失败:', error)
        // 显示后端返回的具体错误信息，不显示通用错误
        if (error.message) {
          ElMessage.error(error.message)
        }
      }
    }
  }

  // 工具函数
  const formatAmount = (amount) => {
    if (!amount) return '0'
    return Number(amount).toLocaleString()
  }

  const formatTime = (time) => {
    if (!time) return '-'
    return new Date(time).toLocaleString()
  }

  const getProgress = (row) => {
    if (!row.targetAmount || row.targetAmount === 0) return 0
    const progress = Math.round((row.currentAmount / row.targetAmount) * 100)
    return Math.min(progress, 100)
  }

  const getCategoryLabel = (category) => {
    const labels = {
      medical: '医疗救助',
      education: '教育助学',
      disaster: '灾难救援',
      poverty: '扶贫济困',
      other: '其他'
    }
    return labels[category] || category
  }

  const getCategoryType = (category) => {
    const types = {
      medical: 'danger',
      education: 'primary',
      disaster: 'warning',
      poverty: 'success',
      other: 'info'
    }
    return types[category] || 'info'
  }

  const getStatusLabel = (status) => {
    const labels = {
      active: '进行中',
      completed: '已完成',
      closed: '已关闭',
      draft: '草稿'
    }
    return labels[status] || status
  }

  const getStatusType = (status) => {
    const types = {
      active: 'success',
      completed: 'primary',
      closed: 'info',
      draft: 'warning'
    }
    return types[status] || 'info'
  }

  // 页面初始化
  onMounted(() => {
    loadProjects()
  })
</script>

<style scoped>
  .admin-projects {
    max-width: 1400px;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
  }

  .header-left {
    flex: 1;
  }

  .page-title {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 24px;
    font-weight: 600;
  }

  .page-description {
    margin: 0;
    color: #666;
    font-size: 14px;
  }

  .header-right {
    display: flex;
    gap: 12px;
  }

  .search-card {
    margin-bottom: 24px;
  }

  .table-card {
    margin-bottom: 24px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-actions {
    display: flex;
    gap: 8px;
  }

  .project-title {
    display: flex;
    align-items: center;
  }

  .progress-text {
    text-align: center;
    font-size: 12px;
    color: #666;
    margin-top: 4px;
  }

  .action-buttons {
    display: flex;
    align-items: center;
    gap: 4px;
    justify-content: flex-start;
  }

  .action-buttons .el-button {
    padding: 4px 8px;
    height: auto;
    min-height: 28px;
    font-size: 12px;
    border-radius: 4px;
    transition: all 0.2s;
  }

  .action-buttons .el-button:hover {
    background-color: rgba(64, 158, 255, 0.1);
  }

  .action-buttons .el-button .el-icon {
    margin-right: 4px;
    font-size: 14px;
  }

  .more-btn {
    width: 28px !important;
    padding: 4px !important;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .more-btn .el-icon {
    margin-right: 0 !important;
    font-size: 16px;
  }

  .more-btn:hover {
    background-color: rgba(64, 158, 255, 0.1) !important;
    border-radius: 4px;
  }

  /* 下拉菜单样式优化 */
  :deep(.el-dropdown-menu) {
    padding: 8px 0;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  :deep(.el-dropdown-menu__item) {
    padding: 8px 16px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  :deep(.el-dropdown-menu__item:hover) {
    background-color: #f5f7fa;
  }

  :deep(.el-dropdown-menu__item .el-icon) {
    font-size: 16px;
  }

  :deep(.el-dropdown-menu__item--divided) {
    border-top: 1px solid #e4e7ed;
    margin-top: 4px;
    padding-top: 12px;
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .page-header {
      flex-direction: column;
      gap: 16px;
    }

    .header-right {
      width: 100%;
      justify-content: flex-start;
    }

    .el-form--inline .el-form-item {
      display: block;
      margin-bottom: 16px;
    }

    .el-table {
      font-size: 12px;
    }

    .action-buttons {
      flex-direction: column;
      gap: 2px;
      align-items: stretch;
    }

    .action-buttons .el-button {
      width: 100%;
      justify-content: center;
      font-size: 11px;
      padding: 2px 4px;
      min-height: 24px;
    }

    .more-btn {
      width: 100% !important;
    }
  }
</style>