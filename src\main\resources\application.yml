# 爱心救助平台配置文件 - 学生课程设计版本
server:
  port: 8080

spring:
  application:
    name: charity-platform

  # 允许Bean定义覆盖，解决MyBatis Plus兼容性问题
  main:
    allow-bean-definition-overriding: true

  # 数据源配置 (请确保MySQL服务已启动，数据库已创建)
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************
    username: root
    password: root

  # Jackson JSON配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

  # 文件上传配置
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 50MB
      file-size-threshold: 2KB

# MyBatis Plus配置
mybatis-plus:
  configuration:
    # 开启驼峰命名转换 (数据库字段user_name -> Java属性userName)
    map-underscore-to-camel-case: true
    # 开启SQL日志 (开发时可以看到执行的SQL语句)
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      # 主键策略：数据库自增
      id-type: auto
    # 完全禁用SQL Runner功能
    enable-sql-runner: false
    # 禁用DDL功能
    sql-runner:
      enabled: false
  # Mapper XML文件位置
  mapper-locations: classpath*:mapper/*.xml

# JWT配置
jwt:
  secret: YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXpBQkNERUZHSElKS0xNTk9QUVJTVFVWV1hZWjAxMjM0NTY3ODkhQCMkJV4mKigpXy0rPXt9W11cfFw7JzoiJyw8Lj4vPw==
  access-token-expiration: 7200000 # 2小时 (2 * 60 * 60 * 1000)
  refresh-token-expiration: 604800000 # 7天 (7 * 24 * 60 * 60 * 1000)
  issuer: charity-platform
  header: Authorization
  prefix: Bearer

# AI配置 - 火山引擎豆包
ai:
  volcengine:
    # API密钥，建议通过环境变量设置
    api-key: 5b245916-7820-41d9-861e-65c89f34f100
    # 基础URL
    base-url: https://ark.cn-beijing.volces.com/api/v3
    # 模型名称
    model: doubao-seed-1-6-250615
    # 系统提示词
    system-prompt: '你是一个专业的AI助手，专门为爱心救助平台提供服务。请用温暖、专业的语调回答用户问题，并提供有用的建议。'
    # 欢迎消息
    welcome-message: '您好！我是爱心救助平台的AI助手，很高兴为您服务。我可以帮助您了解平台功能、解答疑问、提供救助建议等。请问有什么可以帮助您的吗？'
    # 热门问题
    popular-questions:
      - '如何申请救助？'
      - '如何进行捐赠？'
      - '救助申请需要什么材料？'
      - '捐赠资金如何使用？'
      - '如何查看救助进度？'
      - '平台如何保证资金安全？'
    # 最大对话历史记录数
    max-history-size: 10
    # 请求超时时间（秒）- 设置为0表示无超时限制
    timeout-seconds: 0
    # 最大重试次数
    max-retries: 3
    # 连接池配置
    connection-pool:
      max-idle-connections: 5
      keep-alive-duration: 1
    # 调度器配置
    dispatcher:
      max-requests: 64
      max-requests-per-host: 5

# 安全配置
security:
  rate-limit:
    enabled: true
    requests-per-minute: 60
  cors:
    allowed-origins: '*'
    allowed-methods: 'GET,POST,PUT,DELETE,OPTIONS'
    allowed-headers: '*'
    allow-credentials: true

# 日志配置
logging:
  level:
    root: info
    com.example.demo: debug # 我们的项目包开启debug日志
    org.springframework.security: info
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n'

# 文件存储配置
file:
  upload:
    path: ./uploads # 文件上传目录
  base-url: http://localhost:8080/api/files/ # 文件访问基础URL

# Swagger配置
springdoc:
  api-docs:
    path: /v3/api-docs # API文档JSON路径
  swagger-ui:
    path: /swagger-ui.html # Swagger UI访问路径
    tags-sorter: alpha # 按字母顺序排序标签
    operations-sorter: alpha # 按字母顺序排序操作
  show-actuator: false # 不显示actuator端点
