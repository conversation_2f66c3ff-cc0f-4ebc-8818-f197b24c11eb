/*
 Navicat Premium Data Transfer

 Source Server         : localhost_3306
 Source Server Type    : MySQL
 Source Server Version : 80030 (8.0.30)
 Source Host           : localhost:3306
 Source Schema         : charity_platform

 Target Server Type    : MySQL
 Target Server Version : 80030 (8.0.30)
 File Encoding         : 65001

 Date: 29/06/2025 18:50:21
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for conversations
-- ----------------------------
DROP TABLE IF EXISTS `conversations`;
CREATE TABLE `conversations`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '对话ID',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID(可为空，支持游客对话)',
  `session_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话ID',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户消息',
  `response` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'AI回复',
  `conversation_type` enum('general','donation','help_request','project_info') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'general' COMMENT '对话类型: general-通用, donation-捐赠咨询, help_request-求助咨询, project_info-项目信息',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_session_id`(`session_id` ASC) USING BTREE,
  INDEX `idx_conversation_type`(`conversation_type` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  CONSTRAINT `conversations_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'AI对话记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of conversations
-- ----------------------------
INSERT INTO `conversations` VALUES (1, 2, 'session_001', '你好，我想了解如何申请救助', '您好！很高兴为您服务。申请救助需要以下步骤：1. 注册并登录账户 2. 填写救助申请表 3. 上传相关证明材料 4. 等待管理员审核。请问您遇到了什么困难呢？', 'help_request', '2024-12-01 09:00:00');
INSERT INTO `conversations` VALUES (2, 2, 'session_001', '我想为白血病患者捐款', '非常感谢您的爱心！目前我们有一个小明白血病治疗救助项目，已筹集8.5万元，还需要21.5万元。您可以通过支付宝、微信或银行转账进行捐赠。需要我为您介绍详细情况吗？', 'donation', '2024-12-01 10:00:00');
INSERT INTO `conversations` VALUES (3, NULL, 'session_002', '你们平台可靠吗？', '我们是正规的爱心救助平台，所有项目都经过严格审核，资金使用透明公开。我们会定期公布项目进展和资金使用情况，确保每一分钱都用到需要帮助的人身上。您可以查看我们的项目详情和捐赠记录。', 'general', '2024-12-02 15:30:00');
INSERT INTO `conversations` VALUES (4, NULL, 'web-chat-1750908794823', '你是什么模型', '感谢您的咨询！我是爱心助手小暖，我可以帮您：\n• 了解救助项目信息\n• 指导捐赠流程\n• 协助申请救助\n• 解答相关问题\n\n请告诉我您具体需要什么帮助，我会为您详细解答。', 'general', '2025-06-26 11:33:22');
INSERT INTO `conversations` VALUES (5, NULL, 'web-chat-1750908765657', '如何申请救助？', '我了解您的困难，申请救助需要以下步骤：1. 注册并登录账户 2. 填写救助申请表 3. 上传相关证明材料（如医疗证明、收入证明等）4. 等待管理员审核。审核通过后，我们会安排相应的帮助。请问您遇到的是什么类型的困难？', 'general', '2025-06-26 11:33:22');
INSERT INTO `conversations` VALUES (6, NULL, 'web-chat-1750908823121', '你怎么重复我的话？', '具体流程如下：\n1. 浏览项目列表，选择您关心的项目\n2. 查看项目详情，了解资金用途\n3. 点击捐赠按钮，填写捐赠信息\n4. 选择支付方式完成支付\n5. 获得捐赠证书，可随时查看项目进展\n\n如果您需要申请救助，流程是：注册登录 → 填写申请 → 上传证明 → 等待审核。', 'general', '2025-06-26 11:33:51');
INSERT INTO `conversations` VALUES (7, NULL, 'web-chat-1750908878084', '你是什么模型', '感谢您的咨询！我是爱心助手小暖，我可以帮您：\n• 了解救助项目信息\n• 指导捐赠流程\n• 协助申请救助\n• 解答相关问题\n\n请告诉我您具体需要什么帮助，我会为您详细解答。', 'general', '2025-06-26 11:34:46');
INSERT INTO `conversations` VALUES (8, NULL, 'web-chat-1750909577047', '如何申请救助？', '我了解您的困难，申请救助需要以下步骤：1. 注册并登录账户 2. 填写救助申请表 3. 上传相关证明材料（如医疗证明、收入证明等）4. 等待管理员审核。审核通过后，我们会安排相应的帮助。请问您遇到的是什么类型的困难？', 'general', '2025-06-26 11:46:43');
INSERT INTO `conversations` VALUES (9, NULL, 'web-chat-1750909600289', '你是什么模型', '感谢您的咨询！我是爱心助手小暖，我可以帮您：\n• 了解救助项目信息\n• 指导捐赠流程\n• 协助申请救助\n• 解答相关问题\n\n请告诉我您具体需要什么帮助，我会为您详细解答。', 'general', '2025-06-26 11:46:50');
INSERT INTO `conversations` VALUES (10, NULL, 'web-chat-1750909623473', '我想了解救助项目', '我了解您的困难，申请救助需要以下步骤：1. 注册并登录账户 2. 填写救助申请表 3. 上传相关证明材料（如医疗证明、收入证明等）4. 等待管理员审核。审核通过后，我们会安排相应的帮助。请问您遇到的是什么类型的困难？', 'general', '2025-06-26 11:47:26');
INSERT INTO `conversations` VALUES (11, NULL, 'web-chat-1750909653124', '我想了解救助项目', '我了解您的困难，申请救助需要以下步骤：1. 注册并登录账户 2. 填写救助申请表 3. 上传相关证明材料（如医疗证明、收入证明等）4. 等待管理员审核。审核通过后，我们会安排相应的帮助。请问您遇到的是什么类型的困难？', 'general', '2025-06-26 11:47:55');
INSERT INTO `conversations` VALUES (12, NULL, 'web-chat-1750909682204', '你可以提供什么帮助', '感谢您的咨询！我是爱心助手小暖，我可以帮您：\n• 了解救助项目信息\n• 指导捐赠流程\n• 协助申请救助\n• 解答相关问题\n\n请告诉我您具体需要什么帮助，我会为您详细解答。', 'general', '2025-06-26 11:48:23');
INSERT INTO `conversations` VALUES (13, NULL, 'web-chat-1750910062150', '如何申请救助？', '我了解您的困难，申请救助需要以下步骤：1. 注册并登录账户 2. 填写救助申请表 3. 上传相关证明材料（如医疗证明、收入证明等）4. 等待管理员审核。审核通过后，我们会安排相应的帮助。请问您遇到的是什么类型的困难？', 'general', '2025-06-26 11:54:44');
INSERT INTO `conversations` VALUES (14, NULL, 'web-chat-1750910089824', '申请流程是什么？', '我了解您的困难，申请救助需要以下步骤：1. 注册并登录账户 2. 填写救助申请表 3. 上传相关证明材料（如医疗证明、收入证明等）4. 等待管理员审核。审核通过后，我们会安排相应的帮助。请问您遇到的是什么类型的困难？', 'general', '2025-06-26 11:55:20');
INSERT INTO `conversations` VALUES (15, NULL, 'web-chat-1750910132209', '你是什么模型？', '感谢您的咨询！我是爱心助手小暖，我可以帮您：\n• 了解救助项目信息\n• 指导捐赠流程\n• 协助申请救助\n• 解答相关问题\n\n请告诉我您具体需要什么帮助，我会为您详细解答。', 'general', '2025-06-26 11:55:45');
INSERT INTO `conversations` VALUES (16, NULL, 'web-chat-1750910165347', '1+1等于几', '感谢您的咨询！我是爱心助手小暖，我可以帮您：\n• 了解救助项目信息\n• 指导捐赠流程\n• 协助申请救助\n• 解答相关问题\n\n请告诉我您具体需要什么帮助，我会为您详细解答。', 'general', '2025-06-26 11:56:13');
INSERT INTO `conversations` VALUES (17, NULL, 'web-chat-1750918135657', '1+2 等于多少？', '感谢您的咨询！我是爱心助手小暖，我可以帮您：\n• 了解救助项目信息\n• 指导捐赠流程\n• 协助申请救助\n• 解答相关问题\n\n请告诉我您具体需要什么帮助，我会为您详细解答。', 'general', '2025-06-26 14:08:59');
INSERT INTO `conversations` VALUES (18, NULL, 'web-chat-1750918499372', '如何申请救助？', '您好，非常理解您此刻需要帮助的心情。申请救助的流程其实很简单，我们会尽力为您提供清晰的指引，让您能顺利获得所需的帮助。以下是具体步骤，您可以根据自身情况参考：\n\n\n### **第一步：准备基础材料**  \n提前整理好必要的证明材料，能让申请更高效（不同救助类型可能需要补充材料，以下为通用基础材料）：  \n- **身份证明**：申请人及家庭成员的身份证、户口本复印件（或照片）；若为未成年人，需提供监护人关系证明（如出生证明）。  \n- **困难情况说明**：简明描述需要救助的原因（如医疗困境、突发灾害、生活困难等），可以写一份书面说明，也可在申请表中详细填写。  \n- **相关证明材料**：  \n  - 若为医疗救助：医院诊断证明、费用清单、缴费凭证（或欠费证明）；  \n  - 若为生活困难：低保证、特困证明、失业证明等（如有可提供，无则说明实际情况）；  \n  - 若为突发意外（如灾害、事故）：相关部门出具的证明（如村委会/居委会证明、事故认定书等）。  \n- **银行卡信息**：申请人本人的银行卡号、开户行信息（确保后续资助能顺利到账）。  \n\n\n### **第二步：选择申请渠道**  \n我们提供线上线下多种申请方式，您可以选择最方便的一种：  \n- **线上申请**（推荐，更快捷）：  \n  1. 登录我们的官方网站（www.xxx救助平台.com）或关注官方公众号/小程序，在“救助申请”板块填写电子申请表，按提示上传材料照片。  \n  2. 若操作有困难，可拨打我们的**24小时咨询热线：400-XXX-XXXX**，工作人员会远程协助您填写。  \n- **线下申请**：  \n  携带材料前往附近的 **救助服务站点**（可通过官网或热线查询您所在地区的站点地址），现场有工作人员指导您填写纸质申请表并提交材料。  \n\n\n### **第三步：提交申请并跟进**  \n- **提交方式**：线上申请直接在平台提交即可；线下申请可将材料交给站点工作人员，或邮寄至指定地址（地址可咨询客服）。  \n- **进度查询**：提交后，您会收到申请编号，可通过官网“进度查询”板块或拨打热线，随时了解审核状态（一般审核周期为3-7个工作日，紧急情况会优先处理）。  \n- **补充材料**：若材料不全，工作人员会主动联系您说明需要补充的内容，请保持电话畅通，我们会耐心协助您准备。  \n\n\n### **第四步：审核与救助对接**  \n- **审核通过后**：我们的工作人员会在1-3个工作日内与您联系，确认救助方式（如资金资助、物资支持、医疗对接等），并告知具体的帮扶细节。  \n- **若有疑问**：全程可拨打我们的咨询热线（400-XXX-XXXX）或添加客服微信（XXX-XXXX），我们的志愿者会一对一解答您的问题，帮您解决申请中的困难。  \n\n\n### **温馨提示**  \n- **真实信息最重要**：请确保提供的材料和信息真实有效，这是我们评估和对接帮助的基础。  \n- **不必担心材料不全**：如果暂时无法提供某些证明（如无低保证明），也可以先提交现有材料并说明情况，我们会根据实际情况评估，不会因材料不齐而直接拒绝。  \n- **紧急情况优先处理**：若您面临紧急困境（如急需医疗费用、生活物资短缺），请在申请时标注“紧急”，我们会开通绿色通道加急处理。  \n\n\n如果您在准备材料或操作中有任何疑问，随时可以通过 **官网在线客服** 或 **热线电话400-XXX-XXXX** 联系我们，我们的工作人员和志愿者会全程陪伴您，帮您一起完成申请。请相信，您不是一个人在面对困难，我们和许多爱心人士都在关心着您，会尽最大努力与您一起渡过难关。  \n\n如果需要更具体的帮助（比如某类救助的详细材料清单），也可以告诉我们您的具体情况，我们会为您提供更个性化的指引。请一定不要犹豫，寻求帮助本身就是勇敢的一步，我们在这里支持您。 🌟', 'general', '2025-06-26 14:15:28');
INSERT INTO `conversations` VALUES (19, NULL, 'web-chat-1750918528430', '如何申请救助？', '非常理解您此刻可能需要帮助，我们会尽力为您提供支持。申请救助的流程通常如下，您可以根据自身情况参考操作：\n\n\n### **第一步：准备申请材料**  \n请提前整理以下基础材料（不同救助类型可能需要补充其他材料，具体可咨询工作人员）：  \n1. **身份证明**：申请人及家庭成员的身份证、户口本复印件（未成年人需提供监护人证件）；  \n2. **困难情况说明**：详细描述当前面临的困境（如医疗支出、生活困难、灾害影响等），建议附相关证明（如医院诊断书、医疗费用票据、低保证明、受灾照片等）；  \n3. **经济状况证明**：家庭收入来源、银行流水、负债情况等（如有低保证、残疾证、失业证明等也请一并提供）；  \n4. **联系方式**：确保留下畅通的电话、微信或地址，方便后续沟通。  \n\n\n### **第二步：提交申请**  \n您可以通过以下任一方式提交申请（不同平台可能有细微差异，以实际为准）：  \n1. **线上申请**：  \n   - 登录我们的官方网站（[请填写网址]）或小程序，在“救助申请”板块填写表单并上传材料；  \n   - 发送邮件至官方邮箱：[救助专用邮箱地址]，邮件主题注明“姓名+救助申请”，附件附上材料扫描件。  \n2. **线下申请**：  \n   - 携带材料前往当地救助站点或合作机构（可通过官网查询就近站点地址），由工作人员协助填写纸质申请表；  \n   - 若行动不便，可拨打我们的爱心热线 **[请填写热线电话]**，申请工作人员上门协助。  \n\n\n### **第三步：审核与核实**  \n提交申请后，工作人员会在3个工作日内初步审核材料完整性。若材料齐全，会在7-15个工作日内通过电话、视频或实地走访等方式核实情况（请保持联系方式畅通）。核实过程中，可能会请您补充材料或说明细节，请您耐心配合哦~  \n\n\n### **第四步：获取救助反馈**  \n审核通过后，我们会根据您的情况匹配合适的救助方式（如资金帮扶、物资援助、医疗对接、心理疏导等），并通过电话或短信通知您具体的救助方案和后续安排。  \n如果暂时不符合救助条件，我们也会告知原因，并尽力为您提供其他帮扶建议（如政策咨询、公益资源对接等）。  \n\n\n### **温馨提示**  \n- 若您在申请过程中有任何疑问，可随时拨打我们的24小时爱心热线 **[请填写热线电话]**，或添加客服微信 **[请填写微信号]**，工作人员会一对一为您解答；  \n- 请确保所有材料真实有效，如有伪造行为可能会影响后续救助资格哦。  \n\n\n请别担心，每一份求助我们都会认真对待。如果您需要更具体的指导，可以告诉我您的大致情况（比如是医疗救助、生活帮扶还是灾害救助等），我会为您提供更针对性的建议。我们和您一起面对，加油！ 🌟', 'general', '2025-06-26 14:15:49');
INSERT INTO `conversations` VALUES (20, NULL, 'web-chat-1750918550918', '你是什么模型', '我是专为爱心救助平台设计的AI助手，核心目标是用专业和温暖的方式为需要帮助的人、救助者及公益组织提供支持。无论是解答救助流程疑问、分享救助资源信息，还是协助连接爱心力量，我都会尽最大努力提供实用的帮助～ 如果你有具体的救助需求、想了解公益信息，或者有其他需要协助的地方，都可以随时告诉我呀～', 'general', '2025-06-26 14:15:59');
INSERT INTO `conversations` VALUES (21, NULL, 'web-chat-1750918602422', '1+1 等于多少', '1+1=2呀～这是一个简单又基础的数学答案呢～如果还有其他问题，或者关于爱心救助的事情想了解，随时告诉我哦，我会一直在这里帮助你～', 'general', '2025-06-26 14:16:48');
INSERT INTO `conversations` VALUES (22, NULL, 'web-chat-1751018824233', '你好', '你好呀！很高兴能在这里为你提供帮助~ 我们这个爱心救助平台一直希望能成为大家的温暖后盾——无论是救助流浪的小动物需要物资或行动支持，还是生活中遇到困难想寻求帮助，又或者只是想聊聊关于“爱”与“温暖”的故事，都可以和我说说哦。  \n\n你现在是有什么具体想了解或需要协助的事情吗？别客气，我会认真听你讲，尽力为你提供实用的建议和支持~', 'general', '2025-06-27 18:07:14');
INSERT INTO `conversations` VALUES (23, NULL, 'web-chat-1751019235089', '如何申请救助？', '您别着急，我们一直在这里，会尽力帮您梳理申请流程。不同救助类型（比如医疗救助、生活困难救助、紧急灾害救助等）的具体要求可能略有不同，但整体流程可以参考以下步骤，您可以根据自己的情况准备：\n\n\n### **第一步：先确认救助范围和条件**  \n首先可以简单了解一下您需要的救助类型（比如是因病致贫、突发意外、还是基本生活困难等），不同救助可能对“困难程度”“证明材料”有不同要求（比如低收入证明、医疗诊断书、灾害损失说明等）。如果您不太清楚自己是否符合条件，也可以先告诉我们您的大致情况（比如所在地区、遇到的具体困难），我们会帮您初步判断~\n\n\n### **第二步：准备基础材料（提前整理更省心）**  \n一般需要准备这些基础材料（具体以当地救助平台要求为准，建议提前电话或线上咨询确认）：  \n✅ **身份证明**：申请人及家庭成员的身份证、户口本复印件（如果是为未成年人或老人申请，需额外提供监护人关系证明）；  \n✅ **困难证明**：比如低保证、建档立卡贫困户证明（如有），或由社区/村委会开具的“家庭经济困难情况说明”（需盖章）；  \n✅ **核心事由证明**：  \n  - 若是医疗救助：近期的医院诊断证明、费用清单、缴费凭证（原件或复印件）；  \n  - 若是突发意外（如火灾、事故）：相关部门出具的事故说明、损失清单（如有照片或视频可辅助证明）；  \n  - 若是生活困难：说明家庭收入来源（如工资流水、失业证明）、支出情况（如房租、学费、医疗费等）；  \n✅ **银行卡信息**：申请人本人的银行卡号、开户行信息（确保救助款能顺利到账）。  \n\n\n### **第三步：选择申请渠道（线上/线下都可以）**  \n现在很多救助平台都支持线上申请，方便快捷，您可以根据自己的情况选择：  \n- **线上申请**：通过当地民政局官网、“民政通”类APP、救助平台官方公众号（比如搜索“XX市社会救助”），找到“救助申请”入口，按提示填写信息、上传材料（建议材料扫描或拍照清晰，避免模糊影响审核）；  \n- **线下申请**：如果不方便线上操作，可以携带材料到户籍所在地或居住地的社区居委会、村委会，或当地救助管理站，工作人员会协助您填写申请表、提交材料。  \n\n\n### **第四步：配合审核，保持沟通**  \n提交申请后，工作人员会在规定时间内（通常1-15个工作日，具体看救助类型）进行材料审核，可能会电话联系您补充信息，或上门核实情况（比如家庭实际居住条件）。这时候请保持电话畅通，耐心配合就好~  \n\n\n### **第五步：等待结果通知**  \n审核通过后，救助金或物资会通过银行卡发放，或由工作人员联系您领取；如果暂时不符合条件，也会收到反馈说明原因（部分情况可补充材料后重新申请）。  \n\n\n如果过程中您觉得繁琐，或者不知道材料怎么准备，**随时可以告诉我们您的具体困难（比如“家人重病需要医疗救助”“遭遇洪水导致房屋受损”），我们会帮您细化当地的申请渠道和材料清单，甚至协助您对接社区工作人员**。  \n\n您不是一个人在面对困难，我们和很多爱心人士都在努力，希望能帮您一起渡过难关~ 您现在方便说说具体是哪方面需要帮助吗？我们可以一步步来~ 💛', 'general', '2025-06-27 18:14:22');
INSERT INTO `conversations` VALUES (24, NULL, 'web-chat-1751044888299', '如何申请救助？', '您好呀，申请救助的过程或许会让您有些忐忑，但请相信，寻求帮助是勇敢的开始，我们一直在这里支持您。以下是申请救助的一般流程，您可以根据具体情况参考，如有需要也可以随时告诉我您的具体困境，我会尽力帮您细化指引~\n\n\n### **第一步：先确认救助条件，找到匹配的帮助**  \n不同的救助项目（比如大病医疗、临时生活困难、突发事件救助等）通常有具体的帮扶对象范围（比如户籍、收入水平、困难类型等）。您可以先通过以下方式了解：  \n- **查看目标平台/机构的说明**：比如我们平台的官网、公众号，或当地民政局、慈善组织的公开信息，都会写清楚“救助对象”“申请条件”（比如是否需要低收入证明、疾病诊断等）。  \n- **直接咨询**：如果文字说明不够清晰，打平台客服电话或到线下办公点（比如社区服务中心、慈善总会），工作人员会耐心帮您判断是否符合条件，避免您走弯路。  \n\n\n### **第二步：准备申请材料（关键！材料越全，流程越顺）**  \n根据救助类型不同，材料会有差异，但通常需要这些基础文件（建议提前复印或扫描存档）：  \n- **身份证明**：申请人及家庭成员的身份证、户口本（原件+复印件）；如果是给未成年人或老人申请，还需要监护人关系证明（如出生证、亲属关系证明）。  \n- **困难证明**：  \n  - 经济困难：低保证、特困人员证明、低保边缘家庭认定表（可找社区居委会/村委会开具）；如果没有这些，可提供家庭收入证明（比如近3个月工资流水、无收入说明）、支出凭证（如房租、医疗费票据）。  \n  - 疾病/意外：医院诊断证明、病历、费用清单、缴费凭证（如果是大病，可能需要提供医保报销后的自付部分证明）；突发事件（如火灾、车祸）需提供事故认定书、现场照片、损失证明等。  \n- **其他补充材料**：比如村委会/居委会开具的“情况说明”（简述家庭困难原因、需要帮助的具体事项），或能证明困境的照片、视频（非必需，但有时能帮助审核人员更直观了解情况）。  \n- **收款账户**：申请人本人的银行卡号（注明开户行，确保信息准确，方便后续拨款）。  \n\n✨ **小提醒**：如果不清楚怎么开证明，直接问社区工作人员，他们很熟悉流程，会协助您办理~  \n\n\n### **第三步：提交申请（线上/线下，选方便的方式）**  \n- **线上申请**：很多平台支持“在线填表+上传材料”（比如官网的“救助申请入口”、公众号菜单栏的“我要求助”），按提示填写个人信息、困难情况，上传材料照片即可。提交后记得保存“申请编号”，方便后续查询进度。  \n- **线下申请**：如果不方便线上操作，可带齐材料原件和复印件，到救助机构办公点（或社区/村委会代为提交），工作人员会指导您填写纸质申请表，核对材料后会给您“回执单”（上面有审核时间和联系方式）。  \n\n\n### **第四步：配合审核，耐心等待反馈**  \n提交后，机构会在1-15个工作日内（具体看项目要求）审核材料，可能会通过电话、微信联系您补充材料，或安排工作人员家访核实情况（比如了解家庭实际居住条件、困难真实性）。这时候如实说明情况就好，不用紧张~  \n\n如果材料没问题，审核通过后，机构会告知您救助金额、拨款方式（比如直接打到银行卡，或对接物资帮助）；如果暂时不符合条件，也会说明原因，并可能推荐其他帮扶渠道（比如公益众筹、专项基金等）。  \n\n\n### **最后想对你说**  \n申请过程中如果遇到任何卡壳（比如不知道去哪里开证明、材料不齐），随时联系我们的客服（电话XXX-XXXXXXX）或私信留言，我们会一步步陪您梳理。困难只是暂时的，愿意伸手求助的您，已经在靠近光的路上了——我们和很多人都在为您加油呀！ 🌟', 'general', '2025-06-28 01:22:01');
INSERT INTO `conversations` VALUES (25, NULL, 'web-chat-1751046115841', '如何进行捐赠？', '非常感谢您愿意伸出援手，您的每一份善意都能为需要帮助的生命带来温暖和希望～以下是具体的捐赠指引，您可以根据自己的情况选择合适的方式：\n\n\n### **第一步：选择正规的救助平台**  \n为了确保您的爱心切实帮助到受助对象，建议通过经过认证的正规平台捐赠（比如我们爱心救助平台，已通过民政部门备案，所有项目资金流向全程公开可查）。您可以通过官方网站、微信公众号/小程序、APP等官方渠道进入，避免点击非官方链接，保护资金安全。\n\n\n### **第二步：选择您想支持的救助项目**  \n进入平台后，首页会展示各类救助项目（比如流浪动物医疗救助、困境儿童生活帮扶、孤寡老人温暖包计划等）。点击项目卡片，您可以查看 **详细介绍**：包括受助对象的故事、当前所需资金、已筹进度、救助计划等。如果您有特别关注的领域（比如动物救助、儿童关怀），也可以通过“分类筛选”快速找到对应的项目哦～\n\n\n### **第三步：选择捐赠方式并完成操作**  \n根据项目需求，您可以选择 **资金捐赠** 或 **物资捐赠**（部分项目接受物资，如食品、衣物、医疗用品等）：  \n\n#### ▶ **资金捐赠（最常用）**  \n  1. 点击项目页面的 **“立即捐赠”** 按钮，输入您想捐赠的金额（金额不限，1元也是爱～）；  \n  2. 选择支付方式（微信支付、支付宝、银行卡转账等），按提示完成支付；  \n  3. 支付成功后，系统会自动生成 **电子捐赠证书**（含捐赠编号、金额、时间），您可以保存或分享，作为爱心纪念。  \n\n#### ▶ **物资捐赠（部分项目支持）**  \n  如果项目标注“接受物资捐赠”，您可以：  \n  1. 查看项目详情中的 **“物资需求清单”**（比如流浪动物救助可能需要狗粮、 blankets、医疗用品等，会注明规格和数量）；  \n  2. 通过平台客服（电话/在线咨询）确认物资接收地址和注意事项（比如是否需要全新物品、是否支持快递到付等）；  \n  3. 打包物资并寄出，记得在包裹中留下您的联系方式，方便我们后续反馈物资使用情况～  \n\n\n### **第四步：了解捐赠后续进展**  \n捐赠后，您可以通过以下方式关注项目动态：  \n- **定期反馈**：平台会每月在项目页面更新进展（比如受助动物的康复情况、儿童收到资助后的生活变化等），您也可以在“个人中心-我的捐赠”中查看您支持的项目；  \n- **透明公示**：所有资金使用明细（如医疗费用、物资采购清单）会定期通过平台官网/公众号公示，确保每一分钱都用在实处；  \n- **咨询客服**：如果您有任何疑问（比如捐赠凭证补发、项目进度咨询），随时联系我们的24小时客服（电话：XXX-XXXXXXX，或公众号内“在线客服”），我们会耐心为您解答～  \n\n\n最后想对您说：无论捐赠金额多少、形式如何，您的这份善意都像一束光，照亮了需要帮助的角落。如果您身边有同样愿意参与公益的朋友，也欢迎分享给他们——爱心汇聚，就能让更多生命被温暖～  \n\n再次感谢您的信任与善良，期待与您一起，用行动守护每一个需要帮助的“TA”～ ❤️', 'general', '2025-06-28 01:42:25');

-- ----------------------------
-- Table structure for donations
-- ----------------------------
DROP TABLE IF EXISTS `donations`;
CREATE TABLE `donations`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '捐赠ID',
  `user_id` bigint NOT NULL COMMENT '捐赠用户ID',
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `amount` decimal(10, 2) NOT NULL COMMENT '捐赠金额',
  `payment_method` enum('alipay','wechat','bank') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'alipay' COMMENT '支付方式: alipay-支付宝, wechat-微信, bank-银行转账',
  `transaction_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '交易流水号',
  `message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '捐赠留言',
  `is_anonymous` tinyint NULL DEFAULT 0 COMMENT '是否匿名: 0-否, 1-是',
  `status` enum('pending','success','failed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'pending' COMMENT '状态: pending-待支付, success-成功, failed-失败',
  `donation_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '捐赠时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_project_id`(`project_id` ASC) USING BTREE,
  INDEX `idx_donation_time`(`donation_time` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  CONSTRAINT `donations_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `donations_ibfk_2` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '捐赠记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of donations
-- ----------------------------
INSERT INTO `donations` VALUES (1, 2, 1, 1000.00, 'alipay', 'ALI20241201001', '希望小明早日康复！', 0, 'success', '2024-12-01 10:30:00');
INSERT INTO `donations` VALUES (2, 2, 1, 500.00, 'wechat', 'WX20241201002', '加油！', 0, 'success', '2024-12-01 14:20:00');
INSERT INTO `donations` VALUES (3, 2, 2, 2000.00, 'alipay', 'ALI20241202001', '支持教育事业', 1, 'success', '2024-12-02 09:15:00');
INSERT INTO `donations` VALUES (4, 2, 3, 1500.00, 'bank', 'BANK20241203001', '天灾无情人有情', 0, 'success', '2024-12-03 16:45:00');
INSERT INTO `donations` VALUES (5, 1, 5, 123.00, 'alipay', NULL, '123', 0, 'success', '2025-06-28 00:56:45');
INSERT INTO `donations` VALUES (6, 1, 13, 123.00, 'alipay', NULL, NULL, 0, 'failed', '2025-06-29 16:58:56');
INSERT INTO `donations` VALUES (7, 3, 13, 123.00, 'alipay', NULL, NULL, 0, 'failed', '2025-06-29 17:17:58');
INSERT INTO `donations` VALUES (8, 3, 13, 111.00, 'alipay', NULL, NULL, 0, 'pending', '2025-06-29 18:05:01');
INSERT INTO `donations` VALUES (9, 3, 13, 123.00, 'alipay', NULL, NULL, 0, 'pending', '2025-06-29 18:05:05');
INSERT INTO `donations` VALUES (10, 3, 13, 1234.00, 'alipay', NULL, NULL, 0, 'pending', '2025-06-29 18:05:09');
INSERT INTO `donations` VALUES (11, 3, 13, 1.00, 'alipay', NULL, NULL, 0, 'pending', '2025-06-29 18:05:31');
INSERT INTO `donations` VALUES (12, 1, 13, 111.00, 'alipay', NULL, NULL, 0, 'success', '2025-06-29 18:46:15');
INSERT INTO `donations` VALUES (13, 1, 13, 123.00, 'alipay', NULL, NULL, 0, 'failed', '2025-06-29 18:46:40');
INSERT INTO `donations` VALUES (14, 1, 13, 123.00, 'alipay', NULL, NULL, 0, 'pending', '2025-06-29 18:47:19');
INSERT INTO `donations` VALUES (15, 1, 13, 1.00, 'alipay', NULL, NULL, 0, 'pending', '2025-06-29 18:49:45');

-- ----------------------------
-- Table structure for help_requests
-- ----------------------------
DROP TABLE IF EXISTS `help_requests`;
CREATE TABLE `help_requests`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `user_id` bigint NOT NULL COMMENT '申请用户ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '申请标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '详细描述',
  `category` enum('medical','education','disaster','poverty') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '申请分类',
  `amount_needed` decimal(10, 2) NULL DEFAULT NULL COMMENT '所需金额',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系电话',
  `contact_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系地址',
  `proof_images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '证明材料图片(JSON格式)',
  `status` enum('pending','approved','rejected','processing') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'pending' COMMENT '状态: pending-待审核, approved-已通过, rejected-已拒绝, processing-处理中',
  `admin_response` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '管理员回复',
  `admin_id` bigint NULL DEFAULT NULL COMMENT '处理管理员ID',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `admin_id`(`admin_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_category`(`category` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  CONSTRAINT `help_requests_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `help_requests_ibfk_2` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '救助申请表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of help_requests
-- ----------------------------
INSERT INTO `help_requests` VALUES (1, 2, '父亲突发心脏病急需手术费', '我父亲突发急性心肌梗塞，需要立即进行心脏搭桥手术，手术费用约15万元，家庭经济困难，恳请社会爱心人士帮助。', 'medical', 150000.00, '13800138001', '北京市朝阳区某某街道', '[\"https://example.com/proof1.jpg\", \"https://example.com/proof2.jpg\"]', 'approved', '经审核，情况属实，已安排志愿者跟进。', 1, '2025-06-25 10:12:12', '2025-06-25 10:12:12');
INSERT INTO `help_requests` VALUES (2, 2, '失业后生活困难求助', '因公司倒闭失业3个月，积蓄用尽，家中还有老人和孩子需要照顾，生活陷入困境。', 'poverty', 5000.00, '13800138001', '上海市浦东新区某某小区', '[\"https://example.com/proof3.jpg\"]', 'approved', '申请已通过审核', 1, '2025-06-25 10:12:12', '2025-06-27 15:29:18');
INSERT INTO `help_requests` VALUES (3, 1, '123321', '123333333333333333333333333333333333333333333', 'education', NULL, '18859919726', '123', NULL, 'approved', '申请已通过审核', 1, '2025-06-27 18:09:47', '2025-06-27 18:10:47');
INSERT INTO `help_requests` VALUES (4, 1, '123233333333', '11111111111111111111111111111', 'education', NULL, '18859919726', '123', NULL, 'pending', NULL, NULL, '2025-06-27 18:10:28', '2025-06-27 18:10:28');
INSERT INTO `help_requests` VALUES (5, 1, '123333333333333333', '333333333333333333333333333333333', 'medical', NULL, '18859919726', '123', NULL, 'approved', '申请已通过审核', 1, '2025-06-27 18:34:15', '2025-06-27 18:34:46');
INSERT INTO `help_requests` VALUES (6, 1, '1233333333333', '123333333333333333333333', 'medical', NULL, '18859919726', '123', NULL, 'approved', '申请已通过审核', 1, '2025-06-28 01:05:40', '2025-06-29 16:41:02');
INSERT INTO `help_requests` VALUES (7, 1, '111111111', '111111111111111111111111111', 'disaster', 100.00, '18859919726', '123', '[\"/documents/2025/06/document_temp_20250628011105_e0f01e6b.jpg\"]', 'approved', '申请已通过审核', 1, '2025-06-28 01:11:06', '2025-06-28 01:11:42');
INSERT INTO `help_requests` VALUES (8, 3, '测试1233321', '测试123测试123测试123测试123测试123测试123', 'medical', 1000.00, '18859919726', '测试123', NULL, 'approved', '申请已通过审核', 1, '2025-06-29 16:57:22', '2025-06-29 16:58:20');

-- ----------------------------
-- Table structure for projects
-- ----------------------------
DROP TABLE IF EXISTS `projects`;
CREATE TABLE `projects`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '项目标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '项目描述',
  `category` enum('medical','education','disaster','poverty') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '项目分类: medical-医疗, education-教育, disaster-灾难, poverty-扶贫',
  `target_amount` decimal(12, 2) NOT NULL COMMENT '目标金额',
  `current_amount` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '当前筹集金额',
  `images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '项目图片(JSON格式存储多张图片URL)',
  `contact_info` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系信息',
  `status` enum('active','completed','closed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'active' COMMENT '状态: active-进行中, completed-已完成, closed-已关闭',
  `admin_id` bigint NOT NULL COMMENT '发布管理员ID',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `admin_id`(`admin_id` ASC) USING BTREE,
  INDEX `idx_category`(`category` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  CONSTRAINT `projects_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '救助项目表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of projects
-- ----------------------------
INSERT INTO `projects` VALUES (1, '小明白血病治疗救助', '小明今年8岁，患有急性白血病，家庭经济困难，急需医疗费用支持。孩子病情严重，需要进行骨髓移植手术，医疗费用预计需要30万元。', 'medical', 300100.00, 86500.00, '[\"https://example.com/image1.jpg\",\" https://example.com/image2.jpg\"]', '联系人：小明妈妈，电话：138****1234', 'active', 1, '2025-06-25 10:12:12', '2025-06-27 11:31:59');
INSERT INTO `projects` VALUES (2, '山区儿童助学计划', '为贫困山区的孩子们提供学习用品和生活补助，帮助他们完成学业。目前有50名儿童需要资助，每人每年需要2000元。', 'education', 100000.00, 47000.00, '[\"https://example.com/edu1.jpg\", \"https://example.com/edu2.jpg\"]', '联系人：李老师，电话：139****5678', 'active', 1, '2025-06-25 10:12:12', '2025-06-25 10:12:12');
INSERT INTO `projects` VALUES (3, '洪灾紧急救援', '某地区遭受洪灾，100多户家庭受灾，急需生活物资和重建资金支持。', 'disaster', 200100.00, 121500.00, '[\"https://example.com/disaster1.jpg\"]', '联系人：救援队长王先生，电话：137****9012', 'completed', 1, '2025-06-25 10:12:12', '2025-06-27 16:52:40');
INSERT INTO `projects` VALUES (5, '测试123', '测试测试测试测试测试测试测试测试测试测试', 'medical', 10000.00, 300.00, '[\"http://localhost:8080/uploads/projects/2025/06/project_temp_20250627114525_db3d5b9c.png\"]', '测试测试测试测试测试', 'active', 1, '2025-06-27 11:45:39', '2025-06-27 11:45:39');
INSERT INTO `projects` VALUES (6, '父亲突发心脏病急需手术费', '我父亲突发急性心肌梗塞，需要立即进行心脏搭桥手术，手术费用约15万元，家庭经济困难，恳请社会爱心人士帮助。', 'medical', 150000.00, 0.00, NULL, '联系电话: 13800138001\n联系地址: 北京市朝阳区某某街道', 'active', 1, '2025-06-29 16:44:49', '2025-06-29 16:44:49');
INSERT INTO `projects` VALUES (7, '失业后生活困难求助', '因公司倒闭失业3个月，积蓄用尽，家中还有老人和孩子需要照顾，生活陷入困境。', 'poverty', 5000.00, 0.00, NULL, '联系电话: 13800138001\n联系地址: 上海市浦东新区某某小区', 'active', 1, '2025-06-29 16:44:49', '2025-06-29 16:44:49');
INSERT INTO `projects` VALUES (8, '123321', '123333333333333333333333333333333333333333333', 'education', 10000.00, 0.00, NULL, '联系电话: 18859919726\n联系地址: 123', 'active', 1, '2025-06-29 16:44:49', '2025-06-29 16:44:49');
INSERT INTO `projects` VALUES (9, '123333333333333333', '333333333333333333333333333333333', 'medical', 10000.00, 0.00, NULL, '联系电话: 18859919726\n联系地址: 123', 'active', 1, '2025-06-29 16:44:49', '2025-06-29 16:44:49');
INSERT INTO `projects` VALUES (10, '1233333333333', '123333333333333333333333', 'medical', 10000.00, 0.00, NULL, '联系电话: 18859919726\n联系地址: 123', 'active', 1, '2025-06-29 16:44:49', '2025-06-29 16:44:49');
INSERT INTO `projects` VALUES (11, '111111111', '111111111111111111111111111', 'disaster', 100.00, 0.00, NULL, '联系电话: 18859919726\n联系地址: 123', 'active', 1, '2025-06-29 16:44:49', '2025-06-29 16:44:49');
INSERT INTO `projects` VALUES (13, '测试1233321', '测试123测试123测试123测试123测试123测试123', 'medical', 1000.00, 111.00, NULL, '联系电话: 18859919726\n联系地址: 测试123', 'active', 1, '2025-06-29 16:58:20', '2025-06-29 18:46:30');

-- ----------------------------
-- Table structure for system_config
-- ----------------------------
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `config_key`(`config_key` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of system_config
-- ----------------------------
INSERT INTO `system_config` VALUES (1, 'platform_name', '温暖之家爱心救助平台', '平台名称', '2025-06-25 10:12:12', '2025-06-25 10:12:12');
INSERT INTO `system_config` VALUES (2, 'contact_email', '<EMAIL>', '联系邮箱', '2025-06-25 10:12:12', '2025-06-25 10:12:12');
INSERT INTO `system_config` VALUES (3, 'contact_phone', '************', '联系电话', '2025-06-25 10:12:12', '2025-06-25 10:12:12');
INSERT INTO `system_config` VALUES (4, 'ai_welcome_message', '您好！我是爱心助手小暖，我可以帮您了解救助项目、捐赠流程，或解答相关问题。请问需要什么帮助？', 'AI欢迎语', '2025-06-25 10:12:12', '2025-06-25 10:12:12');
INSERT INTO `system_config` VALUES (5, 'min_donation_amount', '1.00', '最小捐赠金额', '2025-06-25 10:12:12', '2025-06-25 10:12:12');
INSERT INTO `system_config` VALUES (6, 'max_donation_amount', '50000.00', '单次最大捐赠金额', '2025-06-25 10:12:12', '2025-06-25 10:12:12');

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码(加密)',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号',
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像URL',
  `role` enum('user','admin') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'user' COMMENT '角色: user-普通用户, admin-管理员',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态: 0-禁用, 1-正常',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE,
  UNIQUE INDEX `email`(`email` ASC) USING BTREE,
  INDEX `idx_username`(`username` ASC) USING BTREE,
  INDEX `idx_email`(`email` ASC) USING BTREE,
  INDEX `idx_role`(`role` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES (1, 'admin', '$2a$10$RhUqY8zN7HIp2e9TCX5lwOrmE8H0jbvkan3JDVQwfdAh4teJV7NmO', 'a***<EMAIL>', '18859919726', '系统管理员', '/avatars/2025/06/avatar_1_20250627175328_7508d469.png', 'admin', 1, '2025-06-25 10:12:11', '2025-06-27 17:53:28');
INSERT INTO `users` VALUES (2, 'testuser', '$2a$10$GGOV4qZF0uVykZkBnoBL1eG3xusylTH09cgtmL0cSbVVIXGsDTwj6', '<EMAIL>', '13800138001', '测试用户', NULL, 'user', 1, '2025-06-25 10:12:11', '2025-06-27 14:54:24');
INSERT INTO `users` VALUES (3, '123', '$2a$10$nhHLboHZtL3oF4j65.By/eq5wc.tV4Rd8nV1II5xMqtWIqHcsvGIa', '1****@qq.com', NULL, '1233', 'http://localhost:8080/uploads/avatars/2025/06/avatar_3_20250627173415_51955589.png', 'user', 1, '2025-06-26 11:32:21', '2025-06-28 01:01:40');

-- ----------------------------
-- Triggers structure for table donations
-- ----------------------------
DROP TRIGGER IF EXISTS `update_project_amount`;
delimiter ;;
CREATE TRIGGER `update_project_amount` AFTER INSERT ON `donations` FOR EACH ROW BEGIN
    IF NEW.status = 'success' THEN
        UPDATE projects 
        SET current_amount = current_amount + NEW.amount 
        WHERE id = NEW.project_id;
    END IF;
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
