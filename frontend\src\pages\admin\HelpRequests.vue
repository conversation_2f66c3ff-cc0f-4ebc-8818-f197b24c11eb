<template>
  <div class="admin-help-requests">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">救助申请管理</h1>
        <p class="page-description">管理和审核用户的救助申请</p>
      </div>
      <div class="header-right">
        <el-button @click="handleRefresh">
          <el-icon>
            <Refresh />
          </el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 快速统计 -->
    <div class="stats-section">
      <el-row :gutter="24">
        <el-col :span="6">
          <el-card class="stats-card pending">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon>
                  <Clock />
                </el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ stats.pending || 0 }}</div>
                <div class="stats-label">待审核</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card approved">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon>
                  <Check />
                </el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ stats.approved || 0 }}</div>
                <div class="stats-label">已通过</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card rejected">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon>
                  <Close />
                </el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ stats.rejected || 0 }}</div>
                <div class="stats-label">已拒绝</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card total">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon>
                  <Document />
                </el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ stats.total || 0 }}</div>
                <div class="stats-label">总申请</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <el-card>
        <el-form :model="filters" inline>
          <el-form-item label="搜索申请">
            <el-input v-model="filters.keyword" placeholder="输入标题或描述关键词" style="width: 250px" clearable
              @keyup.enter="handleSearch">
              <template #prefix>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="申请分类">
            <el-select v-model="filters.category" placeholder="选择分类" style="width: 150px" clearable>
              <el-option label="医疗救助" value="medical" />
              <el-option label="教育助学" value="education" />
              <el-option label="灾难救援" value="disaster" />
              <el-option label="扶贫助困" value="poverty" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>

          <el-form-item label="申请状态">
            <el-select v-model="filters.status" placeholder="选择状态" style="width: 120px" clearable>
              <el-option label="待审核" value="pending" />
              <el-option label="处理中" value="processing" />
              <el-option label="已通过" value="approved" />
              <el-option label="已拒绝" value="rejected" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon>
                <Search />
              </el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 申请列表 -->
    <div class="table-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>申请列表</span>
            <div class="header-actions">
              <span class="total-count">共 {{ pagination.total }} 个申请</span>
            </div>
          </div>
        </template>

        <el-table :data="requests" v-loading="loading" stripe style="width: 100%">
          <el-table-column prop="id" label="ID" width="80" />

          <el-table-column label="申请信息" min-width="250">
            <template #default="{ row }">
              <div class="request-info">
                <div class="request-title">{{ row.title }}</div>
                <div class="request-meta">
                  <span class="user-name">{{ row.username || '未知用户' }}</span>
                  <span class="separator">•</span>
                  <span class="amount">需要 ¥{{ formatAmount(row.amountNeeded) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="分类" width="120">
            <template #default="{ row }">
              <el-tag :type="getCategoryType(row.category)" size="small">
                {{ getCategoryText(row.category) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="申请时间" width="160">
            <template #default="{ row }">
              {{ formatTime(row.createTime) }}
            </template>
          </el-table-column>

          <el-table-column label="审核时间" width="160">
            <template #default="{ row }">
              {{ formatTime(row.reviewTime) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button text type="primary" size="small" @click="handleView(row)">
                  <el-icon>
                    <View />
                  </el-icon>
                  查看
                </el-button>

                <el-button v-if="row.status === 'pending' || row.status === 'processing'" text type="success"
                  size="small" @click="handleQuickApprove(row)">
                  <el-icon>
                    <Check />
                  </el-icon>
                  通过
                </el-button>

                <el-button v-if="row.status === 'pending' || row.status === 'processing'" text type="danger"
                  size="small" @click="handleQuickReject(row)">
                  <el-icon>
                    <Close />
                  </el-icon>
                  拒绝
                </el-button>

                <el-dropdown @command="(command) => handleDropdownCommand(command, row)" trigger="click">
                  <el-button text type="primary" size="small" class="more-btn">
                    <el-icon>
                      <MoreFilled />
                    </el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="urgent">
                        <el-icon>
                          <Warning />
                        </el-icon>
                        标记紧急
                      </el-dropdown-item>
                      <el-dropdown-item command="similar" divided>
                        <el-icon>
                          <Search />
                        </el-icon>
                        查看相似申请
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination v-model:current-page="pagination.current" v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50, 100]" :total="pagination.total" layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange" @current-change="handlePageChange" />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    Search,
    Refresh,
    Clock,
    Check,
    Close,
    Document,
    View,
    MoreFilled,
    Warning
  } from '@element-plus/icons-vue'
  import {
    getHelpRequestList,
    reviewHelpRequest,
    markAsUrgent
  } from '@/api/admin'

  const router = useRouter()

  // 响应式数据
  const loading = ref(false)
  const requests = ref([])
  const stats = ref({
    pending: 0,
    approved: 0,
    rejected: 0,
    total: 0
  })

  // 筛选条件
  const filters = reactive({
    keyword: '',
    category: '',
    status: ''
  })

  // 分页信息
  const pagination = reactive({
    current: 1,
    size: 20,
    total: 0
  })

  // 加载申请列表
  const loadRequests = async () => {
    try {
      loading.value = true

      const params = {
        current: pagination.current,
        size: pagination.size,
        keyword: filters.keyword || undefined,
        category: filters.category || undefined,
        status: filters.status || undefined
      }

      const response = await getHelpRequestList(params)

      if (response && response.records) {
        requests.value = response.records
        pagination.total = response.total || 0

        // 计算统计数据
        calculateStats(response.records)
      }

    } catch (error) {
      console.error('加载申请列表失败:', error)
      ElMessage.error('加载申请列表失败')
    } finally {
      loading.value = false
    }
  }

  // 计算统计数据
  const calculateStats = (data) => {
    stats.value = {
      pending: data.filter(item => item.status === 'pending').length,
      approved: data.filter(item => item.status === 'approved').length,
      rejected: data.filter(item => item.status === 'rejected').length,
      total: data.length
    }
  }

  // 搜索处理
  const handleSearch = () => {
    pagination.current = 1
    loadRequests()
  }

  // 重置筛选
  const handleReset = () => {
    filters.keyword = ''
    filters.category = ''
    filters.status = ''
    pagination.current = 1
    loadRequests()
  }

  // 刷新
  const handleRefresh = () => {
    loadRequests()
  }

  // 分页处理
  const handlePageChange = (page) => {
    pagination.current = page
    loadRequests()
  }

  const handleSizeChange = (size) => {
    pagination.size = size
    pagination.current = 1
    loadRequests()
  }

  // 查看申请详情
  const handleView = (row) => {
    router.push(`/admin/help-requests/${row.id}`)
  }

  // 快速通过
  const handleQuickApprove = async (row) => {
    try {
      await ElMessageBox.confirm(
        `确定要通过申请"${row.title}"吗？`,
        '确认通过',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'success'
        }
      )

      await reviewHelpRequest({
        requestId: row.id,
        status: 'approved',
        adminResponse: '申请已通过审核'
      })

      ElMessage.success('申请已通过')
      loadRequests()

    } catch (error) {
      if (error !== 'cancel') {
        console.error('通过申请失败:', error)
        if (error.message) {
          ElMessage.error(error.message)
        }
      }
    }
  }

  // 快速拒绝
  const handleQuickReject = async (row) => {
    try {
      const { value: reason } = await ElMessageBox.prompt(
        `请输入拒绝申请"${row.title}"的原因：`,
        '确认拒绝',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPlaceholder: '请输入拒绝原因',
          inputValidator: (value) => {
            if (!value || value.trim().length === 0) {
              return '请输入拒绝原因'
            }
            return true
          }
        }
      )

      await reviewHelpRequest({
        requestId: row.id,
        status: 'rejected',
        adminResponse: reason
      })

      ElMessage.success('申请已拒绝')
      loadRequests()

    } catch (error) {
      if (error !== 'cancel') {
        console.error('拒绝申请失败:', error)
        if (error.message) {
          ElMessage.error(error.message)
        }
      }
    }
  }

  // 下拉菜单操作
  const handleDropdownCommand = async (command, row) => {
    switch (command) {
      case 'urgent':
        await handleMarkUrgent(row)
        break
      case 'similar':
        await handleViewSimilar(row)
        break
    }
  }

  // 标记紧急
  const handleMarkUrgent = async (row) => {
    try {
      await ElMessageBox.confirm(
        `确定要将申请"${row.title}"标记为紧急吗？`,
        '确认标记',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await markAsUrgent(row.id)
      ElMessage.success('已标记为紧急申请')
      loadRequests()

    } catch (error) {
      if (error !== 'cancel') {
        console.error('标记紧急失败:', error)
        if (error.message) {
          ElMessage.error(error.message)
        }
      }
    }
  }

  // 查看相似申请
  const handleViewSimilar = async (row) => {
    try {
      // TODO: 实现查看相似申请功能
      ElMessage.info('查看相似申请功能开发中...')
    } catch (error) {
      console.error('查看相似申请失败:', error)
    }
  }

  // 工具函数
  const getCategoryType = (category) => {
    const typeMap = {
      medical: 'danger',
      education: 'primary',
      disaster: 'warning',
      poverty: 'success',
      other: 'info'
    }
    return typeMap[category] || ''
  }

  const getCategoryText = (category) => {
    const textMap = {
      medical: '医疗救助',
      education: '教育助学',
      disaster: '灾难救援',
      poverty: '扶贫助困',
      other: '其他'
    }
    return textMap[category] || category
  }

  const getStatusType = (status) => {
    const typeMap = {
      pending: 'warning',
      processing: 'primary',
      approved: 'success',
      rejected: 'danger'
    }
    return typeMap[status] || ''
  }

  const getStatusText = (status) => {
    const textMap = {
      pending: '待审核',
      processing: '处理中',
      approved: '已通过',
      rejected: '已拒绝'
    }
    return textMap[status] || status
  }

  const formatTime = (timeString) => {
    if (!timeString) return '未知'
    return new Date(timeString).toLocaleString('zh-CN')
  }

  const formatAmount = (amount) => {
    if (!amount) return '0'
    return amount.toLocaleString()
  }

  // 页面初始化
  onMounted(() => {
    loadRequests()
  })
</script>

<style scoped>
  .admin-help-requests {
    padding: 24px;
    background: #f5f5f5;
    min-height: 100vh;
  }

  /* 页面头部 */
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .page-title {
    font-size: 24px;
    font-weight: bold;
    color: #2D3748;
    margin: 0 0 8px 0;
  }

  .page-description {
    color: #718096;
    margin: 0;
    font-size: 14px;
  }

  /* 统计区域 */
  .stats-section {
    margin-bottom: 24px;
  }

  .stats-card {
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
  }

  .stats-card:hover {
    transform: translateY(-2px);
  }

  .stats-content {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
  }

  .stats-card.pending .stats-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  .stats-card.approved .stats-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .stats-card.rejected .stats-icon {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  }

  .stats-card.total .stats-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .stats-info {
    flex: 1;
  }

  .stats-value {
    font-size: 24px;
    font-weight: bold;
    color: #2D3748;
    margin-bottom: 4px;
  }

  .stats-label {
    font-size: 14px;
    color: #718096;
  }

  /* 筛选区域 */
  .filter-section {
    margin-bottom: 24px;
  }

  .filter-section .el-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  /* 表格区域 */
  .table-section .el-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .total-count {
    color: #718096;
    font-size: 14px;
  }

  /* 申请信息 */
  .request-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .request-title {
    font-weight: 500;
    color: #2D3748;
    font-size: 14px;
  }

  .request-meta {
    font-size: 12px;
    color: #718096;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .separator {
    color: #CBD5E0;
  }

  .amount {
    color: #E53E3E;
    font-weight: 500;
  }

  /* 操作按钮 */
  .action-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
  }

  .more-btn {
    padding: 4px 8px;
  }

  /* 分页 */
  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .admin-help-requests {
      padding: 16px;
    }

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .stats-section .el-col {
      margin-bottom: 16px;
    }

    .filter-section .el-form {
      flex-direction: column;
      align-items: stretch;
    }

    .filter-section .el-form-item {
      margin-right: 0;
      margin-bottom: 16px;
    }

    .action-buttons {
      flex-direction: column;
      gap: 4px;
    }
  }
</style>