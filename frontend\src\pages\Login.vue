<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-header">
        <el-icon class="logo-icon">
          <Heart />
        </el-icon>
        <h1>爱心救助平台</h1>
        <p>传递温暖，传递爱心</p>
      </div>

      <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" class="login-form"
        @submit.prevent="handleLogin">
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" placeholder="请输入用户名" size="large" prefix-icon="User" />
        </el-form-item>

        <el-form-item prop="password">
          <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" size="large" prefix-icon="Lock"
            show-password />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" size="large" class="login-button" :loading="loading" @click="handleLogin">
            登录
          </el-button>
        </el-form-item>

        <div class="login-footer">
          <span>还没有账号？</span>
          <el-link type="primary" @click="$router.push('/register')">
            立即注册
          </el-link>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage } from 'element-plus'
  import { useUserStore } from '@/stores/user'

  const router = useRouter()
  const userStore = useUserStore()
  const loginFormRef = ref()
  const loading = ref(false)

  const loginForm = reactive({
    username: '',
    password: ''
  })

  const loginRules = {
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' }
    ],
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
    ]
  }

  const handleLogin = async () => {
    try {
      await loginFormRef.value.validate()
      loading.value = true

      // 调用真实的登录API
      await userStore.loginAction(loginForm)

      ElMessage.success('登录成功')
      router.push('/')
    } catch (error) {
      console.error('登录失败:', error)
      ElMessage.error(error.message || '登录失败，请检查用户名和密码')
    } finally {
      loading.value = false
    }
  }
</script>

<style scoped>
  .login-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
  }

  .login-container {
    background: white;
    border-radius: 12px;
    padding: 40px;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .login-header {
    text-align: center;
    margin-bottom: 40px;
  }

  .logo-icon {
    font-size: 48px;
    color: #e74c3c;
    margin-bottom: 16px;
  }

  .login-header h1 {
    font-size: 24px;
    color: #2c3e50;
    margin-bottom: 8px;
  }

  .login-header p {
    color: #7f8c8d;
    font-size: 14px;
  }

  .login-form {
    width: 100%;
  }

  .login-button {
    width: 100%;
  }

  .login-footer {
    text-align: center;
    margin-top: 20px;
    color: #7f8c8d;
    font-size: 14px;
  }

  .login-footer .el-link {
    margin-left: 8px;
  }
</style>