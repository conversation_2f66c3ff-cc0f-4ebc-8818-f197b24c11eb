package com.example.demo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.entity.Donation;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 捐赠服务接口
 * 定义捐赠相关的业务操作
 */
public interface DonationService {

    /**
     * 创建捐赠记录
     * @param donation 捐赠信息
     * @return 创建成功的捐赠记录
     */
    Donation createDonation(Donation donation);

    /**
     * 处理捐赠支付
     * @param donationId 捐赠ID
     * @param transactionId 交易流水号
     * @return 是否处理成功
     */
    boolean processDonationPayment(Long donationId, String transactionId);

    /**
     * 取消捐赠
     * @param donationId 捐赠ID
     * @param userId 用户ID
     * @return 是否取消成功
     */
    boolean cancelDonation(Long donationId, Long userId);

    /**
     * 根据ID查询捐赠记录
     * @param id 捐赠ID
     * @return 捐赠记录
     */
    Donation findById(Long id);

    /**
     * 根据用户ID查询捐赠记录
     * @param userId 用户ID
     * @return 捐赠记录列表
     */
    List<Donation> findByUserId(Long userId);

    /**
     * 根据项目ID查询捐赠记录
     * @param projectId 项目ID
     * @return 捐赠记录列表
     */
    List<Donation> findByProjectId(Long projectId);

    /**
     * 分页查询捐赠记录
     * @param page 分页参数
     * @param userId 用户ID
     * @param projectId 项目ID
     * @param status 捐赠状态
     * @param paymentMethod 支付方式
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页捐赠记录列表
     */
    IPage<Donation> findDonationsWithConditions(Page<Donation> page, Long userId, Long projectId,
                                               String status, String paymentMethod,
                                               LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询用户总捐赠金额
     * @param userId 用户ID
     * @return 总捐赠金额
     */
    BigDecimal getUserTotalDonationAmount(Long userId);

    /**
     * 查询项目总捐赠金额
     * @param projectId 项目ID
     * @return 总捐赠金额
     */
    BigDecimal getProjectTotalDonationAmount(Long projectId);

    /**
     * 查询项目捐赠人数
     * @param projectId 项目ID
     * @return 捐赠人数
     */
    Long getProjectDonorCount(Long projectId);

    /**
     * 查询最近的捐赠记录
     * @param limit 限制数量
     * @param isAnonymous 是否只查询非匿名捐赠
     * @return 最近捐赠记录列表
     */
    List<Donation> findRecentDonations(Integer limit, Boolean isAnonymous);

    /**
     * 查询大额捐赠记录
     * @param limit 限制数量
     * @return 大额捐赠记录列表
     */
    List<Donation> findLargeDonations(Integer limit);

    /**
     * 统计捐赠数量
     * @param status 捐赠状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 捐赠数量
     */
    Long countDonations(String status, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计捐赠总金额
     * @param status 捐赠状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 捐赠总金额
     */
    BigDecimal sumDonationAmount(String status, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 更新捐赠状态
     * @param donationId 捐赠ID
     * @param status 新状态
     * @return 是否更新成功
     */
    boolean updateDonationStatus(Long donationId, String status);

    /**
     * 验证捐赠权限
     * @param donationId 捐赠ID
     * @param userId 用户ID
     * @return 是否有权限
     */
    boolean hasDonationPermission(Long donationId, Long userId);

    /**
     * 获取捐赠统计信息（按支付方式）
     * @return 支付方式统计列表
     */
    List<Object> getDonationStatsByPaymentMethod();

    /**
     * 获取每日捐赠统计
     * @return 每日捐赠统计列表
     */
    List<Object> getDailyDonationStats();

    /**
     * 获取捐赠概览统计
     * @return 捐赠概览统计
     */
    Object getDonationOverviewStats();

    /**
     * 生成捐赠证书
     * @param donationId 捐赠ID
     * @return 捐赠证书信息
     */
    Object generateDonationCertificate(Long donationId);

    /**
     * 检查用户是否可以捐赠
     * @param userId 用户ID
     * @param projectId 项目ID
     * @param amount 捐赠金额
     * @return 是否可以捐赠
     */
    boolean canUserDonate(Long userId, Long projectId, BigDecimal amount);

    /**
     * 获取用户捐赠统计
     * @param userId 用户ID
     * @return 用户捐赠统计
     */
    Object getUserDonationStats(Long userId);

    /**
     * 获取项目捐赠统计
     * @param projectId 项目ID
     * @return 项目捐赠统计
     */
    Object getProjectDonationStats(Long projectId);

    /**
     * 处理捐赠退款
     * @param donationId 捐赠ID
     * @param reason 退款原因
     * @param adminId 管理员ID
     * @return 是否退款成功
     */
    boolean refundDonation(Long donationId, String reason, Long adminId);

    /**
     * 批量处理捐赠状态更新
     * @param donationIds 捐赠ID列表
     * @param status 新状态
     * @return 是否更新成功
     */
    boolean batchUpdateDonationStatus(List<Long> donationIds, String status);

    /**
     * 获取今日捐赠统计
     * @return 今日捐赠统计
     */
    Object getTodayDonationStats();
}
