package com.example.demo.controller;

import com.example.demo.dto.ApiResponse;
import com.example.demo.dto.request.UserLoginDTO;
import com.example.demo.dto.response.UserLoginResponseDTO;
import com.example.demo.dto.converter.DTOConverter;
import com.example.demo.entity.User;
import com.example.demo.security.JwtTokenProvider;
import com.example.demo.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

/**
 * 认证相关Controller
 * 提供JWT认证相关的API接口
 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
@Tag(name = "认证管理", description = "JWT认证相关API")
@Validated
public class AuthController extends BaseController {

    @Autowired
    private UserService userService;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户登录获取JWT访问令牌")
    public ApiResponse<UserLoginResponseDTO> login(@Valid @RequestBody UserLoginDTO loginDTO) {
        try {
            logOperation("用户登录", loginDTO.getUsername());
            
            // 用户登录验证
            User user = userService.login(loginDTO.getUsername(), loginDTO.getPassword());
            
            // 生成JWT令牌
            String accessToken = jwtTokenProvider.generateAccessToken(user.getId(), user.getUsername(), user.getRole());
            String refreshToken = jwtTokenProvider.generateRefreshToken(user.getId(), user.getUsername());
            
            // 创建登录响应
            UserLoginResponseDTO responseDTO = new UserLoginResponseDTO();
            responseDTO.setAccessToken(accessToken);
            responseDTO.setRefreshToken(refreshToken);
            responseDTO.setExpiresIn(7200L); // 2小时
            responseDTO.setUserInfo(DTOConverter.convertToUserResponseDTO(user));
            
            return success("登录成功", responseDTO);
        } catch (Exception e) {
            log.error("用户登录失败", e);
            return error("登录失败：" + e.getMessage());
        }
    }

    @PostMapping("/refresh")
    @Operation(summary = "刷新访问令牌", description = "使用刷新令牌获取新的访问令牌")
    public ApiResponse<UserLoginResponseDTO> refreshToken(
            @Parameter(description = "刷新令牌") @RequestParam String refreshToken) {
        try {
            logOperation("刷新访问令牌");
            
            // 验证刷新令牌并生成新的访问令牌
            String newAccessToken = jwtTokenProvider.refreshAccessToken(refreshToken);
            
            // 从刷新令牌中获取用户信息
            Long userId = jwtTokenProvider.getUserIdFromToken(refreshToken);
            String username = jwtTokenProvider.getUsernameFromToken(refreshToken);
            
            // 获取用户详细信息
            User user = userService.findById(userId);
            if (user == null) {
                return error("用户不存在");
            }
            
            // 创建响应
            UserLoginResponseDTO responseDTO = new UserLoginResponseDTO();
            responseDTO.setAccessToken(newAccessToken);
            responseDTO.setRefreshToken(refreshToken); // 刷新令牌保持不变
            responseDTO.setExpiresIn(7200L); // 2小时
            responseDTO.setUserInfo(DTOConverter.convertToUserResponseDTO(user));
            
            return success("令牌刷新成功", responseDTO);
        } catch (Exception e) {
            log.error("刷新令牌失败", e);
            return error("令牌刷新失败：" + e.getMessage());
        }
    }

    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户登出，将令牌加入黑名单")
    public ApiResponse<Void> logout(HttpServletRequest request) {
        try {
            Long userId = getCurrentUserId();
            logOperation("用户登出", userId);
            
            // 从请求头中获取访问令牌
            String authHeader = request.getHeader("Authorization");
            String accessToken = jwtTokenProvider.extractTokenFromHeader(authHeader);
            
            if (accessToken != null) {
                // 将访问令牌加入黑名单
                jwtTokenProvider.blacklistToken(accessToken);
            }
            
            return success("登出成功", null);
        } catch (Exception e) {
            log.error("用户登出失败", e);
            return error("登出失败：" + e.getMessage());
        }
    }

    @GetMapping("/validate")
    @Operation(summary = "验证令牌", description = "验证JWT令牌的有效性")
    public ApiResponse<Object> validateToken(HttpServletRequest request) {
        try {
            // 从请求头中获取令牌
            String authHeader = request.getHeader("Authorization");
            String token = jwtTokenProvider.extractTokenFromHeader(authHeader);
            
            if (token == null) {
                return error("缺少访问令牌");
            }
            
            // 验证令牌
            boolean isValid = jwtTokenProvider.validateToken(token);
            
            if (isValid) {
                // 获取令牌信息
                Long userId = jwtTokenProvider.getUserIdFromToken(token);
                String username = jwtTokenProvider.getUsernameFromToken(token);
                String role = jwtTokenProvider.getRoleFromToken(token);
                long remainingTime = jwtTokenProvider.getRemainingValidTime(token);
                
                return success("令牌有效", new Object() {
                    public final Long userId = AuthController.this.jwtTokenProvider.getUserIdFromToken(token);
                    public final String username = AuthController.this.jwtTokenProvider.getUsernameFromToken(token);
                    public final String role = AuthController.this.jwtTokenProvider.getRoleFromToken(token);
                    public final long remainingTime = AuthController.this.jwtTokenProvider.getRemainingValidTime(token);
                    public final boolean valid = true;
                });
            } else {
                return error("令牌无效");
            }
        } catch (Exception e) {
            log.error("验证令牌失败", e);
            return error("令牌验证失败：" + e.getMessage());
        }
    }

    @GetMapping("/user-info")
    @Operation(summary = "获取当前用户信息", description = "根据JWT令牌获取当前用户信息")
    public ApiResponse<Object> getCurrentUserInfo() {
        try {
            Long userId = getCurrentUserId();
            String username = getCurrentUsername();
            String role = getCurrentUserRole();
            
            if (userId == null) {
                return unauthorized("未登录");
            }
            
            // 获取用户详细信息
            User user = userService.findById(userId);
            if (user == null) {
                return notFound("用户不存在");
            }
            
            return success(DTOConverter.convertToUserResponseDTO(user));
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return error("获取用户信息失败：" + e.getMessage());
        }
    }
}
