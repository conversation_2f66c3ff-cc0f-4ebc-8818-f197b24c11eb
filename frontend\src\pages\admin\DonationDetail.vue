<template>
  <div class="donation-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" class="back-btn">
          <el-icon>
            <ArrowLeft />
          </el-icon>
          返回捐赠列表
        </el-button>
        <div class="title-section">
          <h1 class="page-title">捐赠详情</h1>
          <p class="page-description">查看捐赠记录的详细信息</p>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="handleRefresh">
          <el-icon>
            <Refresh />
          </el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <div v-loading="loading" class="content-wrapper">
      <!-- 捐赠基本信息 -->
      <div class="info-section">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>捐赠信息</span>
              <div class="header-actions">
                <el-tag :type="getStatusType(donationInfo.status)" size="large">
                  {{ getStatusText(donationInfo.status) }}
                </el-tag>
              </div>
            </div>
          </template>

          <div class="donation-profile">
            <div class="amount-section">
              <div class="amount-display">
                <span class="currency">¥</span>
                <span class="amount">{{ formatAmount(donationInfo.amount) }}</span>
              </div>
              <div class="amount-label">捐赠金额</div>
            </div>

            <div class="info-grid">
              <div class="info-item">
                <label>捐赠ID</label>
                <span>{{ donationInfo.id }}</span>
              </div>
              <div class="info-item">
                <label>捐赠人</label>
                <span>{{ donationInfo.isAnonymous === 'yes' ? '匿名用户' : (donationInfo.username || '未知用户') }}</span>
              </div>
              <div class="info-item">
                <label>受助项目</label>
                <span class="project-link" @click="viewProject">
                  {{ donationInfo.projectTitle || '未知项目' }}
                </span>
              </div>
              <div class="info-item">
                <label>支付方式</label>
                <el-tag :type="getPaymentType(donationInfo.paymentMethod)" size="small">
                  {{ getPaymentText(donationInfo.paymentMethod) }}
                </el-tag>
              </div>
              <div class="info-item">
                <label>交易流水号</label>
                <span v-if="donationInfo.transactionId" class="transaction-id">
                  {{ donationInfo.transactionId }}
                </span>
                <span v-else class="no-data">-</span>
              </div>
              <div class="info-item">
                <label>捐赠时间</label>
                <span>{{ formatTime(donationInfo.donationTime) }}</span>
              </div>
              <div class="info-item">
                <label>支付时间</label>
                <span>{{ formatTime(donationInfo.paymentTime) }}</span>
              </div>
              <div class="info-item">
                <label>是否匿名</label>
                <el-tag :type="donationInfo.isAnonymous === 'yes' ? 'warning' : 'success'" size="small">
                  {{ donationInfo.isAnonymous === 'yes' ? '匿名捐赠' : '实名捐赠' }}
                </el-tag>
              </div>
            </div>

            <!-- 捐赠留言 -->
            <div v-if="donationInfo.message" class="message-section">
              <h3>捐赠留言</h3>
              <div class="message-content">
                {{ donationInfo.message }}
              </div>
            </div>

            <!-- 退款信息 -->
            <div v-if="donationInfo.status === 'refunded'" class="refund-section">
              <h3>退款信息</h3>
              <div class="refund-content">
                <div class="refund-item">
                  <label>退款原因：</label>
                  <span>{{ donationInfo.refundReason || '未填写' }}</span>
                </div>
                <div class="refund-item">
                  <label>退款时间：</label>
                  <span>{{ formatTime(donationInfo.refundTime) }}</span>
                </div>
                <div class="refund-item">
                  <label>处理人员：</label>
                  <span>{{ donationInfo.refundAdminName || '未知' }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 操作区域 -->
      <div v-if="donationInfo.status === 'completed'" class="actions-section">
        <el-card>
          <template #header>
            <span>管理操作</span>
          </template>

          <div class="action-buttons">
            <el-button type="warning" @click="handleRefund">
              <el-icon>
                <RefreshLeft />
              </el-icon>
              办理退款
            </el-button>

            <el-button @click="handleViewDonor" :disabled="donationInfo.isAnonymous === 'yes'">
              <el-icon>
                <User />
              </el-icon>
              查看捐赠人
            </el-button>

            <el-button @click="handleViewProject">
              <el-icon>
                <Document />
              </el-icon>
              查看项目
            </el-button>

            <el-button @click="handleExportReceipt">
              <el-icon>
                <Download />
              </el-icon>
              导出凭证
            </el-button>
          </div>
        </el-card>
      </div>

      <!-- 相关信息 -->
      <div class="related-section">
        <el-card>
          <template #header>
            <span>相关信息</span>
          </template>

          <div class="related-content">
            <div class="related-item">
              <h4>项目进展</h4>
              <p>{{ donationInfo.projectDescription || '暂无项目描述' }}</p>
              <div class="project-progress">
                <span>筹款进度：</span>
                <el-progress :percentage="calculateProgress(donationInfo.projectRaised, donationInfo.projectTarget)"
                  :stroke-width="8" />
                <span class="progress-text">
                  已筹集 ¥{{ formatAmount(donationInfo.projectRaised) }} / 目标 ¥{{ formatAmount(donationInfo.projectTarget)
                  }}
                </span>
              </div>
            </div>

            <div v-if="donationInfo.isAnonymous !== 'yes'" class="related-item">
              <h4>捐赠人信息</h4>
              <div class="donor-info">
                <div class="donor-item">
                  <label>用户名：</label>
                  <span>{{ donationInfo.username || '未知' }}</span>
                </div>
                <div class="donor-item">
                  <label>邮箱：</label>
                  <span>{{ donationInfo.userEmail || '未设置' }}</span>
                </div>
                <div class="donor-item">
                  <label>注册时间：</label>
                  <span>{{ formatTime(donationInfo.userCreateTime) }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 退款对话框 -->
    <el-dialog v-model="refundDialogVisible" title="办理退款" width="500px" center>
      <el-form :model="refundForm" label-width="100px">
        <el-form-item label="退款金额" required>
          <el-input v-model="refundForm.amount" disabled suffix-icon="el-icon-money">
            <template #prepend>¥</template>
          </el-input>
        </el-form-item>

        <el-form-item label="退款原因" required>
          <el-input v-model="refundForm.reason" type="textarea" :rows="4" placeholder="请输入退款原因..." maxlength="200"
            show-word-limit />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="refundDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitRefund" :loading="refundSubmitting"
            :disabled="!refundForm.reason">
            确认退款
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    ArrowLeft,
    Refresh,
    RefreshLeft,
    User,
    Document,
    Download
  } from '@element-plus/icons-vue'
  import {
    getDonationById,
    refundDonation
  } from '@/api/admin'

  const route = useRoute()
  const router = useRouter()

  // 响应式数据
  const loading = ref(false)
  const donationInfo = ref({})
  const refundDialogVisible = ref(false)
  const refundSubmitting = ref(false)

  // 退款表单
  const refundForm = reactive({
    amount: '',
    reason: ''
  })

  // 获取捐赠ID
  const donationId = route.params.id

  // 加载捐赠详情
  const loadDonationDetail = async () => {
    try {
      loading.value = true

      const response = await getDonationById(donationId)
      if (response) {
        donationInfo.value = response
        refundForm.amount = response.amount
      }

    } catch (error) {
      console.error('加载捐赠详情失败:', error)
      ElMessage.error('加载捐赠详情失败')
    } finally {
      loading.value = false
    }
  }

  // 返回捐赠列表
  const goBack = () => {
    router.push('/admin/donations')
  }

  // 刷新
  const handleRefresh = () => {
    loadDonationDetail()
  }

  // 办理退款
  const handleRefund = () => {
    refundForm.reason = ''
    refundDialogVisible.value = true
  }

  // 提交退款
  const handleSubmitRefund = async () => {
    if (!refundForm.reason) {
      ElMessage.warning('请填写退款原因')
      return
    }

    try {
      refundSubmitting.value = true

      await refundDonation(donationInfo.value.id, refundForm.reason)
      ElMessage.success('退款处理成功')
      refundDialogVisible.value = false
      loadDonationDetail() // 重新加载数据

    } catch (error) {
      console.error('提交退款失败:', error)
      if (error.message) {
        ElMessage.error(error.message)
      } else {
        ElMessage.error('退款处理失败')
      }
    } finally {
      refundSubmitting.value = false
    }
  }

  // 查看捐赠人
  const handleViewDonor = () => {
    if (donationInfo.value.userId && donationInfo.value.isAnonymous !== 'yes') {
      router.push(`/admin/users/${donationInfo.value.userId}`)
    } else {
      ElMessage.warning('匿名用户无法查看详情')
    }
  }

  // 查看项目
  const handleViewProject = () => {
    console.log('查看项目:', donationInfo.value.projectId)
    if (donationInfo.value.projectId) {
      console.log('跳转到项目详情:', `/admin/projects/${donationInfo.value.projectId}`)
      router.push(`/admin/projects/${donationInfo.value.projectId}`)
    } else {
      ElMessage.warning('项目信息不存在')
    }
  }

  const viewProject = () => {
    handleViewProject()
  }

  // 导出凭证
  const handleExportReceipt = () => {
    ElMessage.info('导出凭证功能开发中...')
  }

  // 计算进度百分比
  const calculateProgress = (raised, target) => {
    if (!raised || !target) return 0
    return Math.min(Math.round((raised / target) * 100), 100)
  }

  // 工具函数
  const getPaymentType = (paymentMethod) => {
    const typeMap = {
      alipay: 'primary',
      wechat: 'success',
      bank: 'warning'
    }
    return typeMap[paymentMethod] || ''
  }

  const getPaymentText = (paymentMethod) => {
    const textMap = {
      alipay: '支付宝',
      wechat: '微信支付',
      bank: '银行转账'
    }
    return textMap[paymentMethod] || paymentMethod
  }

  const getStatusType = (status) => {
    const typeMap = {
      pending: 'warning',
      completed: 'success',
      cancelled: 'info',
      refunded: 'danger'
    }
    return typeMap[status] || ''
  }

  const getStatusText = (status) => {
    const textMap = {
      pending: '待支付',
      completed: '已完成',
      cancelled: '已取消',
      refunded: '已退款'
    }
    return textMap[status] || status
  }

  const formatTime = (timeString) => {
    if (!timeString) return '未知'
    return new Date(timeString).toLocaleString('zh-CN')
  }

  const formatAmount = (amount) => {
    if (!amount) return '0'
    return Number(amount).toLocaleString()
  }

  // 页面初始化
  onMounted(() => {
    loadDonationDetail()
  })
</script>

<style scoped>
  .donation-detail {
    padding: 24px;
    background: #f5f5f5;
    min-height: 100vh;
  }

  /* 页面头部 */
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .back-btn {
    color: #718096;
  }

  .title-section {
    flex: 1;
  }

  .page-title {
    font-size: 24px;
    font-weight: bold;
    color: #2D3748;
    margin: 0 0 8px 0;
  }

  .page-description {
    color: #718096;
    margin: 0;
    font-size: 14px;
  }

  /* 内容区域 */
  .content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  /* 信息区域 */
  .info-section .el-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .donation-profile {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  /* 金额展示 */
  .amount-section {
    text-align: center;
    padding: 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
  }

  .amount-display {
    font-size: 48px;
    font-weight: bold;
    margin-bottom: 8px;
  }

  .currency {
    font-size: 32px;
    opacity: 0.8;
  }

  .amount {
    margin-left: 4px;
  }

  .amount-label {
    font-size: 16px;
    opacity: 0.9;
  }

  /* 信息网格 */
  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .info-item label {
    font-size: 12px;
    color: #718096;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .info-item span {
    font-size: 14px;
    color: #2D3748;
    font-weight: 500;
  }

  .project-link {
    color: #4299E1 !important;
    cursor: pointer;
    text-decoration: underline;
  }

  .project-link:hover {
    color: #3182CE !important;
  }

  .transaction-id {
    font-family: monospace;
    background: #F7FAFC;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #E2E8F0;
  }

  .no-data {
    color: #A0AEC0;
    font-style: italic;
  }

  /* 留言区域 */
  .message-section h3,
  .refund-section h3 {
    font-size: 16px;
    font-weight: bold;
    color: #2D3748;
    margin: 0 0 12px 0;
  }

  .message-content {
    background: #F7FAFC;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #4299E1;
    line-height: 1.6;
    color: #2D3748;
  }

  /* 退款信息 */
  .refund-content {
    background: #FED7D7;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #E53E3E;
  }

  .refund-item {
    display: flex;
    margin-bottom: 8px;
  }

  .refund-item:last-child {
    margin-bottom: 0;
  }

  .refund-item label {
    font-weight: 500;
    color: #2D3748;
    min-width: 80px;
  }

  .refund-item span {
    color: #4A5568;
  }

  /* 操作区域 */
  .actions-section .el-card,
  .related-section .el-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .action-buttons {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
  }

  .action-buttons .el-button {
    flex: 1;
    min-width: 140px;
  }

  /* 相关信息 */
  .related-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .related-item h4 {
    font-size: 14px;
    font-weight: bold;
    color: #2D3748;
    margin: 0 0 12px 0;
  }

  .related-item p {
    color: #4A5568;
    line-height: 1.6;
    margin: 0 0 16px 0;
  }

  .project-progress {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .progress-text {
    font-size: 12px;
    color: #718096;
  }

  .donor-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .donor-item {
    display: flex;
  }

  .donor-item label {
    font-weight: 500;
    color: #2D3748;
    min-width: 80px;
  }

  .donor-item span {
    color: #4A5568;
  }

  /* 对话框 */
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .donation-detail {
      padding: 16px;
    }

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .header-left {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    .amount-display {
      font-size: 36px;
    }

    .currency {
      font-size: 24px;
    }

    .info-grid {
      grid-template-columns: 1fr;
    }

    .action-buttons {
      flex-direction: column;
    }

    .action-buttons .el-button {
      flex: none;
      width: 100%;
    }

    .related-content {
      gap: 16px;
    }
  }
</style>