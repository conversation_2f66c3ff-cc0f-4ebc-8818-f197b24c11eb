@use './variables.scss' as *;

// 全局样式重置和基础样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  background-color: $bg-secondary;
  color: $text-primary;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 容器样式
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 $spacing-lg;
  
  @media (max-width: $breakpoint-md) {
    padding: 0 $spacing-md;
  }
}

// 卡片样式
.card {
  background: $bg-primary;
  border-radius: $radius-lg;
  box-shadow: $shadow-md;
  padding: $spacing-xl;
  margin-bottom: $spacing-lg;
  border: 1px solid $border-light;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: $shadow-lg;
    transform: translateY(-2px);
  }
}

// 按钮样式覆盖Element Plus
.el-button {
  &--primary {
    background-color: $primary-red !important;
    border-color: $primary-red !important;
    
    &:hover {
      background-color: $primary-red-dark !important;
      border-color: $primary-red-dark !important;
    }
    
    &:active {
      background-color: $primary-red-dark !important;
      border-color: $primary-red-dark !important;
    }
  }
  
  &--default {
    color: $text-primary;
    border-color: $border-medium;
    
    &:hover {
      color: $primary-red;
      border-color: $primary-red;
    }
  }
}

// 链接样式
.el-link {
  &--primary {
    color: $primary-red !important;
    
    &:hover {
      color: $primary-red-dark !important;
    }
  }
}

// 进度条样式
.el-progress {
  .el-progress-bar__outer {
    background-color: $primary-red-lighter;
  }
  
  .el-progress-bar__inner {
    background-color: $primary-red;
  }
}

// 标签样式
.el-tag {
  &--success {
    background-color: rgba($success, 0.1);
    border-color: $success;
    color: $success;
  }
  
  &--warning {
    background-color: rgba($warning, 0.1);
    border-color: $warning;
    color: $warning;
  }
  
  &--danger {
    background-color: rgba($error, 0.1);
    border-color: $error;
    color: $error;
  }
}

// 菜单样式
.el-menu {
  &--horizontal {
    border-bottom: none !important;
    
    .el-menu-item {
      color: $text-primary;
      
      &:hover {
        color: $primary-red;
        background-color: transparent;
      }
      
      &.is-active {
        color: $primary-red;
        border-bottom-color: $primary-red;
      }
    }
  }
}

// 输入框样式
.el-input {
  .el-input__wrapper {
    &:hover {
      box-shadow: 0 0 0 1px $primary-red-light;
    }
    
    &.is-focus {
      box-shadow: 0 0 0 1px $primary-red;
    }
  }
}

// 表单样式
.el-form-item {
  .el-form-item__label {
    color: $text-primary;
    font-weight: 500;
  }
}

// 工具类
.text-primary { color: $text-primary; }
.text-secondary { color: $text-secondary; }
.text-light { color: $text-light; }
.text-white { color: $text-white; }
.text-red { color: $primary-red; }

.bg-primary { background-color: $bg-primary; }
.bg-secondary { background-color: $bg-secondary; }
.bg-accent { background-color: $bg-accent; }
.bg-red { background-color: $primary-red; }
.bg-red-light { background-color: $primary-red-light; }

.border-light { border-color: $border-light; }
.border-medium { border-color: $border-medium; }
.border-red { border-color: $primary-red; }

.shadow-sm { box-shadow: $shadow-sm; }
.shadow-md { box-shadow: $shadow-md; }
.shadow-lg { box-shadow: $shadow-lg; }

.rounded-sm { border-radius: $radius-sm; }
.rounded-md { border-radius: $radius-md; }
.rounded-lg { border-radius: $radius-lg; }
.rounded-xl { border-radius: $radius-xl; }

// 响应式工具类
@media (max-width: $breakpoint-md) {
  .hidden-mobile { display: none !important; }
  .container { padding: 0 $spacing-md; }
}

@media (min-width: $breakpoint-md) {
  .hidden-desktop { display: none !important; }
}
