package com.example.demo.dto.request;

import com.example.demo.dto.PageRequestDTO;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 救助项目查询请求DTO
 */
@Schema(description = "救助项目查询请求")
public class ProjectQueryDTO extends PageRequestDTO {

    @Schema(description = "项目分类", example = "medical")
    private String category;

    @Schema(description = "项目状态", example = "active")
    private String status;

    @Schema(description = "管理员ID", example = "1")
    private Long adminId;

    // Getters and Setters
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getAdminId() {
        return adminId;
    }

    public void setAdminId(Long adminId) {
        this.adminId = adminId;
    }
}
