# 🌟 爱心救助平台系统设计文档（图片版）

> 基于 Spring Boot 3 + Vue 3 + AI 技术的现代化爱心救助平台系统设计

---

## 📋 图表说明

本文档包含了系统设计的所有 Mermaid 图表。由于 Markdown 在某些环境中不能直接显示 Mermaid 图表，我们提供以下解决方案：

### 🔧 查看图表的方法

1. **在线查看**：
   - 将文档上传到 GitHub/GitLab（原生支持 Mermaid）
   - 使用 Mermaid Live Editor: https://mermaid.live/

2. **本地查看**：
   - 使用 Typora 编辑器
   - VS Code + Mermaid Preview 插件
   - Obsidian + Mermaid 插件

3. **图片导出**：
   - 使用在线工具将 Mermaid 代码转换为 PNG/SVG
   - 替换文档中的代码块为图片链接

---

## 📊 核心图表列表

### 1. 系统整体架构图
**位置**：第2.1节
**说明**：展示完整的系统分层架构，从用户层到数据层的完整技术栈

```mermaid
graph TB
    subgraph "用户层 (User Layer)"
        U1[普通用户]
        U2[管理员]
        U3[移动端用户]
    end
    
    subgraph "表现层 (Presentation Layer)"
        F1[Vue 3 前端应用]
        F2[管理后台]
        F3[移动端界面]
    end
    
    subgraph "网关层 (Gateway Layer)"
        G1[Nginx 反向代理]
        G2[负载均衡]
        G3[SSL 终端]
    end
    
    subgraph "应用层 (Application Layer)"
        A1[Spring Boot 应用]
        A2[RESTful API]
        A3[JWT 认证]
    end
    
    subgraph "业务层 (Business Layer)"
        B1[用户管理服务]
        B2[项目管理服务]
        B3[捐赠管理服务]
        B4[AI 对话服务]
        B5[申请管理服务]
    end
    
    subgraph "数据层 (Data Layer)"
        D1[MySQL 数据库]
        D2[Redis 缓存]
        D3[文件存储]
    end
    
    subgraph "外部服务 (External Services)"
        E1[火山引擎豆包 AI]
    end

    U1 --> F1
    U2 --> F2
    U3 --> F3

    F1 --> G1
    F2 --> G1
    F3 --> G1

    G1 --> A1
    A1 --> B1
    A1 --> B2
    A1 --> B3
    A1 --> B4
    A1 --> B5

    B1 --> D1
    B2 --> D1
    B3 --> D1
    B4 --> D1
    B5 --> D1

    A1 --> D2
    A1 --> D3

    B4 --> E1
```

### 2. 数据库 ER 关系图
**位置**：第3.2节
**说明**：展示核心业务实体及其关系

```mermaid
erDiagram
    USERS {
        bigint id PK
        varchar username UK
        varchar password
        varchar email UK
        varchar phone
        varchar real_name
        varchar avatar
        enum role
        tinyint status
        timestamp create_time
        timestamp update_time
    }
    
    PROJECTS {
        bigint id PK
        varchar title
        text description
        enum category
        decimal target_amount
        decimal current_amount
        text images
        varchar contact_info
        enum status
        bigint admin_id FK
        timestamp create_time
        timestamp update_time
    }
    
    DONATIONS {
        bigint id PK
        bigint user_id FK
        bigint project_id FK
        decimal amount
        enum payment_method
        varchar transaction_id
        varchar message
        tinyint is_anonymous
        enum status
        timestamp donation_time
    }
    
    HELP_REQUESTS {
        bigint id PK
        bigint user_id FK
        varchar title
        text description
        enum category
        decimal amount_needed
        varchar contact_phone
        varchar contact_address
        text proof_images
        enum status
        text admin_response
        bigint admin_id FK
        timestamp create_time
        timestamp update_time
    }
    
    CONVERSATIONS {
        bigint id PK
        bigint user_id FK
        varchar session_id
        text message
        text response
        enum conversation_type
        timestamp create_time
    }
    
    USERS ||--o{ PROJECTS : "admin_id"
    USERS ||--o{ DONATIONS : "user_id"
    USERS ||--o{ HELP_REQUESTS : "user_id"
    USERS ||--o{ HELP_REQUESTS : "admin_id"
    USERS ||--o{ CONVERSATIONS : "user_id"
    PROJECTS ||--o{ DONATIONS : "project_id"
```

### 3. 功能模块图
**位置**：第4.1节
**说明**：展示前后端功能模块的对应关系

```mermaid
graph TB
    subgraph "前端功能模块"
        F1[首页展示]
        F2[用户认证]
        F3[项目浏览]
        F4[在线捐赠]
        F5[救助申请]
        F6[AI智能助手]
        F7[个人中心]
        F8[管理后台]
    end
    
    subgraph "后端服务模块"
        B1[用户管理服务]
        B2[认证授权服务]
        B3[项目管理服务]
        B4[捐赠管理服务]
        B5[申请管理服务]
        B6[AI对话服务]
        B7[文件管理服务]
        B8[统计分析服务]
    end
    
    subgraph "数据存储模块"
        D1[用户数据]
        D2[项目数据]
        D3[捐赠数据]
        D4[申请数据]
        D5[对话数据]
        D6[文件数据]
    end
    
    F1 --> B1
    F1 --> B3
    F1 --> B8
    
    F2 --> B2
    F3 --> B3
    F4 --> B4
    F5 --> B5
    F6 --> B6
    F7 --> B1
    F8 --> B1
    F8 --> B3
    F8 --> B4
    F8 --> B5
    
    B1 --> D1
    B2 --> D1
    B3 --> D2
    B4 --> D3
    B5 --> D4
    B6 --> D5
    B7 --> D6
```

### 4. 用户注册登录流程图
**位置**：第4.3节
**说明**：详细的用户认证流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as 后端API
    participant DB as 数据库
    participant JWT as JWT服务
    
    Note over U,JWT: 用户注册流程
    U->>F: 填写注册信息
    F->>F: 前端验证
    F->>A: POST /api/auth/register
    A->>A: 数据验证
    A->>A: 密码加密(BCrypt)
    A->>DB: 保存用户信息
    DB-->>A: 返回用户ID
    A-->>F: 注册成功响应
    F-->>U: 显示注册成功
    
    Note over U,JWT: 用户登录流程
    U->>F: 输入用户名密码
    F->>A: POST /api/auth/login
    A->>DB: 查询用户信息
    DB-->>A: 返回用户数据
    A->>A: 验证密码
    A->>JWT: 生成JWT令牌
    JWT-->>A: 返回访问令牌
    A-->>F: 登录成功+令牌
    F->>F: 存储令牌到本地
    F-->>U: 跳转到首页
```

### 5. 救助申请审核流程图
**位置**：第4.3节
**说明**：完整的救助申请业务流程

```mermaid
sequenceDiagram
    participant U as 申请用户
    participant F as 前端
    participant A as 后端API
    participant DB as 数据库
    participant Admin as 管理员
    participant N as 通知服务
    
    Note over U,N: 申请提交阶段
    U->>F: 填写申请信息
    U->>F: 上传证明材料
    F->>A: POST /api/help-requests
    A->>A: 数据验证
    A->>DB: 保存申请信息
    DB-->>A: 返回申请ID
    A->>N: 发送通知给管理员
    A-->>F: 申请提交成功
    F-->>U: 显示申请编号
    
    Note over U,N: 管理员审核阶段
    Admin->>F: 查看待审核申请
    F->>A: GET /api/help-requests
    A->>DB: 查询申请列表
    DB-->>A: 返回申请数据
    A-->>F: 返回申请列表
    F-->>Admin: 显示申请详情
    
    Admin->>F: 审核决定(通过/拒绝)
    F->>A: PUT /api/help-requests/{id}/review
    A->>DB: 更新申请状态
    A->>N: 发送结果通知
    
    alt 申请通过
        A->>A: 创建救助项目
        A->>DB: 保存项目信息
        N->>U: 发送通过通知
    else 申请拒绝
        N->>U: 发送拒绝通知+原因
    end
```

---

## 🛠️ 使用建议

### 对于开发者
- 建议使用支持 Mermaid 的编辑器查看完整文档
- 可以将代码复制到 Mermaid Live Editor 中查看图表
- 在 GitHub/GitLab 上查看可以看到渲染后的图表

### 对于项目展示
- 建议将图表导出为 PNG/SVG 格式
- 插入到 PowerPoint 或其他演示文档中
- 可以使用截图工具保存图表图片

### 对于文档维护
- 保留 Mermaid 代码便于后续修改
- 同时维护图片版本用于不同场景
- 定期更新图表以反映系统变化

---

**注意**：完整的系统设计文档请参考 `SYSTEM_DESIGN.md`，本文档主要用于解决 Mermaid 图表显示问题。
