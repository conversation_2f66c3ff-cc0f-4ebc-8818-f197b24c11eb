package com.example.demo.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.example.demo.entity.User;
import com.example.demo.mapper.UserMapper;
import com.example.demo.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 测试控制器
 * 用于验证项目是否正常启动
 */
@Tag(name = "测试接口", description = "用于测试系统功能和查看项目信息的接口")
@RestController
@RequestMapping("/api/test")
public class TestController {

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private UserService userService;

    @Autowired
    private UserMapper userMapper;

    @Operation(summary = "Hello接口", description = "测试系统是否正常运行")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "请求成功")
    })
    @GetMapping("/hello")
    public Map<String, Object> hello() {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "🎉 爱心救助平台启动成功！");
        result.put("timestamp", LocalDateTime.now());
        result.put("status", "success");
        result.put("description", "这是一个基于Spring Boot 3的爱心救助平台");
        return result;
    }

    @Operation(summary = "健康检查", description = "检查系统运行状态和版本信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "系统运行正常")
    })
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("application", "charity-platform");
        result.put("version", "1.0.0");
        result.put("java_version", System.getProperty("java.version"));
        result.put("spring_boot_version", "3.2.1");
        return result;
    }

    @Operation(summary = "功能特性", description = "查看平台已完成和计划中的功能特性")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取功能列表成功")
    })
    @GetMapping("/features")
    public Map<String, Object> features() {
        Map<String, Object> result = new HashMap<>();
        result.put("platform_name", "温暖之家爱心救助平台");

        List<String> completedFeatures = new ArrayList<>();
        completedFeatures.add("✅ Spring Boot 3 项目搭建");
        completedFeatures.add("✅ RESTful API 基础框架");
        completedFeatures.add("✅ 项目结构规划");
        completedFeatures.add("✅ 数据库设计完成");

        List<String> plannedFeatures = new ArrayList<>();
        plannedFeatures.add("🔄 用户管理系统");
        plannedFeatures.add("🔄 救助项目管理");
        plannedFeatures.add("🔄 捐赠管理");
        plannedFeatures.add("🔄 AI智能对话");
        plannedFeatures.add("🔄 权限管理");

        result.put("completed", completedFeatures);
        result.put("planned", plannedFeatures);
        result.put("total_pages", "10+ 页面规划中");
        result.put("database_tables", "5个核心表已设计");

        return result;
    }

    @Operation(summary = "技术栈", description = "查看项目使用的技术栈信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取技术栈信息成功")
    })
    @GetMapping("/tech-stack")
    public Map<String, Object> techStack() {
        Map<String, Object> result = new HashMap<>();

        Map<String, String> backend = new HashMap<>();
        backend.put("framework", "Spring Boot 3.2.1");
        backend.put("java", "JDK 21");
        backend.put("database", "MySQL 8.0 (待集成)");
        backend.put("orm", "MyBatis Plus (待集成)");
        backend.put("security", "Spring Security + JWT (待集成)");

        Map<String, String> frontend = new HashMap<>();
        frontend.put("framework", "Vue 3 (计划中)");
        frontend.put("ui", "Element Plus (计划中)");
        frontend.put("build", "Vite (计划中)");

        result.put("backend", backend);
        result.put("frontend", frontend);
        result.put("special_features", "AI智能对话助手");

        return result;
    }

    @Operation(summary = "生成密码哈希", description = "生成BCrypt密码哈希值，用于测试")
    @GetMapping("/encode-password")
    public Map<String, Object> encodePassword(@RequestParam String password) {
        Map<String, Object> result = new HashMap<>();
        String encodedPassword = passwordEncoder.encode(password);
        result.put("originalPassword", password);
        result.put("encodedPassword", encodedPassword);
        result.put("timestamp", LocalDateTime.now());
        return result;
    }

    @Operation(summary = "创建管理员账号", description = "临时接口：创建管理员账号用于测试")
    @PostMapping("/create-admin")
    public Map<String, Object> createAdmin(
            @RequestParam String username,
            @RequestParam String password,
            @RequestParam(required = false) String email) {
        Map<String, Object> result = new HashMap<>();

        try {
            User admin = new User();
            admin.setUsername(username);
            admin.setPassword(password);
            admin.setEmail(email != null ? email : username + "@admin.com");
            admin.setRealName("管理员");
            admin.setRole(User.Role.ADMIN.getCode());
            admin.setStatus(User.Status.NORMAL.getCode());

            User createdAdmin = userService.register(admin);

            result.put("success", true);
            result.put("message", "管理员账号创建成功");
            result.put("username", createdAdmin.getUsername());
            result.put("role", createdAdmin.getRole());
            result.put("timestamp", LocalDateTime.now());

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "创建失败：" + e.getMessage());
            result.put("timestamp", LocalDateTime.now());
        }

        return result;
    }

    @Operation(summary = "更新用户密码哈希", description = "临时接口：直接更新用户密码哈希值")
    @PostMapping("/update-password-hash")
    public Map<String, Object> updatePasswordHash(
            @RequestParam String username,
            @RequestParam String passwordHash) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 查找用户
            User user = userMapper.findByUsername(username);
            if (user == null) {
                result.put("success", false);
                result.put("message", "用户不存在");
                return result;
            }

            // 直接更新密码哈希
            user.setPassword(passwordHash);
            int updated = userMapper.updateById(user);

            if (updated > 0) {
                result.put("success", true);
                result.put("message", "密码哈希更新成功");
                result.put("username", username);
            } else {
                result.put("success", false);
                result.put("message", "更新失败");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新失败：" + e.getMessage());
        }

        result.put("timestamp", LocalDateTime.now());
        return result;
    }
}
