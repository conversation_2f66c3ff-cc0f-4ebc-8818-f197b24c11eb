package com.example.demo.exception;

/**
 * 错误码枚举
 * 定义系统中所有可能的错误码和错误消息
 */
public enum ErrorCode {

    // 系统级错误 (1000-1999)
    SUCCESS(200, "操作成功"),
    SYSTEM_ERROR(1000, "系统内部错误"),
    NETWORK_ERROR(1001, "网络连接异常"),
    DATABASE_ERROR(1002, "数据库操作异常"),
    CACHE_ERROR(1003, "缓存操作异常"),
    FILE_UPLOAD_ERROR(1004, "文件上传失败"),
    FILE_DOWNLOAD_ERROR(1005, "文件下载失败"),
    FILE_TYPE_NOT_SUPPORTED(1006, "不支持的文件类型"),
    FILE_SIZE_EXCEEDED(1007, "文件大小超过限制"),
    FILE_NOT_FOUND(1008, "文件不存在"),
    FILE_DELETE_ERROR(1009, "文件删除失败"),

    // 请求参数错误 (2000-2999)
    PARAM_ERROR(2000, "请求参数错误"),
    PARAM_MISSING(2001, "缺少必要参数"),
    PARAM_TYPE_ERROR(2002, "参数类型错误"),
    PARAM_FORMAT_ERROR(2003, "参数格式错误"),
    PARAM_LENGTH_ERROR(2004, "参数长度错误"),
    PARAM_RANGE_ERROR(2005, "参数值超出范围"),
    JSON_PARSE_ERROR(2006, "JSON解析错误"),
    REQUEST_METHOD_ERROR(2007, "请求方法不支持"),
    INVALID_PARAMETER(2008, "无效的参数"),

    // 认证授权错误 (3000-3999)
    UNAUTHORIZED(3000, "未登录或登录已过期"),
    ACCESS_DENIED(3001, "权限不足"),
    TOKEN_INVALID(3002, "令牌无效"),
    TOKEN_EXPIRED(3003, "令牌已过期"),
    LOGIN_FAILED(3004, "用户名或密码错误"),
    ACCOUNT_DISABLED(3005, "账户已被禁用"),
    ACCOUNT_LOCKED(3006, "账户已被锁定"),
    PASSWORD_ERROR(3007, "密码错误"),
    CAPTCHA_ERROR(3008, "验证码错误"),

    // 用户相关错误 (4000-4999)
    USER_NOT_FOUND(4000, "用户不存在"),
    USER_ALREADY_EXISTS(4001, "用户已存在"),
    USERNAME_DUPLICATE(4002, "用户名已被使用"),
    EMAIL_DUPLICATE(4003, "邮箱已被使用"),
    PHONE_DUPLICATE(4004, "手机号已被使用"),
    USER_STATUS_ERROR(4005, "用户状态异常"),
    PASSWORD_TOO_WEAK(4006, "密码强度不足"),
    OLD_PASSWORD_ERROR(4007, "原密码错误"),

    // 项目相关错误 (5000-5999)
    PROJECT_NOT_FOUND(5000, "项目不存在"),
    PROJECT_STATUS_ERROR(5001, "项目状态异常"),
    PROJECT_PERMISSION_DENIED(5002, "无权限操作此项目"),
    PROJECT_AMOUNT_ERROR(5003, "项目金额设置错误"),
    PROJECT_CATEGORY_ERROR(5004, "项目分类错误"),
    PROJECT_ALREADY_COMPLETED(5005, "项目已完成"),
    PROJECT_ALREADY_CANCELLED(5006, "项目已取消"),
    PROJECT_NOT_ACTIVE(5007, "项目未激活"),

    // 捐赠相关错误 (6000-6999)
    DONATION_NOT_FOUND(6000, "捐赠记录不存在"),
    DONATION_AMOUNT_ERROR(6001, "捐赠金额错误"),
    DONATION_STATUS_ERROR(6002, "捐赠状态异常"),
    DONATION_PERMISSION_DENIED(6003, "无权限操作此捐赠"),
    PAYMENT_FAILED(6004, "支付失败"),
    PAYMENT_TIMEOUT(6005, "支付超时"),
    PAYMENT_CANCELLED(6006, "支付已取消"),
    REFUND_FAILED(6007, "退款失败"),
    DONATION_LIMIT_EXCEEDED(6008, "超出捐赠限额"),
    DONATION_NOT_ALLOWED(6009, "当前不允许捐赠"),

    // 救助申请相关错误 (7000-7999)
    HELP_REQUEST_NOT_FOUND(7000, "救助申请不存在"),
    HELP_REQUEST_STATUS_ERROR(7001, "申请状态异常"),
    HELP_REQUEST_PERMISSION_DENIED(7002, "无权限操作此申请"),
    HELP_REQUEST_LIMIT_EXCEEDED(7003, "申请数量超出限制"),
    HELP_REQUEST_CATEGORY_ERROR(7004, "申请分类错误"),
    HELP_REQUEST_AMOUNT_ERROR(7005, "申请金额错误"),
    HELP_REQUEST_DOCUMENTS_MISSING(7006, "缺少必要证明材料"),
    HELP_REQUEST_ALREADY_REVIEWED(7007, "申请已审核"),
    HELP_REQUEST_NOT_REVIEWABLE(7008, "申请不可审核"),

    // 对话相关错误 (8000-8999)
    CONVERSATION_NOT_FOUND(8000, "对话记录不存在"),
    SESSION_NOT_FOUND(8001, "会话不存在"),
    MESSAGE_TOO_LONG(8002, "消息内容过长"),
    CONVERSATION_TYPE_ERROR(8003, "对话类型错误"),
    AI_SERVICE_ERROR(8004, "AI服务异常"),
    CONVERSATION_LIMIT_EXCEEDED(8005, "对话次数超出限制"),

    // 业务逻辑错误 (9000-9999)
    BUSINESS_ERROR(9000, "业务处理异常"),
    DATA_NOT_FOUND(9001, "数据不存在"),
    DATA_ALREADY_EXISTS(9002, "数据已存在"),
    DATA_CONFLICT(9003, "数据冲突"),
    OPERATION_NOT_ALLOWED(9004, "操作不被允许"),
    STATUS_TRANSITION_ERROR(9005, "状态转换错误"),
    RESOURCE_EXHAUSTED(9006, "资源已耗尽"),
    RATE_LIMIT_EXCEEDED(9007, "请求频率超限"),
    MAINTENANCE_MODE(9008, "系统维护中"),
    FEATURE_DISABLED(9009, "功能已禁用");

    private final int code;
    private final String message;

    ErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 根据错误码获取ErrorCode枚举
     */
    public static ErrorCode getByCode(int code) {
        for (ErrorCode errorCode : ErrorCode.values()) {
            if (errorCode.getCode() == code) {
                return errorCode;
            }
        }
        return SYSTEM_ERROR;
    }

    /**
     * 判断是否为成功状态
     */
    public boolean isSuccess() {
        return this.code == SUCCESS.code;
    }

    /**
     * 判断是否为客户端错误（4xx）
     */
    public boolean isClientError() {
        return this.code >= 2000 && this.code < 5000;
    }

    /**
     * 判断是否为服务端错误（5xx）
     */
    public boolean isServerError() {
        return this.code >= 1000 && this.code < 2000;
    }

    @Override
    public String toString() {
        return String.format("ErrorCode{code=%d, message='%s'}", code, message);
    }
}
